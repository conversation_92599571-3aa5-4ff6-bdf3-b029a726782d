# Bitexco Ticket API - Enums Documentation

This document provides comprehensive documentation for all enumerations in the `Bitexco.Ticket.Domain.Enums` namespace.

## Table of Contents

1. [Agent Related Enums](#agent-related-enums)
2. [Audit & Logging Enums](#audit--logging-enums)
3. [Commission Related Enums](#commission-related-enums)
4. [Content Management Enums](#content-management-enums)
5. [Customer Related Enums](#customer-related-enums)
6. [Group Booking Enums](#group-booking-enums)
7. [Invoice Enums](#invoice-enums)
8. [Notification Enums](#notification-enums)
9. [Order Related Enums](#order-related-enums)
10. [Payment Related Enums](#payment-related-enums)
11. [POS Device Enums](#pos-device-enums)
12. [Price Related Enums](#price-related-enums)
13. [Promotion Related Enums](#promotion-related-enums)
14. [Refund Related Enums](#refund-related-enums)
15. [Role & Permission Enums](#role--permission-enums)
16. [Security Enums](#security-enums)
17. [Shift Management Enums](#shift-management-enums)
18. [Support Ticket Enums](#support-ticket-enums)
19. [Ticket Related Enums](#ticket-related-enums)
20. [Voucher Related Enums](#voucher-related-enums)

---

## Agent Related Enums

### AgentType
Defines the types of agents in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `collaborator` | Cộng tác viên (Collaborator) |
| 1 | `agent` | Đại lý (Agent) |

### AgentApproveStatus
Represents the approval status of agents.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `pending` | Approval is pending |
| 1 | `approved` | Agent has been approved |
| 2 | `suspended` | Agent has been suspended |

### AgentDebtStatus
Represents the current status of an agent debt.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `outstanding` | Debt is outstanding and unpaid |
| 2 | `paid` | Debt has been fully paid |

### AgentDebtPaymentMethod
Represents the payment method used for agent debt settlement.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `cash` | Cash payment |
| 1 | `bank_transfer` | Bank transfer payment |

---

## Audit & Logging Enums

### AuditLogActionType
Defines the types of actions that can be logged in the audit system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `create` | Create action |
| 1 | `read` | Read action |
| 2 | `update` | Update action |
| 3 | `delete` | Delete action |
| 4 | `login` | Login action |
| 5 | `logout` | Logout action |

---

## Commission Related Enums

### CommissionPaymentMethod
Defines the payment methods available for commission payments.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `bank_transfer` | Bank transfer payment method |
| 1 | `cash` | Cash payment method |

### CommissionPaymentStatus
Represents the status of commission payments.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `pending` | Payment is pending |
| 1 | `paid` | Payment has been completed |

### CommissionReportStatus
Defines the status of commission reports.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `generated` | Report is pending review |
| 1 | `approved` | Report has been approved |
| 2 | `rejected` | Report has been rejected |
| 3 | `paid` | Report has been paid out |

---

## Content Management Enums

### ContentPageStatus
Represents the status of content pages in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `draft` | The content page is in draft status, not yet published |
| 1 | `published` | The content page is published and visible to users |
| 2 | `archived` | The content page is archived and no longer active |
| 3 | `deleted` | The content page has been deleted |

---

## Customer Related Enums

### CustomerType
Defines the types of customers in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `individual` | Represents an individual customer |
| 1 | `corporate` | Represents a corporate customer |
| 2 | `ship` | Represents a customer related to shipping or maritime activities |

---

## Group Booking Enums

### GroupBookingType
Types of group bookings available in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `corporate` | Corporate group booking |
| 1 | `ship` | Ship group booking |

### GroupBookingStatus
Status of a group booking throughout its lifecycle.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `draft` | Booking is in draft status |
| 1 | `waiting_for_check_in` | Booking is waiting for check-in |
| 2 | `checked_in` | Group has checked in |
| 3 | `cancelled` | Booking has been cancelled |

---

## Invoice Enums

### InvoiceStatus
Represents the possible states of an invoice.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `issued` | Invoice has been issued |
| 1 | `cancelled` | Invoice has been cancelled |
| 2 | `revised` | Invoice has been revised |

### SendMailStatus
Represents the email delivery status for invoices.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `unsent` | Invoice has not been sent |
| 1 | `sent` | Invoice has been sent |
| 2 | `failed` | Invoice sending failed |

---

## Notification Enums

### NotificationType
Defines the available notification delivery channels.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `email` | Email notification |
| 1 | `sms` | SMS text message |
| 2 | `app` | In-app notification |
| 3 | `system` | System notification (internal) |

### NotificationTemplateType
Types of notification templates available.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `sms` | SMS notification template |
| 1 | `email` | Email notification template |

### NotificationStatus
Defines the possible notification delivery statuses.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `pending` | Awaiting sending |
| 1 | `sent` | Successfully sent |
| 2 | `failed` | Failed to send |
| 3 | `delivered` | Successfully delivered |

---

## Order Related Enums

### OrderStatus
Represents the status of orders in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `pending_payment` | Order is awaiting payment |
| 1 | `paid` | Order has been paid |
| 2 | `cancelled` | Order has been cancelled |
| 3 | `refunded` | Order has been refunded |

### OrderRefundStatus
Tracks the refund status of orders.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `none` | No refund requested |
| 1 | `pending_refund` | Refund request is pending |
| 2 | `refunded` | Refund has been processed |
| 3 | `rejected_refund` | Refund request has been rejected |

---

## Payment Related Enums

### PaymentTransactionMethod
Defines the available payment methods for transactions.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `cash` | Cash payment method |
| 1 | `pos` | POS payment method |
| 2 | `bank_transfer` | Bank transfer payment method |
| 3 | `debt` | Debt payment method |
| 4 | `vnpay` | VNPay payment method |
| 5 | `zalopay` | ZaloPay payment method |
| 6 | `momo` | MoMo payment method |

### PaymentTransactionStatus
Represents the status of payment transactions.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `pending` | Transaction is pending |
| 1 | `success` | Transaction was successful |
| 2 | `partial_success` | Transaction was partially successful |
| 3 | `failed` | Transaction failed |
| 4 | `refunded` | Transaction has been refunded |
| 5 | `partial_refunded` | Transaction has been partially refunded |

### PaymentTransactionType
Defines the direction of payment transactions.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `income` | Represents incoming payments (e.g., customer payments) |
| 1 | `expense` | Represents outgoing payments (e.g., refunds, payouts) |

### PaymentReconciliationStatus
Status of payment reconciliation processes.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `pending` | Reconciliation is pending |
| 1 | `completed` | Reconciliation is completed |

---

## POS Device Enums

### PosDeviceType
Types of Point of Sale devices in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `POS` | Point of Sale device |
| 1 | `Kiosk` | Kiosk device |

### PosDeviceStatus
Operational status of POS devices.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `online` | Device is online and operational |
| 1 | `offline` | Device is offline and not operational |
| 2 | `maintenance` | Device is under maintenance |
| 3 | `error` | Device is experiencing an error or malfunction |

### PosRoles
Defines the roles available for POS system users.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `technician` | Kỹ thuật (Technician) |
| 1 | `cashier` | Thu ngân (Cashier) |
| 2 | `manager` | Quản lý (Manager) |

---

## Price Related Enums

### PriceApprovalStatus
Represents the possible states in the price approval process.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `pending` | Price change is awaiting approval |
| 1 | `approved` | Price change has been approved |
| 2 | `rejected` | Price change has been rejected |
| 3 | `implemented` | Approved price change has been implemented |
| 4 | `cancelled` | Price change request was cancelled |

---

## Promotion Related Enums

### PromotionType
Types of promotions available in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `percentage` | A percentage discount on the total price |
| 1 | `fixed_amount` | A fixed amount discount on the total price |

### PromotionApplyType
Defines how promotions can be applied to different customer types.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `all` | Represents all customers, including both individual and corporate customers |
| 1 | `individual` | Represents an individual customer |
| 2 | `include_corporate` | Represents a corporate customer, including both individual and corporate customers |
| 3 | `exclude_corporate` | Represents a corporate customer, excluding individual customers |

---

## Refund Related Enums

### RefundPaymentMethod
Defines the payment methods available for refund transactions.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `cash` | Cash refund method |
| 2 | `bank_transfer` | Bank transfer refund method |

---

## Role & Permission Enums

### RoleType
Defines the main role types in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `pos` | Point of Sale role |
| 1 | `admin` | Admin role |

---

## Security Enums

### PreferredOtpMethod
Defines the preferred methods for OTP (One-Time Password) delivery.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `email` | Email as the preferred method for OTP |
| 1 | `sms` | SMS as the preferred method for OTP |

---

## Shift Management Enums

### ShiftStatus
Represents the status of work shifts.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `open` | Shift is currently open |
| 1 | `closed` | Shift has been closed |

---

## Support Ticket Enums

### SupportTicketStatus
Status of support tickets throughout their lifecycle.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `open` | Ticket is currently open and awaiting response |
| 1 | `in_progress` | Ticket is being actively worked on |
| 2 | `resolved` | Ticket has been resolved |
| 3 | `closed` | Ticket has been closed and no further action is needed |

### SupportTicketPriority
Priority levels for support tickets.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `low` | Low priority ticket, can be addressed later |
| 1 | `medium` | Medium priority ticket, should be addressed in a reasonable timeframe |
| 2 | `high` | High priority ticket, needs immediate attention |
| 3 | `urgent` | Urgent ticket, requires immediate action |

### SupportTicketMessageSenderType
Types of senders for support ticket messages.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `customer` | Message sent by the customer |
| 1 | `user` | Message sent by a user (e.g., a team member) |
| 2 | `agent` | Message sent by a support agent |
| 3 | `admin` | Message sent by an admin |
| 4 | `system` | Message sent by the system (e.g., automated responses) |

---

## Ticket Related Enums

### TicketStatus
Represents the lifecycle status of tickets.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `created` | The ticket has been created but not yet sold |
| 1 | `sold` | The ticket has been sold to a customer |
| 2 | `used` | The ticket has been used by the customer |
| 3 | `cancelled` | The ticket has been cancelled by the customer or system |
| 4 | `refunded` | The ticket has been refunded to the customer |
| 5 | `expired` | The ticket has expired and can no longer be used |

### TicketExpirationDurationUnit
Units for measuring ticket expiration duration.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `days` | Duration in days |
| 1 | `months` | Duration in months |
| 2 | `years` | Duration in years |

### TicketTypeConditionType
Types of conditions that can be applied to ticket types.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `TimeRange` | Condition based on a time range |
| 1 | `Age` | Condition based on age |
| 2 | `GroupSize` | Condition based on group size |

---

## Voucher Related Enums

### VoucherType
Types of vouchers available in the system.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `percentage` | A percentage discount on the total price |
| 1 | `fixed_amount` | A fixed amount discount on the total price |

### VoucherApplyType
Defines how vouchers can be applied to different customer types.

| Value | Name | Description |
|-------|------|-------------|
| 0 | `all` | Represents all customers, including both individual and corporate customers |
| 1 | `individual` | Represents an individual customer |
| 2 | `include_corporate` | Represents a corporate customer, including both individual and corporate customers |
| 3 | `exclude_corporate` | Represents a corporate customer, excluding individual customers |

---

## Usage Notes

- All enums are defined in the `Bitexco.Ticket.Domain.Enums` namespace
- Enum values are zero-indexed by default
- Some enums include XML documentation comments for better IntelliSense support
- Comments in the source code provide additional context for each enum value

---

*Generated on: June 28, 2025*
*Total Enums Documented: 41*
