# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER root
WORKDIR /app
EXPOSE 8080
EXPOSE 8081


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["../Services/Bitexco.Ticket.API/Bitexco.Ticket.API.csproj", "../Services/Bitexco.Ticket.API/"]
COPY ["../Services/Bitexco.Ticket.Application/Bitexco.Ticket.Application.csproj", "../Services/Bitexco.Ticket.Application/"]
COPY ["../BuildingBlocks/BuildingBlocks.Messaging/BuildingBlocks.Messaging.csproj", "../BuildingBlocks/BuildingBlocks.Messaging/"]
COPY ["../BuildingBlocks/BuildingBlocks/BuildingBlocks.csproj", "../BuildingBlocks/BuildingBlocks/"]
COPY ["../Services/Bitexco.Ticket.Domain/Bitexco.Ticket.Domain.csproj", "../Services/Bitexco.Ticket.Domain/"]
COPY ["../Services/Bitexco.Ticket.Infrastructure/Bitexco.Ticket.Infrastructure.csproj", "../Services/Bitexco.Ticket.Infrastructure/"]
RUN dotnet restore "../Services/Bitexco.Ticket.API/Bitexco.Ticket.API.csproj"
COPY . .
WORKDIR "/src/Services/Bitexco.Ticket.API"
RUN dotnet build "./Bitexco.Ticket.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Bitexco.Ticket.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Bitexco.Ticket.API.dll"]
