version: '3.8'

services:
  bitexco-ticket-api:
    build:
      context: .
      dockerfile: Dockerfile
    image: bitexco-ticket-api
    container_name: bitexco-ticket-api
    ports:
      - "8080:8080"
      - "8081:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - DISCORD_LOGGING_WEBHOOK_ID=1381463210460315831
      - DISCORD_LOGGING_WEBHOOK_TOKEN=gqw-MzqyidIqS-lBDOZITbQg116XCJblmgTuffD1uJXSAqdFQAf0_KcnnIX4ZNBmr7eu
      - DISCORD_LOGGING_WEBHOOK_URL=https://discord.com/api/webhooks/1381463210460315831/gqw-MzqyidIqS-lBDOZITbQg116XCJblmgTuffD1uJXSAqdFQAf0_KcnnIX4ZNBmr7eu
    volumes:
      - .:/src
    networks:
      - elk_npm_net

networks:
  elk_npm_net:
    external: true
