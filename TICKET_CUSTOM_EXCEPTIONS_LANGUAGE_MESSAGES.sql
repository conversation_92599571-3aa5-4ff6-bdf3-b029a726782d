-- SQL Script để insert language messages cho custom exceptions mới

-- CustomerNotFoundException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('CustomerNotFoundException', 'Customer not found'),
('CustomerNotFoundException_vi', 'Không tìm thấy khách hàng'),
('CustomerNotFoundException_id', 'Customer with ID {0} not found'),
('CustomerNotFoundException_id_vi', 'Không tìm thấy khách hàng với ID {0}');

-- OrderItemNotFoundException messages  
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('OrderItemNotFoundException', 'Order item not found'),
('OrderItemNotFoundException_vi', 'Không tìm thấy mục đơn hàng'),
('OrderItemNotFoundException_id', 'Order item with ID {0} not found'),
('OrderItemNotFoundException_id_vi', 'Không tìm thấy mục đơn hàng với ID {0}');

-- UserNotFoundException_id messages (thêm constructor với id parameter)
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('UserNotFoundException_id', 'User with ID {0} not found'),
('UserNotFoundException_id_vi', 'Không tìm thấy người dùng với ID {0}');
