-- SQL INSERT statements for Customer language messages
-- Execute these statements directly in the database

-- Customer exception messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('CustomerNotFoundException', 'Customer not found'),
('CustomerNotFoundException_vi', 'Không tìm thấy khách hàng'),
('CustomerNotFoundException_customerId', 'Customer with ID ''{0}'' not found'),
('CustomerNotFoundException_customerId_vi', 'Không tìm thấy khách hàng với ID ''{0}''');

-- Customer success messages (if needed in future)
-- INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
-- ('CustomerCreatedSuccessfully', 'Customer created successfully'),
-- ('CustomerCreatedSuccessfully_vi', 'Tạo khách hàng thành công'),
-- ('CustomerUpdatedSuccessfully', 'Customer updated successfully'),
-- ('CustomerUpdatedSuccessfully_vi', '<PERSON>ậ<PERSON> nhật khách hàng thành công'),
-- ('CustomerDeletedSuccessfully', 'Customer deleted successfully'),
-- ('CustomerDeletedSuccessfully_vi', 'Xóa khách hàng thành công');
