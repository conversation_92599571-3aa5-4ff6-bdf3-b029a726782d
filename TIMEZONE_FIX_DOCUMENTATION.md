# Timezone Handling Fix - GetRevenueGrowthChartQueryHandler

## 🐛 Vấn đề gặp phải

Khi gọi API `GetRevenueGrowthChart` sau khi implement timezone handling, gặp lỗi:

```
"message": "The conversion could not be completed because the supplied DateTime did not have the Kind property set correctly. For example, when the Kind property is DateTimeKind.Local, the source time zone must be TimeZoneInfo.Local. (Parameter 'sourceTimeZone')"
```

## 🔍 Nguyên nhân

Lỗi xảy ra trong method `ConvertLocalToUtc()` khi sử dụng `TimeZoneInfo.ConvertTimeToUtc(localTime, VietnamTimeZone)`. 

**Vấn đề cốt lõi**: `TimeZoneInfo.ConvertTimeToUtc()` yêu cầu `DateTime.Kind` phải được set chính xác:

- Nếu `Kind = DateTimeKind.Local` → `sourceTimeZone` phải là `TimeZoneInfo.Local`
- Nếu `Kind = DateTimeKind.Utc` → không cần `sourceTimeZone`  
- Nếu `Kind = DateTimeKind.Unspecified` → có thể sử dụng bất kỳ `TimeZone` nào

DateTime từ client thường có `Kind = DateTimeKind.Unspecified` hoặc `Local`, không phải Vietnam timezone.

## ✅ Giải pháp đã implement

### 1. Sửa method `ConvertLocalToUtc()`

**Trước khi sửa:**
```csharp
private static DateTime ConvertLocalToUtc(DateTime localTime)
{
    return TimeZoneInfo.ConvertTimeToUtc(localTime, VietnamTimeZone);
}
```

**Sau khi sửa:**
```csharp
private static DateTime ConvertLocalToUtc(DateTime localTime)
{
    // Ensure DateTime has Unspecified kind to work with custom timezone
    var unspecifiedTime = DateTime.SpecifyKind(localTime, DateTimeKind.Unspecified);
    return TimeZoneInfo.ConvertTimeToUtc(unspecifiedTime, VietnamTimeZone);
}
```

### 2. Sửa method `ConvertUtcToLocal()`

**Trước khi sửa:**
```csharp
private static DateTime ConvertUtcToLocal(DateTime utcTime)
{
    return TimeZoneInfo.ConvertTimeFromUtc(utcTime, VietnamTimeZone);
}
```

**Sau khi sửa:**
```csharp
private static DateTime ConvertUtcToLocal(DateTime utcTime)
{
    // Ensure DateTime has UTC kind for proper conversion
    var utcTimeSpecified = DateTime.SpecifyKind(utcTime, DateTimeKind.Utc);
    return TimeZoneInfo.ConvertTimeFromUtc(utcTimeSpecified, VietnamTimeZone);
}
```

### 3. Thêm method `ConvertUtcToLocalSafe()`

```csharp
private static DateTime? ConvertUtcToLocalSafe(DateTime? utcTime)
{
    if (!utcTime.HasValue)
        return null;
        
    return ConvertUtcToLocal(utcTime.Value);
}
```

### 4. Cập nhật các grouping methods

Sử dụng `ConvertUtcToLocalSafe()` thay vì trực tiếp gọi `ConvertUtcToLocal()` để xử lý nullable DateTime an toàn hơn:

```csharp
// GroupByHour
var hourOrders = orders.Where(o => 
{
    var localTime = ConvertUtcToLocalSafe(o.CreatedAt);
    return localTime.HasValue && localTime.Value.Hour == hour;
}).ToList();

// GroupByDay  
var dayOrders = orders.Where(o => 
{
    var localTime = ConvertUtcToLocalSafe(o.CreatedAt);
    return localTime.HasValue && localTime.Value.Date == currentDate;
}).ToList();

// GroupByMonth
var monthOrders = orders.Where(o => 
{
    var localTime = ConvertUtcToLocalSafe(o.CreatedAt);
    return localTime.HasValue && 
           localTime.Value.Year == currentMonth.Year && 
           localTime.Value.Month == currentMonth.Month;
}).ToList();
```

## 🧪 Testing

Đã tạo unit tests để verify fix:

1. **Test DateTimeKind.Unspecified**: Kiểm tra conversion với DateTime từ API
2. **Test DateTimeKind.Local**: Kiểm tra conversion với DateTime local
3. **Test nullable DateTime**: Kiểm tra xử lý an toàn với nullable values

## 📋 Checklist đã hoàn thành

- ✅ Sửa lỗi DateTimeKind trong `ConvertLocalToUtc()`
- ✅ Sửa lỗi DateTimeKind trong `ConvertUtcToLocal()`  
- ✅ Thêm method `ConvertUtcToLocalSafe()` cho nullable DateTime
- ✅ Cập nhật tất cả grouping methods sử dụng safe conversion
- ✅ Tạo unit tests để verify fix
- ✅ Đảm bảo backward compatibility
- ✅ Không thay đổi API request/response structure
- ✅ Duy trì logic timezone conversion Vietnam ↔ UTC

## 🎯 Kết quả

- ❌ **Trước**: API throw exception về DateTimeKind
- ✅ **Sau**: API hoạt động bình thường với mọi loại DateTime input
- ✅ Timezone conversion vẫn chính xác (Vietnam ↔ UTC)
- ✅ Grouping data theo local time như mong muốn
- ✅ Week calculation bắt đầu từ Monday

## 🔧 Cách sử dụng

API có thể nhận DateTime input với bất kỳ DateTimeKind nào:

```json
{
  "dateFrom": "2024-01-15T00:00:00",     // Unspecified
  "dateTo": "2024-01-15T23:59:59"        // Unspecified  
}
```

Hoặc:

```json
{
  "dateFrom": "2024-01-15T00:00:00+07:00", // With timezone
  "dateTo": "2024-01-15T23:59:59+07:00"    // With timezone
}
```

Tất cả đều được xử lý chính xác mà không gây lỗi DateTimeKind.
