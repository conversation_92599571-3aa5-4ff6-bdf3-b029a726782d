# ShiftConfig Implementation - String JSONB Approach

## 📋 Overview

ShiftConfig system đã được implement theo yêu cầu sếp với **string fields + jsonb database type** approach:

- **Database**: Fields có type `jsonb` cho performance và indexing
- **Entity**: Fields có type `string` cho flexibility  
- **Business Logic**: Convert string ⟷ objects khi cần trong code
- **DTOs**: Giữ `List<>` format cho UI convenience

## 🏗️ Architecture

### **1. Entity Layer (Storage Format)**
```csharp
public class ShiftConfig : Entity<Guid>
{
    public string WorkingDays { get; set; } = "[]";     // JSON string
    public string WorkingHours { get; set; } = "[]";   // JSON string
    
    // Helper methods
    public List<int> GetWorkingDaysAsList()
    public void SetWorkingDaysFromList(List<int> days)
    public List<WorkingHourSlot> GetWorkingHoursAsList() 
    public void SetWorkingHoursFromList(List<WorkingHourSlot> hours)
}
```

### **2. DTO Layer (UI Format)**
```csharp
public record ShiftConfigDto
{
    public List<int> WorkingDays { get; set; } = [];              // For UI
    public List<WorkingHourSlotDto> WorkingHours { get; set; } = []; // For UI
}
```

### **3. Mapping Layer**
```csharp
public static class ShiftConfigMappingHelper
{
    public static ShiftConfigResponseDto ToResponseDto(this ShiftConfig entity)
    public static void UpdateFromDto(this ShiftConfig entity, ShiftConfigDto dto)
    public static ShiftConfig ToEntity(this ShiftConfigDto dto)
}
```

## 📊 Database Schema

```sql
CREATE TABLE "ShiftConfigs" (
    "Id" uuid NOT NULL PRIMARY KEY,
    "Name" varchar(255) NOT NULL,
    "Description" text NULL,
    "AllowOpenEarly" boolean NOT NULL DEFAULT false,
    "IsActive" boolean NOT NULL DEFAULT true,
    "WorkingDays" jsonb NOT NULL DEFAULT '[]',    -- [1,2,3,4,5]
    "WorkingHours" jsonb NOT NULL DEFAULT '[]',   -- [{"startTime":"09:00","endTime":"12:30","order":1}]
    "IsDeleted" boolean NOT NULL DEFAULT false,
    "CreatedAt" timestamp NULL,
    "CreatedBy" varchar(255) NULL,
    "UpdatedAt" timestamp NULL,
    "UpdatedBy" varchar(255) NULL
);

-- JSONB Indexes for performance
CREATE INDEX "IX_ShiftConfigs_WorkingDays" ON "ShiftConfigs" USING gin ("WorkingDays");
CREATE INDEX "IX_ShiftConfigs_WorkingHours" ON "ShiftConfigs" USING gin ("WorkingHours");
```

## 💻 Usage Examples

### **Creating ShiftConfig**
```csharp
// In Command Handler
var dto = new ShiftConfigDto
{
    Name = "Ca Hành chính",
    Description = "Ca làm việc từ thứ 2 đến thứ 6",
    AllowOpenEarly = true,
    WorkingDays = [1, 2, 3, 4, 5], // Monday to Friday
    WorkingHours = [
        new WorkingHourSlotDto { StartTime = TimeOnly.Parse("09:00"), EndTime = TimeOnly.Parse("12:30"), Order = 1 },
        new WorkingHourSlotDto { StartTime = TimeOnly.Parse("13:30"), EndTime = TimeOnly.Parse("21:30"), Order = 2 }
    ]
};

var entity = dto.ToEntity();
context.ShiftConfigs.Add(entity);
await context.SaveChangesAsync();
```

### **Querying ShiftConfig**
```csharp
// In Query Handler
var entities = await context.ShiftConfigs
    .Where(sc => !sc.IsDeleted && sc.IsActive)
    .ToListAsync();

var dtos = entities.Select(e => e.ToResponseDto()).ToList();
```

### **Raw Database Values**
```json
// WorkingDays field value in database:
[1,2,3,4,5]

// WorkingHours field value in database:
[
  {"startTime":"09:00","endTime":"12:30","order":1},
  {"startTime":"13:30","endTime":"21:30","order":2}
]
```

## 🔍 PostgreSQL JSONB Queries

```sql
-- Find configs with Monday (1) in working days
SELECT * FROM "ShiftConfigs" 
WHERE "WorkingDays" @> '[1]';

-- Find configs with morning shifts (before 12:00)
SELECT * FROM "ShiftConfigs" 
WHERE "WorkingHours" @> '[{"startTime":"09:00"}]';

-- Query specific time range
SELECT * FROM "ShiftConfigs" 
WHERE "WorkingHours" @? '$[*] ? (@.startTime == "09:00")';
```

## ✅ Benefits

1. **Storage Efficiency**: JSONB type có compression và indexing tốt
2. **Query Flexibility**: PostgreSQL JSONB operators mạnh mẽ  
3. **UI Convenience**: DTOs với List<> dễ xử lý trong frontend
4. **Type Safety**: Helper methods đảm bảo valid JSON format
5. **Boss Compliance**: String fields như yêu cầu sếp

## 🚀 Next Implementation Steps

1. **CQRS Commands**: Create, Update, Delete ShiftConfig
2. **CQRS Queries**: Get ShiftConfigs, Get by Id
3. **FluentValidation**: Validate working days/hours format
4. **API Endpoints**: `/shift-configs` với snake-case
5. **Exception Messages**: Multilingual error handling

## 📝 Files Created/Modified

✅ **Domain Layer:**
- `ShiftConfig.cs` - Main entity với string fields + helper methods
- `WorkingHourSlot.cs` - Value object cho JSON structure  
- `ShiftConfigUser.cs` - Junction table cho user assignment
- `Shift.cs` - Updated với ShiftConfigId reference

✅ **Application Layer:**
- `ShiftConfigDto.cs` - DTOs với List<> fields for UI
- `ShiftConfigMappingHelper.cs` - Conversion utilities
- `ShiftDto.cs` - Updated với ShiftConfigId field

**Total:** 6 files, ~300 lines implemented
**Build Status:** ✅ All successful
**Convention Compliance:** ✅ Boss Rules followed
