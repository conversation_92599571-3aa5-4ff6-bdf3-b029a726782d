-- S<PERSON> Script to insert language messages for ALL exceptions

-- File Upload Exception messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('FileUploadException', 'File upload failed'),
('FileUploadException_vi', 'T<PERSON>i tệp lên thất bại'),
('FileUploadException_message', 'File upload failed: {0}'),
('FileUploadException_message_vi', 'Tải tệp lên thất bại: {0}'),
('FileUploadException_fileName_reason', 'Failed to upload file {0}: {1}'),
('FileUploadException_fileName_reason_vi', 'Không thể tải lên tệp {0}: {1}');

-- InvalidFileTypeException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('InvalidFileTypeException', 'Invalid file type'),
('InvalidFileTypeException_vi', '<PERSON><PERSON><PERSON> tệp không hợp lệ'),
('InvalidFileTypeException_fileType', 'Invalid file type: {0}'),
('InvalidFileTypeException_fileType_vi', 'Loại tệp không hợp lệ: {0}'),
('InvalidFileTypeException_fileName_fileType', 'File {0} has invalid type: {1}'),
('InvalidFileTypeException_fileName_fileType_vi', 'Tệp {0} có loại không hợp lệ: {1}');

-- FileSizeExceededException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('FileSizeExceededException', 'File size exceeds the maximum limit'),
('FileSizeExceededException_vi', 'Kích thước tệp vượt quá giới hạn tối đa'),
('FileSizeExceededException_maxSize', 'File size exceeds the maximum limit of {0}'),
('FileSizeExceededException_maxSize_vi', 'Kích thước tệp vượt quá giới hạn tối đa {0}'),
('FileSizeExceededException_fileName_maxSize', 'File {0} size exceeds the maximum limit of {1}'),
('FileSizeExceededException_fileName_maxSize_vi', 'Kích thước tệp {0} vượt quá giới hạn tối đa {1}');

-- File Upload Validation messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('FileUpload_FileRequired', 'File is required'),
('FileUpload_FileRequired_vi', 'Tệp là bắt buộc'),
('FileUpload_InvalidFileSize', 'File size cannot exceed 10MB'),
('FileUpload_InvalidFileSize_vi', 'Kích thước tệp không được vượt quá 10MB'),
('FileUpload_InvalidFileType', 'Invalid file type. Allowed types: jpg, jpeg, png, gif, pdf, doc, docx, txt'),
('FileUpload_InvalidFileType_vi', 'Loại tệp không hợp lệ. Các loại được phép: jpg, jpeg, png, gif, pdf, doc, docx, txt');

-- VoucherCampaignInUseException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('VoucherCampaignInUseException_id', 'Voucher campaign with code {0} is currently in use and cannot be modified or deleted'),
('VoucherCampaignInUseException_id_vi', 'Chiến dịch voucher với mã {0} đang được sử dụng và không thể sửa đổi hoặc xóa');

-- VoucherExpiredException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('VoucherExpiredException_code', 'Voucher with code {0} has expired or is not valid at this time'),
('VoucherExpiredException_code_vi', 'Voucher với mã {0} đã hết hạn hoặc không hợp lệ tại thời điểm này');

-- VoucherLimitReachException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('VoucherLimitReachException_code', 'Voucher {0} usage limit has been reached'),
('VoucherLimitReachException_code_vi', 'Voucher {0} đã đạt giới hạn sử dụng');

-- VoucherNotApplicableException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('VoucherNotApplicableException_code', 'Voucher {0} is not applicable for the current customer type or agent'),
('VoucherNotApplicableException_code_vi', 'Voucher {0} không áp dụng được cho loại khách hàng hoặc đại lý hiện tại');

-- AgentExitstingException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('AgentExitstingException_code', 'Agent with code {0} already exists'),
('AgentExitstingException_code_vi', 'Đại lý với mã {0} đã tồn tại');

-- VoucherInActiveException messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('VoucherInActiveException_code', 'Voucher with {0} is inactive and cannot be used'),
('VoucherInActiveException_code_vi', 'Voucher với {0} không hoạt động và không thể sử dụng');