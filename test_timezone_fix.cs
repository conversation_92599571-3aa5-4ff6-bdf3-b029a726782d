using System;

// Simple test to verify timezone conversion fix
public class TimezoneFixTest
{
    private static readonly TimeZoneInfo VietnamTimeZone = TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time");
    
    public static void TestDateTimeKindFix()
    {
        Console.WriteLine("Testing DateTime Kind Fix...");
        
        // Test 1: Unspecified Kind (common from API)
        var unspecifiedTime = new DateTime(2024, 1, 15, 9, 30, 0, DateTimeKind.Unspecified);
        Console.WriteLine($"Input (Unspecified): {unspecifiedTime} (Kind: {unspecifiedTime.Kind})");
        
        try
        {
            var utcResult = ConvertLocalToUtc(unspecifiedTime);
            Console.WriteLine($"Converted to UTC: {utcResult} (Kind: {utcResult.Kind})");
            Console.WriteLine("✅ Unspecified Kind conversion: SUCCESS");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Unspecified Kind conversion: FAILED - {ex.Message}");
        }
        
        // Test 2: Local Kind
        var localTime = new DateTime(2024, 1, 15, 9, 30, 0, DateTimeKind.Local);
        Console.WriteLine($"\nInput (Local): {localTime} (Kind: {localTime.Kind})");
        
        try
        {
            var utcResult = ConvertLocalToUtc(localTime);
            Console.WriteLine($"Converted to UTC: {utcResult} (Kind: {utcResult.Kind})");
            Console.WriteLine("✅ Local Kind conversion: SUCCESS");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Local Kind conversion: FAILED - {ex.Message}");
        }
        
        // Test 3: UTC to Local conversion
        var utcTime = new DateTime(2024, 1, 15, 2, 30, 0, DateTimeKind.Utc);
        Console.WriteLine($"\nInput (UTC): {utcTime} (Kind: {utcTime.Kind})");
        
        try
        {
            var localResult = ConvertUtcToLocal(utcTime);
            Console.WriteLine($"Converted to Local: {localResult} (Kind: {localResult.Kind})");
            Console.WriteLine("✅ UTC to Local conversion: SUCCESS");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ UTC to Local conversion: FAILED - {ex.Message}");
        }
        
        // Test 4: Nullable DateTime
        DateTime? nullableUtc = new DateTime(2024, 1, 15, 2, 30, 0, DateTimeKind.Utc);
        Console.WriteLine($"\nInput (Nullable UTC): {nullableUtc} (Kind: {nullableUtc?.Kind})");
        
        try
        {
            var localResult = ConvertUtcToLocalSafe(nullableUtc);
            Console.WriteLine($"Converted to Local: {localResult} (Kind: {localResult?.Kind})");
            Console.WriteLine("✅ Nullable UTC conversion: SUCCESS");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Nullable UTC conversion: FAILED - {ex.Message}");
        }
        
        Console.WriteLine("\n=== Test Summary ===");
        Console.WriteLine("All timezone conversion methods should work without DateTimeKind exceptions.");
    }
    
    private static DateTime ConvertLocalToUtc(DateTime localTime)
    {
        // Fixed version with SpecifyKind
        var unspecifiedTime = DateTime.SpecifyKind(localTime, DateTimeKind.Unspecified);
        return TimeZoneInfo.ConvertTimeToUtc(unspecifiedTime, VietnamTimeZone);
    }
    
    private static DateTime ConvertUtcToLocal(DateTime utcTime)
    {
        // Fixed version with SpecifyKind
        var utcTimeSpecified = DateTime.SpecifyKind(utcTime, DateTimeKind.Utc);
        return TimeZoneInfo.ConvertTimeFromUtc(utcTimeSpecified, VietnamTimeZone);
    }
    
    private static DateTime? ConvertUtcToLocalSafe(DateTime? utcTime)
    {
        if (!utcTime.HasValue)
            return null;
            
        return ConvertUtcToLocal(utcTime.Value);
    }
}

// Uncomment to run test
// TimezoneFixTest.TestDateTimeKindFix();
