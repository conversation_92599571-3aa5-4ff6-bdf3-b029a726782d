def VERSION_OLD = ""
def VERSION_NEW = ""
def IS_BUILD = false


pipeline {
    agent any

    tools {
        'org.jenkinsci.plugins.docker.commons.tools.DockerTool' 'docker'
    }

    environment {
        branch= 'main'
        bitexco='bitexco-ticket-api'
        version_file = "/var/jenkins_home/versions_bitexco_api/version.txt"
        version_docker_compose_old = "/var/jenkins_home/store_bitexco_api_docker_compose"
    }

    stages {
        stage('CLONE RESOURCE') {
            steps {
                git branch: "${env.branch}", credentialsId: "CREDENTIALS", url: "${env.gitBitexcoUrl}"
            }
        }

        stage('CHECK VERSION FILE') {
            steps {
                script {
                    def versions = getVersions(env.version_file)
                    VERSION_OLD = versions.oldVersion
                    VERSION_NEW = versions.newVersion
                }
            }
        }

        stage('DELETE DOCKER-COMPOSE NORMAL') {
            steps {
                script {
                    sh """ rm -rf docker-compose.yaml """
                }
            }
        }

        stage('STOP RESOURCE') {
            steps {
                script {
                    if (fileExists("${env.version_docker_compose_old}/docker-compose.yaml")) {
                        sh "cp ${env.version_docker_compose_old}/docker-compose.yaml ."

                        if(fileExists("docker-compose.yaml")) {
                            sh "cat docker-compose.yaml"

                            boolean status = runDockerComposeDownStatus();
                            if(status) {
                                sh """ rm -rf docker-compose.yaml """
                            }
                        }
                    }
                }
            }
        }

        stage('CREATE DOCKER-COMPOSE FILE') {
            steps {
                script {
                    if (!VERSION_OLD?.trim()) {
                        error "VERSION_OLD is empty or invalid!"
                    }
                    sh """
                        cat <<EOF > docker-compose.yaml

version: '3.8'

services:
  bitexco-ticket-api:
    build:
      context: .
      dockerfile: Dockerfile
    image: bitexco-ticket-api:${VERSION_NEW}
    container_name: bitexco-ticket-api-${VERSION_NEW}
    ports:
      - "8080:8080"
      - "8081:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - DISCORD_LOGGING_WEBHOOK_ID=1381463210460315831
      - DISCORD_LOGGING_WEBHOOK_TOKEN=gqw-MzqyidIqS-lBDOZITbQg116XCJblmgTuffD1uJXSAqdFQAf0_KcnnIX4ZNBmr7eu
      - DISCORD_LOGGING_WEBHOOK_URL=https://discord.com/api/webhooks/1381463210460315831/gqw-MzqyidIqS-lBDOZITbQg116XCJblmgTuffD1uJXSAqdFQAf0_KcnnIX4ZNBmr7eu
    volumes:
      - .:/src
    networks:
      - elk_npm_net

networks:
  elk_npm_net:
    external: true
    
EOF
                    """
                    sh "cat docker-compose.yaml"
                    sh "docker-compose config"
                    sh "ls"
                }
            }
        }

        stage('BUILD RESOURCE') {
            steps {
                script {
                    boolean buildSuccess = isBuildSuccessful()
                    IS_BUILD = buildSuccess
                }
            }
        }

        stage('CHECK STATUS BUILD RESOURCE') {
            steps {
                script {
                    def tags = getImageTagsByName(env.bitexco)
                    def versionTags = []

                    if (IS_BUILD) {

                        /* START CLEAR IMAGE OLD VERSION */
                        echo "✅ Docker-compose build completed successfully"
                        for (tag in tags) {
                            if(!tag.trim().isEmpty()) {
                                versionTags << "${tag}"
                            }
                        }

                        if(versionTags.size() >= 3) {
                            for(tag in versionTags) {
                                echo "${tag}"

                                def tagVersion = tag.toInteger()
                                def versionOld = VERSION_OLD.toInteger()

                                if(tagVersion < versionOld) {
                                    sh "docker image rm ${env.bitexco}:${tagVersion}"
                                }
                            }
                        }
                        /* END CLEAR IMAGE OLD VERSION */

                        /*START UPDATE NEW VERSION FILE */
                        def versionNumber = VERSION_NEW.toInteger() + 1
                        updateVersions(version_file, VERSION_NEW, versionNumber.toString())
                        /*END UPDATE NEW VERSION FILE */

                    } else {
                        def statusFile = fileExists("${env.version_docker_compose_old}/docker-compose.yaml")
                        echo "${statusFile}"

                        if (fileExists("${env.version_docker_compose_old}/docker-compose.yaml")) {
                            if(fileExists("docker-compose.yaml")) {
                                sh "rm -rf docker-compose.yaml"
                            }

                            sh "cp ${env.version_docker_compose_old}/docker-compose.yaml ."
                            boolean statusStartOldVersion = runDockerComposeStatus()
                            if(statusStartOldVersion) {
                                echo "Start old version successfully"

                            } else {
                                echo "Start old version unSuccessfully"
                            }
                        }

                        error "❌ Docker-compose build failed"
                        sh "ls ${env.version_docker_compose_old}"

                    }
                }
            }
        }

        stage('START RESOURCE SUCCESS') {
            steps {
                script {
                    echo "Check status ${IS_BUILD}"
                    if (IS_BUILD) {
                        sh "ls"
                        sh "cat docker-compose.yaml"
                        def status = runDockerComposeStatus()
                        if(status) {
                            if (fileExists("${env.version_docker_compose_old}/docker-compose.yaml")) {
                                sh "rm -rf ${env.version_docker_compose_old}/docker-compose.yaml"
                            }
                            sh "cp docker-compose.yaml ${env.version_docker_compose_old}"
                        }
                    }
                }
            }
        }
    }
}


Map getVersions(String filePath) {
    if (!fileExists(filePath)) {
        error "File ${filePath} does not exist!"
    }
    
    def fileContent = readFile filePath
    echo "File content:\n${fileContent}"
    
    if (!fileContent?.trim()) {
        error "File ${filePath} is empty!"
    }
    
    def oldVersion = fileContent.find(/old_version=(\d+)/)?.replaceAll(/old_version=/, '') ?: 'not found'
    def newVersion = fileContent.find(/new_version=(\d+)/)?.replaceAll(/new_version=/, '') ?: 'not found'
    
    if (oldVersion == 'not found' || newVersion == 'not found') {
        error "Failed to parse versions from ${filePath}. Check file format."
    }
    
    return [oldVersion: oldVersion, newVersion: newVersion]
}

void updateVersions(String filePath, String oldVersion, String newVersion) {
    if (!fileExists(filePath)) {
        error "File ${filePath} does not exist!"
    }
    
    def updatedContent = "old_version=${oldVersion}\nnew_version=${newVersion}\n"
    
    writeFile file: filePath, text: updatedContent
    echo "Updated file content:\n${updatedContent}"
    
    def updatedFileContent = readFile filePath
    echo "File content after update:\n${updatedFileContent}"
    
    sh "cat ${filePath}"
}

boolean isBuildSuccessful() {
    try {
        def status = sh(script: 'docker-compose build --no-cache', returnStatus: true)
        echo "Docker-compose build status: ${status == 0 ? 'Success' : 'Failed'}"
        return status == 0

    } catch (Exception e) {
        echo "Error running docker-compose: ${e.getMessage()}"
        return false

    }
}

List<String> getImageTagsByName(String imageName) {
    def output = sh(script: "docker images --format '{{.Tag}}' ${imageName}", returnStdout: true)
    echo "${output}"
    return output.split('\n').findAll { it.trim() }
}

/* FUNCTION RUN DOCKER-COMPOSE AND RESPONSE STATUS */
boolean runDockerComposeStatus() {
    try {
        sh 'docker-compose up -d'
        return true

    } catch (Exception e) {
        echo "Error running docker-compose: ${e.getMessage()}"
        return false

    }
}

/* FUNCTION STOP DOCKER-COMPOSE PROCESS */
boolean runDockerComposeDownStatus() {
    try {
        sh 'docker-compose down'
        return true

    } catch (Exception e) {
        echo "Error running docker-compose down: ${e.getMessage()}"
        return false

    }
}