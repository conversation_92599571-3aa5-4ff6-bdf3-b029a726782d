using Bitexco.Ticket.Application.Features.Invoices.GetInvoiceById;
using Bitexco.Ticket.Application.Features.Invoices.GetInvoices;

namespace Bitexco.Ticket.API.Endpoints;

public class InvoiceModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/invoices")
            .WithTags("Invoices")
            .WithSummary("Invoice Management")
            .WithDescription("Endpoints for managing invoices in the system");

        // GET /invoices - Get paginated invoices with optional filtering
        group.MapGet("", async ([AsParameters] GetInvoicesQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetInvoices")
        .Produces<PaginationResponse<GetInvoicesResponseDto>>(StatusCodes.Status200OK)
        .WithSummary("Get invoices")
        .WithDescription("Retrieves a paginated list of invoices with optional filtering by date range, customer, agent, and status")
        .WithMetadata(new LanguageClientAttribute());

        // GET /invoices/{invoiceId} - Get invoice details by ID
        group.MapGet("/{invoiceId:guid}", async (Guid invoiceId, ISender sender) =>
        {
            var query = new GetInvoiceByIdQuery(invoiceId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetInvoiceById")
        .Produces<Response<GetInvoiceByIdResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get invoice by ID")
        .WithDescription("Gets detailed information for a specific invoice by its ID")
        .WithMetadata(new LanguageClientAttribute());
    }
}
