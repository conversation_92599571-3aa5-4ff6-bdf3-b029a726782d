using Bitexco.Ticket.Application.Features.OrderRefunds.ApproveOrderRefundById;
using Bitexco.Ticket.Application.Features.OrderRefunds.GetOrderRefundById;
using Bitexco.Ticket.Application.Features.OrderRefunds.GetOrderRefunds;
using Bitexco.Ticket.Application.Features.OrderRefunds.RejectOrderRefundById;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.API.Endpoints;

public record ApproveOrderRefundByIdRequest
{
    public DateTime? RefundPaidAt { get; set; }
    public RefundPaymentMethod? RefundPaymentMethod { get; set; }
    public string? RefundPaymentProofFilePath { get; set; }
}

public record RejectOrderRefundRequest
{
    public string? RefundRejectionReason { get; set; }
}

public class RefundModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/order-refunds")
            .WithTags("Order Refunds")
            .WithSummary("Order Refund Management")
            .WithDescription("Endpoints for managing order refunds in the system");

        // GET /order-refunds - Get paginated refunds with optional filtering
        group.MapGet("", async ([AsParameters] GetOrderRefundsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetOrderRefunds")
        .Produces<PaginationResponse<GetOrderRefundsResponse>>(StatusCodes.Status200OK)
        .WithSummary("Get order refunds")
        .WithDescription("Retrieves a paginated list of order refunds with optional filtering by date range, customer, agent, and status")
        .WithMetadata(new LanguageClientAttribute());

        // GET /order-refunds/{orderId} - Get order details by ID
        group.MapGet("/{orderId:guid}", async (Guid orderId, ISender sender) =>
        {
            var query = new GetOrderRefundByIdQuery(orderId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetOrderRefundById")
        .Produces<Response<GetOrderRefundByIdResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get order refund by ID")
        .WithDescription("Gets detailed information for a specific order refund by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /order-refunds/{orderId}/approve - Approve an order refund
        group.MapPut("/{orderId:guid}/approve", async (Guid orderId, ApproveOrderRefundByIdRequest request, ISender sender) =>
        {
            var command = new ApproveOrderRefundByIdCommand
            {
                OrderId = orderId,
                RefundPaidAt = request.RefundPaidAt,
                RefundPaymentMethod = request.RefundPaymentMethod,
                RefundPaymentProofFilePath = request.RefundPaymentProofFilePath
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("ApproveOrderRefundById")
        .Accepts<ApproveOrderRefundByIdRequest>("application/json")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Approve order refund")
        .WithDescription("Approves a refund for an order with optional payment details")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /order-refunds/{orderId}/reject - Reject an order refund
        group.MapPut("/{orderId:guid}/reject", async (Guid orderId, RejectOrderRefundRequest request, ISender sender) =>
        {
            var command = new RejectOrderRefundCommand
            {
                OrderId = orderId,
                RefundRejectionReason = request.RefundRejectionReason
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("RejectOrderRefund")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Reject order refund")
        .WithDescription("Rejects a refund for an order")
        .WithMetadata(new LanguageClientAttribute());
    }
}
