using Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueOverview;
using Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueGrowthChart;
using Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueBySalesChannel;
using Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetTopRevenueByAgent;
using Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueByDevice;
using Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueByCustomerType;
using Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueByPaymentMethod;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for revenue report endpoints
/// </summary>
public class RevenueReportModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/revenue-reports")
            .WithTags("Revenue Reports")
            .WithSummary("Revenue Report Management")
            .WithDescription("Endpoints for revenue reporting and analysis");

        // GET /revenue-reports/overview - Get revenue overview with comparison to previous period
        group.MapGet("/overview", async ([AsParameters] GetRevenueOverviewQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetRevenueOverview")
        .Produces<Response<RevenueOverviewResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get revenue overview with comparison")
        .WithDescription(@"Get revenue overview for the specified date range with comparison to previous period.        
**Returns:**
- TotalActualRevenue: Tổng doanh thu thực tế
- TicketsSold: Số vé bán ra
- TicketsCancelled: Số vé huỷ
- RetailRevenue: Doanh thu từ bán lẻ
- AgentRevenue: Doanh thu từ Đại lý
- CollaboratorRevenue: Doanh thu từ Cộng tác viên
- Percentage changes: Tỉ lệ % thay đổi so với cùng kỳ trước đó cho tất cả các giá trị trên

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /revenue-reports/growth-chart - Get revenue growth chart data
        group.MapGet("/growth-chart", async ([AsParameters] GetRevenueGrowthChartQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetRevenueGrowthChart")
        .Produces<Response<List<RevenueGrowthChartDataPointDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get revenue growth chart data")
        .WithDescription(@"Get revenue growth chart data with different period groupings based on date range.
        
**Returns:**
List of data points with:
- Period: Thời gian (ngày/tuần/tháng/giờ tùy theo khoảng thời gian)
- Revenue: Giá trị doanh thu

**Period Types:**
- Today: 24 hours data points
- This week: Daily data points for days of week
- Year range: Monthly data points
- Other ranges: Daily data points

**Parameters:**
- dateFrom: Start date for chart data (required)
- dateTo: End date for chart data (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /revenue-reports/by-sales-channel - Get revenue breakdown by sales channel
        group.MapGet("/by-sales-channel", async ([AsParameters] GetRevenueBySalesChannelQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetRevenueBySalesChannel")
        .Produces<Response<List<RevenueBySalesChannelResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get revenue breakdown by sales channel")
        .WithDescription(@"Get revenue data grouped by sales channel in the specified date range.
        
**Returns:**
List of sales channels with:
- ChannelName: Tên kênh bán hàng
- Revenue: Doanh thu

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /revenue-reports/top-by-agent - Get top 3 revenue by agent type
        group.MapGet("/top-by-agent", async ([AsParameters] GetTopRevenueByAgentQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetTopRevenueByAgent")
        .Produces<Response<List<TopRevenueByAgentResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get top 3 revenue by agent type")
        .WithDescription(@"Get top 3 highest revenue by CTV (collaborator) or Agency (agent) in the specified date range.        
**Returns:**
Top 3 list with:
- Name: Tên CTV hoặc Đại lý
- Code: Mã CTV hoặc Đại lý
- Revenue: Doanh thu

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)
- type: Agent type filter - 'ctv' for collaborator or 'agency' for agent (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /revenue-reports/by-device - Get revenue breakdown by device
        group.MapGet("/by-device", async ([AsParameters] GetRevenueByDeviceQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetRevenueByDevice")
        .Produces<Response<List<RevenueByDeviceResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get revenue breakdown by POS device")
        .WithDescription(@"Get revenue data grouped by POS device in the specified date range.
        
**Returns:**
List of devices with:
- DeviceName: Tên thiết bị
- DeviceId: ID thiết bị
- Revenue: Doanh thu

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /revenue-reports/by-customer-type - Get revenue breakdown by customer type
        group.MapGet("/by-customer-type", async ([AsParameters] GetRevenueByCustomerTypeQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetRevenueByCustomerType")
        .Produces<Response<List<RevenueByCustomerTypeResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get revenue breakdown by customer type")
        .WithDescription(@"Get revenue data grouped by customer type in the specified date range.
        
**Returns:**
List of customer types with:
- CustomerTypeName: Tên nhóm khách hàng
- Revenue: Doanh thu

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /revenue-reports/by-payment-method - Get revenue breakdown by payment method
        group.MapGet("/by-payment-method", async ([AsParameters] GetRevenueByPaymentMethodQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetRevenueByPaymentMethod")
        .Produces<Response<List<RevenueByPaymentMethodResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get revenue breakdown by payment method")
        .WithDescription(@"Get revenue data grouped by payment method in the specified date range.
        
**Returns:**
List of payment methods with:
- PaymentMethodName: Tên phương thức thanh toán
- Revenue: Doanh thu

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)")
        .WithMetadata(new LanguageClientAttribute());
    }
}