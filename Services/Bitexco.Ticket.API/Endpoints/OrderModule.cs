using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Orders.CancelOrder;
using Bitexco.Ticket.Application.Features.Orders.CreateOrder;
using Bitexco.Ticket.Application.Features.Orders.ExportOrder;
using Bitexco.Ticket.Application.Features.Orders.GetOrderById;
using Bitexco.Ticket.Application.Features.Orders.GetOrders;
using Bitexco.Ticket.Application.Features.Orders.GetOrdersForPos;
using Bitexco.Ticket.Application.Features.Orders.ProcessRefund;
using Bitexco.Ticket.Application.Features.Orders.UpdateOrderStatus;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.API.Endpoints;
/// <summary>
/// Request DTOs for Order API
/// </summary>
public record CreateOrderRequest
{
    public Guid? CustomerId { get; set; }
    public Guid? AgentId { get; set; }
    public Guid? RetailChannelId { get; set; }
    public Guid? GroupBookingId { get; set; }
    public Guid? PosUserId { get; set; }
    public Guid? PosDeviceId { get; set; }
    public Guid? VoucherId { get; set; }
    public Guid? ShiftId { get; set; }
    public DateOnly VisitDate { get; set; }
    // <summary>
    /// Customer type for this order
    /// /// 'individual', 'corporate', 'ship'
    /// </summary>
    public CustomerType? CustomerType { get; set; }
    public PaymentTransactionDto PaymentTransaction { get; set; } = null!;
    public List<OrderItemDto> Items { get; set; } = [];
}

public record UpdateOrderStatusRequest
{
    public OrderStatus Status { get; set; }
}

public record ProcessRefundRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class OrderModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/orders")
            .WithTags("Orders")
            .WithSummary("Order Management")
            .WithDescription("Endpoints for managing orders in the system");

        // POST /orders - Create a new order
        group.MapPost("", async (CreateOrderRequest request, ISender sender) =>
        {
            var command = request.Adapt<CreateOrderCommand>();
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("CreateOrder")
        .Produces<Response<CreateOrderResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create a new order")
        .WithDescription("Creates a new order with ticket items for Admin, POS, or Online channels")
        .WithMetadata(new LanguageClientAttribute());

        // GET /orders - Get paginated orders with optional filtering
        group.MapGet("", async ([AsParameters] GetOrdersQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetOrders")
        .Produces<PaginationResponse<OrderSummaryDto>>(StatusCodes.Status200OK)
        .WithSummary("Get orders")
        .WithDescription("Retrieves a paginated list of orders with optional filtering by date range, customer, agent, and status")
        .WithMetadata(new LanguageClientAttribute());

        // GET /orders/for-pos - Get orders for POS system
        group.MapGet("/for-pos", async ([AsParameters] GetOrdersForPosQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetOrdersForPos")
        .Produces<Response<List<OrdersForPosResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get orders for POS")
        .WithDescription("Retrieves a list of orders specifically for POS systems, filtered by date")
        .WithMetadata(new LanguageClientAttribute());

        // GET /orders/{orderId} - Get order details by ID
        group.MapGet("/{orderId:guid}", async (Guid orderId, ISender sender) =>
        {
            var query = new GetOrderByIdQuery(orderId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetOrderById")
        .Produces<Response<GetOrderByIdResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get order by ID")
        .WithDescription("Gets detailed information for a specific order by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /orders/{orderId}/status - Update order status
        group.MapPut("/{orderId:guid}/status", async (Guid orderId, UpdateOrderStatusRequest request, ISender sender) =>
        {
            var command = new UpdateOrderStatusCommand
            {
                OrderId = orderId,
                Status = request.Status
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("UpdateOrderStatus")
        .Produces<Response<UpdateOrderStatusResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Update order status")
        .WithDescription("Updates the status of an order (Pending, Paid, Cancelled, Refunded)")
        .WithMetadata(new LanguageClientAttribute());

        // POST /orders/{orderId}/refund - Process a refund
        group.MapPost("/{orderId:guid}/refund", async (Guid orderId, ProcessRefundRequest request, ISender sender) =>
        {
            var command = new ProcessRefundCommand
            {
                OrderId = orderId,
                Reason = request.Reason
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("ProcessRefund")
        .Produces<Response<ProcessRefundResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Process refund")
        .WithDescription("Processes a refund for an order with reason tracking")
        .WithMetadata(new LanguageClientAttribute());

        // DELETE /orders/{orderId} - Cancel/delete an order
        group.MapDelete("/{orderId:guid}", async (Guid orderId, HttpContext httpContext, ISender sender) =>
        {
            var command = new CancelOrderCommand
            {
                OrderId = orderId,
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("CancelOrder")
        .Produces<Response<CancelOrderResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Cancel order")
        .WithDescription("Cancels an order with approval check")
        .WithMetadata(new LanguageClientAttribute());

        // GET /orders/export - Export orders to Excel
        group.MapGet("/export", async ([AsParameters] ExportOrderCommand query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("ExportOrders")
        .Produces<ExportOrderResponse>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Export orders")
        .WithDescription("Exports orders based on search criteria to an Excel file")
        .WithMetadata(new LanguageClientAttribute());
    }
}