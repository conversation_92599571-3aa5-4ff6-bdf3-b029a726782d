using Bitexco.Ticket.Application.Features.SystemConfigs.Dtos;
using Bitexco.Ticket.Application.Features.SystemConfigs.GetPaymentMethods;
using Bitexco.Ticket.Application.Features.SystemConfigs.GetPromotionApplyTypes;
using Bitexco.Ticket.Application.Features.SystemConfigs.GetRegions;
using Bitexco.Ticket.Application.Features.SystemConfigs.GetVoucherApplyTypes;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for system configuration endpoints
/// </summary>
public class SystemConfigModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/system-configs")
            .WithTags("System Configurations")
            .WithSummary("System Configuration Management")
            .WithDescription("Endpoints for managing system configurations");

        // GET /system-configs/regions - Get list of regions
        group.MapGet("/regions", async (ISender sender) =>
        {
            var query = new GetRegionsQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetRegions")
        .Produces<Response<List<RegionDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get regions list")
        .WithDescription("Retrieve a list of available regions from system configuration")
        .WithMetadata(new LanguageClientAttribute()); 

        // GET /system-configs/payment-methods - Get list of payment methods
        group.MapGet("/payment-methods", async (ISender sender) =>
        {
            var query = new GetPaymentMethodsQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetPaymentMethods")
        .Produces<Response<List<PaymentMethodDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get payment methods list")
        .WithDescription("Retrieve a list of available payment methods from system configuration")
        .WithMetadata(new LanguageClientAttribute()); 

        // GET /system-configs/voucher-apply-types - Get list of voucher apply types
        group.MapGet("/voucher-apply-types", async (ISender sender) =>
        {
            var query = new GetVoucherApplyTypesQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetVoucherApplyTypes")
        .Produces<Response<List<VoucherApplyTypeDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get voucher apply types list")
        .WithDescription("Retrieve a list of available voucher apply types")
        .WithMetadata(new LanguageClientAttribute());


        // GET /system-configs/promotion-apply-types - Get list of promotion apply types
        group.MapGet("/promotion-apply-types", async (ISender sender) =>
        {
            var query = new GetPromotionApplyTypesQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        }).WithName("GetPromotionApplyTypes")
        .Produces<Response<List<PromotionApplyTypeDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get promotion apply types list")
        .WithDescription("Retrieve a list of available promotion apply types")
        .WithMetadata(new LanguageClientAttribute());

    }
}
