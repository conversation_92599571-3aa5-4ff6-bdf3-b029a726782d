using Bitexco.Ticket.Application.Features.LanguageMessages.Commands;
using Bitexco.Ticket.Application.Features.LanguageMessages.Dtos;
using Bitexco.Ticket.Application.Features.LanguageMessages.Queries;

namespace Bitexco.Ticket.API.Endpoints;

public class LanguageMessageModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/language-messages")
            .WithTags("Language Messages")
            .WithSummary("Language Messages Management")
            .WithDescription("Endpoints for managing language messages in the system");

        // GET /language-messages - Retrieve all language messages
        group.MapGet("", async (ISender sender) =>
        {
            var query = new GetAllLanguageMessagesQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        }).WithName("GetAllLanguageMessages")
        .Produces<Response<List<LanguageMessageResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get all language messages")
        .WithDescription("Retrieve a list of all language messages in the system")
        .WithMetadata(new LanguageClientAttribute());

        // GET /language-messages/{key} - Retrieve a language message by key
        group.MapGet("/{key}", async (string key, ISender sender) =>
        {
            var query = new GetLanguageMessageByKeyQuery(key);
            var result = await sender.Send(query);
            return Results.Ok(result);
        }).WithName("GetLanguageMessageByKey")
        .Produces<Response<LanguageMessageResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get language message by key")
        .WithDescription("Retrieve a specific language message by its key")
        .WithMetadata(new LanguageClientAttribute());

        // POST /language-messages - Create a new language message
        group.MapPost("", async (CreateLanguageMessageDto request, ISender sender) =>
        {
            var command = new CreateLanguageMessageCommand(request);
            var result = await sender.Send(command);
            return Results.Ok(result);
        }).WithName("CreateLanguageMessage")
        .Produces<Response<CreateLanguageMessageResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status409Conflict)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create a new language message")
        .WithDescription("Create a new language message with the provided key and message")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /language-messages/{key} - Update an existing language message
        group.MapPut("/{key}", async (string key, UpdateLanguageMessageDto request, ISender sender) =>
        {
            var command = new UpdateLanguageMessageCommand(key, request);
            var result = await sender.Send(command);
            return Results.Ok(result);
        }).WithName("UpdateLanguageMessage")
        .Produces<Response<UpdateLanguageMessageResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update an existing language message")
        .WithDescription("Update an existing language message with the provided message")
        .WithMetadata(new LanguageClientAttribute());

        // DELETE /language-messages/{key} - Delete a language message (hard delete)
        group.MapDelete("/{key}", async (string key, ISender sender) =>
        {
            var command = new DeleteLanguageMessageCommand(key);
            var result = await sender.Send(command);
            return Results.Ok(result);
        }).WithName("DeleteLanguageMessage")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete a language message")
        .WithDescription("Permanently delete a language message from the system (hard delete)")
        .WithMetadata(new LanguageClientAttribute());
    }
}
