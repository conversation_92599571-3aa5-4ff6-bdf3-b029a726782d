﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Voucher.Queries;

namespace Bitexco.Ticket.API.Endpoints
{
    public class VoucherModule : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            var group = app.MapGroup("/vouchers")
               .WithTags("Vouchers")
               .WithSummary("Voucher Management")
               .WithDescription("Endpoints for managing vouchers in the system");

            // POST /vouchers - Create a new voucher
            //group.MapPost("", async (VoucherDto request, ISender sender) =>
            //{
            //    var command = new CreateVoucherCommand(request);
            //    var result = await sender.Send(command);
            //    return Results.Ok(result);
            //}).WithName("CreateVoucher")
            //.Produces<Response<CreateVoucherResult>>(StatusCodes.Status200OK)
            //.ProducesProblem(StatusCodes.Status400BadRequest)
            //.ProducesProblem(StatusCodes.Status500InternalServerError)
            //.WithSummary("Create a new voucher")
            //.WithDescription("Create a new voucher with the provided details")
            //.WithMetadata(new LanguageClientAttribute());

            // PUT /vouchers - Update an existing voucher
            //group.MapPut("/{id:guid}", async (Guid id, VoucherDto request, ISender sender) =>
            //{
            //    var command = new UpdateVoucherCommand(id, request);
            //    var result = await sender.Send(command);
            //    return Results.Ok(result);
            //})
            //.WithName("UpdateVoucher")
            //.Produces<Response<UpdateVoucherResult>>(StatusCodes.Status200OK)
            //.ProducesProblem(StatusCodes.Status400BadRequest)
            //.ProducesProblem(StatusCodes.Status404NotFound)
            //.ProducesProblem(StatusCodes.Status500InternalServerError)
            //.WithSummary("Update an existing voucher")
            //.WithDescription("Update an existing voucher with the provided details")
            //.WithMetadata(new LanguageClientAttribute());

            //// DELETE /vouchers/{id} - Delete a voucher by ID
            //group.MapDelete("/{id}", async (Guid id, ISender sender) =>
            //{
            //    var command = new DeleteVoucherCommand(id);
            //    var result = await sender.Send(command);
            //    return Results.Ok(result);
            //}).WithName("DeleteVoucher")
            //.Produces<Response<object>>(StatusCodes.Status200OK)
            //.ProducesProblem(StatusCodes.Status404NotFound)
            //.ProducesProblem(StatusCodes.Status500InternalServerError)
            //.WithSummary("Delete a voucher")
            //.WithDescription("Delete a voucher by its ID")
            //.WithMetadata(new LanguageClientAttribute());

            // GET /vouchers/validate - Validate a voucher by code
            group.MapGet("/validate", async ([AsParameters] GetVoucherByCodeQuery request,
                ISender sender) =>
            {
                var result = await sender.Send(request);
                return Results.Ok(result);
            }).WithName("ValidateVoucher")
            .Produces<Response<VoucherResponseDto>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Validate a voucher by code")
            .WithDescription("Validate a voucher using its code to check its status and details")
            .WithMetadata(new LanguageClientAttribute());

            // GET /vouchers - Retrieve all vouchers
            group.MapGet("", async (int PageIndex,
                int PageSize,
                string? Code,
                DateTime? ValidFrom,
                DateTime? ValidTo,
                ISender sender) =>
            {
                var request = new GetAllVoucherQuery(new GetAllVoucherQueryDto
                {
                    Code = Code,
                    ValidFrom = ValidFrom,
                    ValidTo = ValidTo,
                    PageIndex = PageIndex,
                    PageSize = PageSize
                });

                var result = await sender.Send(request);
                return Results.Ok(result);
            }).WithName("GetAllVouchers")
            .Produces<PaginationResponse<List<VoucherResponseDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Get all vouchers")
            .WithDescription("Retrieve a list of all vouchers in the system")
            .WithMetadata(new LanguageClientAttribute());
        }
    }
}