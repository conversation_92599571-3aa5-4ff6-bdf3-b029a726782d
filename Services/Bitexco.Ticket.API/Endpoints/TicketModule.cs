using Bitexco.Ticket.Application.Features.Tickets.CreateTicket;
using Bitexco.Ticket.Application.Features.Tickets.CreateTicketBulk;
using Bitexco.Ticket.Application.Features.Tickets.KioskSync;
using Bitexco.Ticket.Application.Features.Tickets.ValidateTicketByCode;
using Bitexco.Ticket.Application.Features.Tickets.UseTicketByCode;
using Microsoft.AspNetCore.Mvc;
using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Tickets.Dtos;
using Bitexco.Ticket.Application.Features.Tickets.GetTicketByCode;
using Bitexco.Ticket.Application.Features.Tickets.GetTicketHistoryByCode;
using Bitexco.Ticket.Application.Features.Tickets.GetTickets;
using Bitexco.Ticket.Application.Features.Tickets.ExportTickets;
using Bitexco.Ticket.Application.Features.Tickets.GetTicketsForPos;
using Bitexco.Ticket.Application.Features.Tickets.GetTicketById;
using Bitexco.Ticket.Application.Features.Tickets.GetAllTickets;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Request model for marking a ticket as used
/// </summary>
public record UseTicketRequest(string AccessLocation, Guid? ValidatedByUserId, Guid? ValidatedAtDeviceId);

/// <summary>
/// Carter module for ticket management endpoints
/// </summary>
public class TicketModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/tickets")
            .WithTags("Tickets")
            .WithSummary("Ticket Management")
            .WithDescription("Endpoints for managing tickets in the system");

        // POST /tickets - Create a new ticket with QR code
        group.MapPost("", async (TicketDto request, ISender sender) =>
        {
            var command = new CreateTicketCommand(request);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("CreateTicket")
        .Produces<Response<CreateTicketResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create a new ticket")
        .WithDescription("Create a new ticket with a unique QR code")
        .WithMetadata(new LanguageClientAttribute());

        // POST /tickets/bulk - Create multiple tickets by ticket type ID and quantity
        group.MapPost("/bulk", async (CreateTicketBulkCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithName("CreateTicketBulk")
        .Produces<Response<List<TicketResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create multiple tickets in bulk")
        .WithDescription("Create a list of tickets based on ticket type ID and quantity. All tickets will have 'created' status.")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets - Retrieve all tickets with optional filtering and pagination
        group.MapGet("", async ([AsParameters] GetTicketsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTickets")
        .Produces<PaginationResponse<TicketResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get all tickets")
        .WithDescription("Retrieve a paginated list of tickets with optional filtering by status, date, etc.")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets/all - Retrieve all tickets without pagination
        group.MapGet("/all", async ([AsParameters] GetAllTicketsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetAllTickets")
        .Produces<Response<List<AllTicketResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get all tickets without pagination")
        .WithDescription("Retrieve a complete list of all tickets without pagination, useful for administrative purposes")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets/forPos - Retrieve tickets for POS system with optional filtering
        group.MapGet("/for-pos", async ([AsParameters] GetTicketsForPosQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTicketsForPos")
        .Produces<Response<TicketForPosResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get tickets for POS")
        .WithDescription("Retrieve a paginated list of tickets specifically for POS systems, with optional filtering by status, date, etc.")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets/{id} - Retrieve ticket details by ID
        group.MapGet("/{id:guid}", async (Guid id, ISender sender) =>
        {
            var query = new GetTicketByIdQuery(id);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTicketById")
        .Produces<Response<GetTicketByIdResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get ticket by ID")
        .WithDescription("Retrieve detailed information for a specific ticket by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets/{code} - Retrieve detailed ticket information by QR code
        group.MapGet("/{code}", async (string code, ISender sender) =>
        {
            var query = new GetTicketByCodeQuery(code);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTicketByCode")
        .Produces<Response<DetailTicketResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get detailed ticket information by QR code")
        .WithDescription("Retrieve complete ticket details including ticket info, customer info, and lifecycle history")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets/validate/{code} - Validate ticket by QR code
        group.MapGet("/validate/{code}", async (string code, ISender sender) =>
        {
            var query = new ValidateTicketByCodeQuery(code);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("ValidateTicket")
        .Produces<Response<TicketValidationResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Validate ticket")
        .WithDescription("Validate a ticket's status, expiry, and other conditions")
        .WithMetadata(new LanguageClientAttribute());

        // PATCH /tickets/use/{code} - Mark ticket as used by QR code
        group.MapPatch("/use/{code}", async (string code, UseTicketRequest request, ISender sender) =>
        {
            var command = new UseTicketByCodeCommand(
                code,
                request.AccessLocation,
                request.ValidatedByUserId,
                request.ValidatedAtDeviceId);

            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("UseTicket")
        .Produces<Response<UseTicketResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Mark ticket as used")
        .WithDescription("Update a ticket's status to 'used' and record validation details")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets/kiosk-sync - Endpoint for syncing tickets with kiosk devices
        group.MapGet("/kiosk-sync", async ([FromQuery] DateTime? lastSyncTime, [FromQuery] Guid? deviceId, ISender sender) =>
        {
            var query = new KioskSyncQuery(lastSyncTime, deviceId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("KioskSync")
        .Produces<Response<KioskSyncResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Kiosk synchronization")
        .WithDescription("Sync ticket data with kiosk devices for offline validation")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets/history/{code} - Retrieve ticket lifecycle and transaction history by QR code
        group.MapGet("/history/{code}", async (string code, ISender sender) =>
        {
            var query = new GetTicketHistoryByCodeQuery(code);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTicketHistory")
        .Produces<Response<TicketHistoryResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get ticket history")
        .WithDescription("Retrieve the complete lifecycle and audit trail for a specific ticket")
        .WithMetadata(new LanguageClientAttribute());

        // GET /tickets/export - Export tickets to Excel
        group.MapGet("/export", async ([AsParameters] ExportTicketCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        }).WithName("ExportTickets")
        .Produces<Response<ExportTicketResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Export tickets to Excel")
        .WithDescription("Export tickets to an Excel file with optional filtering by status, date, etc.")
        .WithMetadata(new LanguageClientAttribute());
    }
}