﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.GroupBookings.AddBusToGroupBooking;
using Bitexco.Ticket.Application.Features.GroupBookings.CancelGroupBookingById;
using Bitexco.Ticket.Application.Features.GroupBookings.CreateGroupBooking;
using Bitexco.Ticket.Application.Features.GroupBookings.ExportGroupBookings;
using Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookingBuses;
using Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookingById;
using Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookings;
using Bitexco.Ticket.Application.Features.GroupBookings.GetSummaryGroupBookings;
using Bitexco.Ticket.Application.Features.GroupBookings.RemoveAgentFromBusInGroupBooking;
using Bitexco.Ticket.Application.Features.GroupBookings.RemoveBusFromGroupBooking;
using Bitexco.Ticket.Application.Features.GroupBookings.UpdateGroupBookingBus;

namespace Bitexco.Ticket.API.Endpoints;

public class GroupBookingModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/group-bookings")
        .WithTags("GroupBookings")
        .WithSummary("GroupBookings Management")
        .WithDescription("Endpoints for managing group bookings in the system");

        // GET /group-bookings - Retrieve all group bookings
        group.MapGet("", async ([AsParameters] GetGroupBookingQuery request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        }).WithName("GetAllGroupBookings")
        .Produces<PaginationResponse<GroupBookingResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get all group bookings")
        .WithDescription("Retrieve a list of all group bookings in the system")
        .WithMetadata(new LanguageClientAttribute());

        // GET /group-bookings/summary - Retrieve summary of group bookings
        group.MapGet("summary", async ([AsParameters] GetSummaryGroupBookingsQuery request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        }).WithName("GetSummaryGroupBookings")
        .Produces<Response<List<GetSummaryGroupBookingResult>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get summary of group bookings")
        .WithDescription("Retrieve a summary of group bookings, typically used in POS systems");

        // GET /group-bookings/{id} - Retrieve a group booking by ID
        group.MapGet("{id:guid}", async (Guid id, ISender sender) =>
        {
            var query = new GetGroupBookingByIdQuery { Id = id };
            var result = await sender.Send(query);
            return result is not null ? Results.Ok(result) : Results.NotFound();
        })
        .WithName("GetGroupBookingById")
        .Produces<Response<GetGroupBookingByIdResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get group booking by ID")
        .WithDescription("Retrieve a group booking by its ID");

        // POST /group-bookings - Create a new group booking
        group.MapPost("", async (CreateGroupBookingCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        }).WithName("CreateGroupBooking")
        .Produces<Response<CreateGroupBookingResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create a new group booking")
        .WithDescription("Create a new group booking with the provided details")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /group-bookings/{id}/cancel - Cancel a group booking
        group.MapPut("{id:guid}/cancel", async (Guid id, ISender sender) =>
        {
            var request = new CancelGroupBookingByIdCommand { Id = id };
            var result = await sender.Send(request);
            return Results.Ok(result);
        }).WithName("CancelGroupBookingById")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Cancel a group booking")
        .WithDescription("Cancel a group booking with the provided ID")
        .WithMetadata(new LanguageClientAttribute());

        // POST /group-bookings/{id}/buses - Add buses to a group booking
        group.MapPost("{id:guid}/buses", async (Guid id, CreateGroupBookingBus request, ISender sender) =>
        {
            var command = new AddBusToGroupBookingCommand
            {
                Id = id,
                Bus = request
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        }).WithName("AddBusesToGroupBooking")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Add buses to a group booking")
        .WithDescription("Add buses to a group booking with the provided ID")
        .WithMetadata(new LanguageClientAttribute());

        // GET /group-bookings/{id}/buses - Retrieve buses in a group booking
        group.MapGet("{id:guid}/buses", async (Guid id, ISender sender) =>
        {
            var query = new GetGroupBookingBusesQuery { Id = id };
            var result = await sender.Send(query);
            return result is not null ? Results.Ok(result) : Results.NotFound();
        }).WithName("GetBusesInGroupBooking")
        .Produces<Response<List<GetGroupBookingBusDetailDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get buses in a group booking")
        .WithDescription("Retrieve all buses associated with a group booking by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /group-bookings/{id}/buses/{busId} - Update a bus in a group booking
        group.MapPut("{id:guid}/buses/{busId:guid}", async (Guid id, Guid busId, CreateGroupBookingBus bus, ISender sender) =>
        {
            var command = new UpdateGroupBookingBusCommand
            {
                Id = id,
                BusId = busId,
                Bus = bus
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        }).WithName("UpdateBusInGroupBooking")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update a bus in a group booking")
        .WithDescription("Update a bus in a group booking with the provided IDs")
        .WithMetadata(new LanguageClientAttribute());

        // DELELE /group-bookings/{id}/busses/{busId} - Remove a bus from a group booking
        group.MapDelete("{id:guid}/buses/{busId:guid}", async (Guid id, Guid busId, ISender sender) =>
        {
            var request = new RemoveBusFromGroupBookingCommand { Id = id, BusId = busId };
            var result = await sender.Send(request);
            return Results.Ok(result);
        }).WithName("RemoveBusFromGroupBooking")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Remove a bus from a group booking")
        .WithDescription("Remove a bus from a group booking with the provided IDs")
        .WithMetadata(new LanguageClientAttribute());

        // DELELE /group-bookings/{id}/busses/{busId}/agents/{agentId} - Remove an agent from a bus in a group booking
        group.MapDelete("{id:guid}/buses/{busId:guid}/agents/{agentId:guid}", async (Guid id, Guid busId, Guid agentId, ISender sender) =>
        {
            var request = new RemoveAgentFromBusInGroupBookingCommand { Id = id, BusId = busId, AgentId = agentId };
            var result = await sender.Send(request);
            return Results.Ok(result);
        }).WithName("RemoveAgentFromBusInGroupBooking")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Remove an agent from a bus in a group booking")
        .WithDescription("Remove an agent from a bus in a group booking with the provided IDs")
        .WithMetadata(new LanguageClientAttribute());

        // GET /group-bookings/export - Export group bookings
        group.MapGet("export", async ([AsParameters] ExportGroupBookingCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        }).WithName("ExportGroupBookings")
        .Produces<ExportGroupBookingResponse>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Export group bookings")
        .WithDescription("Export group bookings in a format suitable for external use")
        .WithMetadata(new LanguageClientAttribute());
    }
}