using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Customers.Commands.CreateCustomer;
using Bitexco.Ticket.Application.Features.Customers.Commands.DeleteCustomer;
using Bitexco.Ticket.Application.Features.Customers.Commands.ExportCustomer;
using Bitexco.Ticket.Application.Features.Customers.Commands.UpdateCustomer;
using Bitexco.Ticket.Application.Features.Customers.Queries.GetCustomerById;
using Bitexco.Ticket.Application.Features.Customers.Queries.GetCustomers;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for Customer management endpoints
/// </summary>
public class CustomerModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/customers")
            .WithTags("Customers")
            .WithSummary("Customer Management")
            .WithDescription("Endpoints for managing customers in the system")
            .WithMetadata(new LanguageClientAttribute());

        group.MapPost("/", async (CustomerDto Customer, ISender sender) =>
        {
            var result = await sender.Send(new CreateCustomerCommand { Customer = Customer });
            return Results.Ok(result);
        })
        .WithName("CreateCustomer")
        .WithSummary("Create a new customer")
        .WithDescription("Create a new customer with the provided information")
        .Produces<Response<CreateCustomerResult>>(200)
        .ProducesValidationProblem()
        .ProducesProblem(500);

        group.MapPut("/{id:guid}", async (Guid id, CustomerDto customerDto, ISender sender) =>
        {
            var command = new UpdateCustomerCommand { Id = id, Customer = customerDto };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("UpdateCustomer")
        .WithSummary("Update an existing customer")
        .WithDescription("Update customer information by ID")
        .Produces<Response<object>>(200)
        .ProducesValidationProblem()
        .ProducesProblem(404)
        .ProducesProblem(500);

        group.MapGet("/{id:guid}", async (Guid id, ISender sender) =>
        {
            var query = new GetCustomerByIdQuery { Id = id };
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetCustomerById")
        .WithSummary("Get customer by ID")
        .WithDescription("Retrieve customer details by ID")
        .Produces<Response<CustomerDetailResponseDto>>(200)
        .ProducesProblem(404)
        .ProducesProblem(500);

        group.MapDelete("/{id:guid}", async (Guid id, ISender sender) =>
        {
            var command = new DeleteCustomerCommand { Id = id };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("DeleteCustomer")
        .WithSummary("Delete a customer")
        .WithDescription("Soft delete a customer by ID")
        .Produces<Response<object>>(200)
        .ProducesProblem(404)
        .ProducesProblem(500);

        group.MapGet("/", async ([AsParameters] GetCustomersQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetCustomers")
        .WithSummary("Get all customers with pagination")
        .WithDescription("Retrieve paginated list of customers with optional filtering by search term and retail channel")
        .Produces<PaginationResponse<CustomerResponseDto>>(200)
        .ProducesProblem(500);

        group.MapGet("/export", async ([AsParameters] ExportCustomerCommand query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        }).WithName("ExportCustomers")
        .WithSummary("Export customers")    
        .WithDescription("Export customers to an Excel file with optional filtering by search term and retail channel")
        .Produces<Response<ExportCustomerResponse>>(200)
        .ProducesProblem(500)
        .ProducesValidationProblem();
    }
}