using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Agents.Commands.CreateAgent;
using Bitexco.Ticket.Application.Features.Agents.Commands.DeleteAgent;
using Bitexco.Ticket.Application.Features.Agents.Commands.ExportAgent;
using Bitexco.Ticket.Application.Features.Agents.Commands.ImportAgent;
using Bitexco.Ticket.Application.Features.Agents.Commands.LockAgent;
using Bitexco.Ticket.Application.Features.Agents.Commands.UnlockAgent;
using Bitexco.Ticket.Application.Features.Agents.Commands.UpdateAgent;
using Bitexco.Ticket.Application.Features.Agents.Dtos;
using Bitexco.Ticket.Application.Features.Agents.Queries.GetAgentById;
using Bitexco.Ticket.Application.Features.Agents.Queries.GetAgents;
using Bitexco.Ticket.Application.Features.Agents.Queries.GetFilteredAgents;
using Bitexco.Ticket.Application.Features.Contracts;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Request model for locking an agent account
/// </summary>
public record LockAgentRequest(string Reason);

public record CreateOrUpdateContractRequest(
    string SapContractCode,
    string ContractName,
    DateOnly SignedDate,
    DateOnly ExpiryDate,
    string? ContractFileUrl,
    ContractStatus Status,
    string? Notes);

/// <summary>
/// Carter module for agent management endpoints
/// </summary>
public class AgentModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/agents")
            .WithTags("Agents")
            .WithSummary("Agent Management")
            .WithDescription("Endpoints for managing ticket sales agents in the system");

        // GET /agents - Get list of agents with filtering and pagination
        group.MapGet("", async ([AsParameters] GetAgentsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetAgents")
        .Produces<Response<List<AgentResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get all agents")
        .WithDescription("Get a paginated list of agents with optional filtering")
        .WithMetadata(new LanguageClientAttribute());

        // GET /agents/filter - Get agents with specific filters
        group.MapGet("/filter", async ([AsParameters] GetFilteredAgentsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetAgentsFilter")
        .Produces<PaginationResponse<AgentResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get agents with filters")
        .WithDescription("Get a paginated list of agents with specific filters like name, tax code, status, and creation date")
        .WithMetadata(new LanguageClientAttribute());


        // GET /agents/{id} - Get agent details by ID
        group.MapGet("/{id}", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetAgentByIdQuery { Id = id });
            return result;
        })
        .WithName("GetAgentById")
        .Produces<Response<AgentResponseDetailDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get agent by ID")
        .WithDescription("Get detailed information for a specific agent")
        .WithMetadata(new LanguageClientAttribute());

        // POST /agents - Create a new agent
        group.MapPost("", async (AgentDto request, ISender sender) =>
        {
            var command = new CreateAgentCommand { Agent = request };
            var result = await sender.Send(command);
            return result;
        })
        .WithName("CreateAgent")
        .Produces<Response<AgentResponseDetailDto>>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create agent")
        .WithDescription("Create a new ticket sales agent")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /agents/{id} - Update an existing agent
        group.MapPut("/{id}", async (Guid id, AgentDto request, ISender sender) =>
        {
            var command = new UpdateAgentCommand { Id = id, Agent = request };
            var result = await sender.Send(command);
            return result;
        })
        .WithName("UpdateAgent")
        .Produces<Response<AgentResponseDetailDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update agent")
        .WithDescription("Update an existing agent's information")
        .WithMetadata(new LanguageClientAttribute());

        // DELETE /agents/{id} - Delete an agent
        group.MapDelete("/{id}", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new DeleteAgentCommand { Id = id });
            return result;
        })
        .WithName("DeleteAgent")
        .Produces<Response<object>>(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete agent")
        .WithDescription("Mark an agent as deleted in the system")
        .WithMetadata(new LanguageClientAttribute());

        // POST /agents/{id}/lock - Lock an agent account
        group.MapPost("/{id}/lock", async (Guid id, LockAgentRequest request, ISender sender) =>
        {
            var result = await sender.Send(new LockAgentCommand { Id = id, Reason = request.Reason });
            return result;
        })
        .WithName("LockAgent")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Lock agent account")
        .WithDescription("Suspend an agent account and prevent them from accessing the system")
        .WithMetadata(new LanguageClientAttribute());

        // POST /agents/{id}/unlock - Unlock an agent account
        group.MapPost("/{id}/unlock", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new UnlockAgentCommand { Id = id });
            return result;
        })
        .WithName("UnlockAgent")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Unlock agent account")
        .WithDescription("Reactivate a suspended agent account")
        .WithMetadata(new LanguageClientAttribute());

        // GET /agents/seller/export - Export agents as sellers
        group.MapGet("/seller/export", async (ISender sender) =>
        {
            var result = await sender.Send(new ExportSellerCommand());
            return result;
        }).WithName("ExportSeller")
        .DisableAntiforgery()
        .Produces<Response<ExportSellerCommandResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Export agents as sellers")
        .WithDescription("Export agents to be used as sellers in the system")
        .WithMetadata(new LanguageClientAttribute());

        // POST /agents/seller/import - Import agents as sellers
        group.MapPost("/seller/import", async (IFormFile file, ISender sender) =>
        {
            var result = await sender.Send(new ImportSellerCommand { FilePath = file });
            return result;
        }).WithName("ImportSeller")
         .DisableAntiforgery()
        .Produces<Response<List<AgentResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Import agents as sellers")
        .WithDescription("Import agents from an Excel file to be used as sellers")
        .WithMetadata(new LanguageClientAttribute());

        // GET /agents/export - Export agents
        group.MapGet("/export", async (ISender sender) =>
        {
            var result = await sender.Send(new ExportAgentCommand());
            return result;
        }).WithName("ExportAgent")
        .Produces<Response<ExportAgentCommandResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Export agents")
        .WithDescription("Export agents data for external use")
        .WithMetadata(new LanguageClientAttribute());

        //POST /agents/import - Import agents
        group.MapPost("/import", async (IFormFile file, ISender sender) =>
        {
            var result = await sender.Send(new ImportAgentCommand { FilePath = file });
            return result;
        }).
        WithName("ImportAgent")
        .DisableAntiforgery()
        .Produces<Response<List<AgentResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Import agents")
        .WithDescription("Import agents from an Excel file")
        .WithMetadata(new LanguageClientAttribute());

        //GET /agents/{id}/contracts - Get contracts by agent ID
        group.MapGet("/{id}/contracts", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetContractsByAgentQuery(id));
            return result;
        })
        .WithName("GetContractsByAgent")
        .Produces<Response<List<ContractResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get contracts by agent ID")
        .WithDescription("Get all contracts associated with a specific agent")
        .WithMetadata(new LanguageClientAttribute());

        //POST /agents/{id}/contracts - Create contract for agent
        group.MapPost("/{id}/contracts", async (Guid id, CreateOrUpdateContractRequest request, ISender sender) =>
        {
            var command = request.Adapt<CreateContractCommand>() with
            {
                AgentId = id
            };
            var result = await sender.Send(command);
            return result;
        })
        .WithName("CreateContract")
        .Produces<Response<CreateContractResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create contract for agent")
        .WithDescription("Create a new contract for an agent")
        .WithMetadata(new LanguageClientAttribute());

        //PUT /agents/{id}/contracts/{contractId} - Update contract by ID
        group.MapPut("/{id}/contracts/{contractId}", async (Guid id, Guid contractId, CreateOrUpdateContractRequest request, ISender sender) =>
        {
            var command = request.Adapt<UpdateContractCommand>() with
            {
                AgentId = id,
                Id = contractId
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("UpdateContract")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update contract for agent")
        .WithDescription("Update an existing contract for an agent")
        .WithMetadata(new LanguageClientAttribute());

        //DELETE /agents/{id}/contracts/{contractId} - Delete contract by ID
        group.MapDelete("/{id}/contracts/{contractId}", async (Guid id, Guid contractId, ISender sender) =>
        {
            var result = await sender.Send(new DeleteContractCommand(contractId, id));
            return Results.Ok(result);
        })
        .WithName("DeleteContract")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete contract for agent")
        .WithDescription("Delete an existing contract for an agent")
        .WithMetadata(new LanguageClientAttribute());
    }
}