﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Faq.Commands;
using Bitexco.Ticket.Application.Features.Faq.Queries;

namespace Bitexco.Ticket.API.Endpoints
{
    public record GetAllFaqRequest();

    public class FaqModule : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            var group = app.MapGroup("/faqs")
            .WithTags("Faqs")
            .WithSummary("Faqs Management")
            .WithDescription("Endpoints for managing faqs in the system");

            // GET /faq - Retrieve all FAQs
            group.MapGet("", async ([AsParameters] GetAllFaqRequest request, ISender sender) =>
            {
                var query = new GetAllFaqQuery();
                var result = await sender.Send(query);
                return Results.Ok(result);
            }).WithName("GetAllFaqs")
            .Produces<Response<List<FaqResponseDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Get all FAQs")
            .WithDescription("Retrieve a list of all FAQs in the system")
            .WithMetadata(new LanguageClientAttribute());

            // POST /faq - Create a new FAQ
            group.MapPost("", async (CreateFaqDto request, ISender sender) =>
            {
                var command = new CreateFaqCommand(request);
                var result = await sender.Send(command);
                return Results.Ok(result);
            }).WithName("CreateFaq")
            .Produces<Response<CreateFaqResult>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Create a new FAQ")
            .WithDescription("Create a new FAQ with the provided details")
            .WithMetadata(new LanguageClientAttribute());

            // PUT /faq - Update an existing FAQ
            group.MapPut("", async (UpdateFaqDto command, ISender sender) =>
            {
                var request = new UpdateFaqCommand(command);
                var result = await sender.Send(request);
                return Results.Ok(result);
            }).WithName("UpdateFaq")
            .Produces<Response<UpdateFaqResult>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Update an existing FAQ")
            .WithDescription("Update an existing FAQ with the provided details")
            .WithMetadata(new LanguageClientAttribute());
        }
    }
}