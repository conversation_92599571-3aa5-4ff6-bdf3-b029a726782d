﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.VoucherCampaign.Commands;
using Bitexco.Ticket.Application.Features.VoucherCampaign.Queries;

namespace Bitexco.Ticket.API.Endpoints
{
    public class VoucherCampainModule : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            var group = app.MapGroup("/voucher-campaigns")
                .WithTags("Voucher Campaigns")
                .WithSummary("Voucher Campaign Management")
                .WithDescription("Endpoints for managing voucher campaigns in the system");
            
            // POST /voucher-campaigns - Create a new voucher campaign
            group.MapPost("", async (CreateVoucherCampaignCommand request, ISender sender) =>
            {
                var command = request;
                var result = await sender.Send(command);
                return Results.Ok(result);
            }).WithName("CreateVoucherCampaign")
            .Produces<Response<CreateVoucherCampaignResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Create a new voucher campaign")
            .WithDescription("Create a new voucher campaign with the provided details")
            .WithMetadata(new LanguageClientAttribute());

            // PUT /voucher-campaigns/{id} - Update an existing voucher campaign
            group.MapPatch("/{id:guid}/active", async (Guid id, ISender sender) =>
            {
                var result = await sender.Send(new ActiveVoucherCampaignCommand { Id = id });
                return Results.Ok(result);
            }).WithName("UpdateVoucherCampaign")
            .Produces<Response<VoucherCampaignResponseDto>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Activate/Deactivate a voucher campaign")
            .WithDescription("Activate or deactivate a voucher campaign by its ID")
            .WithMetadata(new LanguageClientAttribute());

            // GET /voucher-campaigns - Get all voucher campaigns with pagination
            group.MapGet("", async ([AsParameters] VoucherCampaignQuery query, ISender sender) =>
            {
                var result = await sender.Send(query);
                return Results.Ok(result);
            }).WithName("GetVoucherCampaigns")
            .Produces<Response<List<VoucherCampaignResponseDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Get all voucher campaigns")
            .WithDescription("Retrieve all voucher campaigns with pagination support")
            .WithMetadata(new LanguageClientAttribute());

            // DELETE /voucher-campaigns/{id} - Delete a voucher campaign by ID
            group.MapDelete("/{id:guid}", async (Guid id, ISender sender) =>
            {
                var command = new DeleteVoucherCampaignCommand { Id = id };
                var result = await sender.Send(command);
                return Results.Ok(result);
            }).WithName("DeleteVoucherCampaign")
            .Produces<Response<object>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Delete a voucher campaign")
            .WithDescription("Delete a voucher campaign by its ID if it is not in use")
            .WithMetadata(new LanguageClientAttribute());
        }
    }
}