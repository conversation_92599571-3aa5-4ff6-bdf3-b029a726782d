using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Shifts.OpenShift;
using Bitexco.Ticket.Application.Features.Shifts.DeleteShift;
using Bitexco.Ticket.Application.Features.Shifts.GetShiftById;
using Bitexco.Ticket.Application.Features.Shifts.GetShifts;
using Bitexco.Ticket.Application.Features.Shifts.UpdateShift;
using Bitexco.Ticket.Application.Features.Shifts.CloseShift;
using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Application.Features.Shifts.GetShiftTotalAmountById;
using Bitexco.Ticket.Application.Features.Shifts.GetShiftRevenueSummary;
using Bitexco.Ticket.Application.Features.Shifts.GetShiftPaymentBreakdown;
using Bitexco.Ticket.Application.Features.Shifts.GetShiftTicketTypeRevenue;

namespace Bitexco.Ticket.API.Endpoints;

public class ShiftModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/shifts")
            .WithTags("Shifts")
            .WithSummary("Shift Management")
            .WithDescription("Endpoints for managing shifts in the system");

        // POST /shifts/open - Create a new shift
        group.MapPost("open", async (OpenShiftCommandRequest request, ISender sender) =>
        {
            var command = new OpenShiftCommand
            {
                Shift = request
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("OpenShift")
        .Produces<Response<OpenShiftCommandResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Open a new shift")
        .WithDescription("Open a new shift for a user on a POS device")
        .WithMetadata(new LanguageClientAttribute())
        .RequireAuthorization();

        // PUT /shifts/{id}/close - Close an existing shift
        group.MapPut("{id:guid}/close", async (Guid id, CloseShiftCommandRequest request, ISender sender) =>
        {
            var command = new CloseShiftCommand
            {
                Id = id,
                Shift = request
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("CloseShift")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Close a shift")
        .WithDescription("Close an existing shift by providing the shift details")
        .WithMetadata(new LanguageClientAttribute())
        .RequireAuthorization();

        // GET /shifts - Get paginated shifts with optional filtering
        group.MapGet("", async ([AsParameters] GetShiftsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetShifts")
        .Produces<PaginationResponse<ShiftSummaryDto>>(StatusCodes.Status200OK)
        .WithSummary("Get shifts")
        .WithDescription("Retrieves a paginated list of shifts with optional filtering by user, POS device, date range, and status")
        .WithMetadata(new LanguageClientAttribute());

        // GET /shifts/{shiftId} - Get shift details by ID
        group.MapGet("/{shiftId:guid}", async (Guid shiftId, ISender sender) =>
        {
            var query = new GetShiftByIdQuery(shiftId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetShiftById")
        .Produces<Response<ShiftResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get shift by ID")
        .WithDescription("Gets detailed information for a specific shift by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // GET /shifts/{shiftId}/total-amount - Get total amount for a shift
        group.MapGet("/{shiftId:guid}/total-amount", async (Guid shiftId,
            PaymentTransactionMethod? paymentMethod, ISender sender) =>
        {
            var query = new GetShiftTotalAmountByIdQuery(shiftId, paymentMethod);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetShiftTotalAmount")
        .Produces<Response<GetShiftTotalAmountByIdResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get total amount for a shift")
        .WithDescription("Retrieves the total amount for a specific shift by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /shifts/{shiftId} - Update shift
        group.MapPut("/{shiftId:guid}", async (Guid shiftId, ShiftDto request, ISender sender) =>
        {
            var command = new UpdateShiftCommand
            {
                Id = shiftId,
                Shift = request
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("UpdateShift")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Update shift")
        .WithDescription("Updates an existing shift by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // DELETE /shifts/{shiftId} - Delete shift
        group.MapDelete("/{shiftId:guid}", async (Guid shiftId, ISender sender) =>
        {
            var command = new DeleteShiftCommand(shiftId);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("DeleteShift")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Delete shift")
        .WithDescription("Deletes a shift by its ID (soft delete)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /shifts/{shiftId}/revenue-summary - Get shift revenue summary
        group.MapGet("/{shiftId:guid}/revenue-summary", async (Guid shiftId, ISender sender) =>
        {
            var query = new GetShiftRevenueSummaryQuery(shiftId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetShiftRevenueSummary")
        .Produces<Response<GetShiftRevenueSummaryResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get shift revenue summary")
        .WithDescription("Gets revenue summary including total revenue, order count, tickets sold/cancelled")
        .WithMetadata(new LanguageClientAttribute());

        // GET /shifts/{shiftId}/payment-breakdown - Get shift payment breakdown
        group.MapGet("/{shiftId:guid}/payment-breakdown", async (Guid shiftId, ISender sender) =>
        {
            var query = new GetShiftPaymentBreakdownQuery(shiftId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetShiftPaymentBreakdown")
        .Produces<Response<GetShiftPaymentBreakdownResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get shift payment breakdown")
        .WithDescription("Gets payment breakdown by payment method with amounts and transaction counts")
        .WithMetadata(new LanguageClientAttribute());

        // GET /shifts/{shiftId}/ticket-type-revenue - Get shift ticket type revenue
        group.MapGet("/{shiftId:guid}/ticket-type-revenue", async (Guid shiftId, ISender sender) =>
        {
            var query = new GetShiftTicketTypeRevenueQuery(shiftId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetShiftTicketTypeRevenue")
        .Produces<Response<GetShiftTicketTypeRevenueResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get shift ticket type revenue")
        .WithDescription("Gets revenue breakdown by ticket type with quantities, unit prices, and amounts")
        .WithMetadata(new LanguageClientAttribute());
    }
}
