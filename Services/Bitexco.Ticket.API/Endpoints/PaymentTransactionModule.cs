using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.PaymentTransactions.CreatePaymentTransaction;
using Bitexco.Ticket.Application.Features.PaymentTransactions.ExportPaymentTransactions;
using Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactionByOrderId;
using Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactions;
using Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactionSummary;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Request DTOs for Payment Transaction API
/// </summary>
public record CreatePaymentTransactionRequest
{
    public PaymentTransactionMethod PaymentMethod { get; set; }
    public decimal Amount { get; set; }
    public string? TransactionCode { get; set; }
}

/// <summary>
/// Carter module for payment transaction management endpoints
/// </summary>
public class PaymentTransactionModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/payment-transactions")
            .WithTags("Payment Transactions")
            .WithSummary("Payment Transaction Management")
            .WithDescription("Endpoints for managing payment transactions in the system");

        // GET /payment-transactions - Get payment transactions by order ID
        group.MapGet("", async ([AsParameters] GetPaymentTransactionsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetPaymentTransactions")
        .Produces<PaginationResponse<GetPaymentTransactionResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get payment transactions")
        .WithDescription("Retrieves payment transactions")
        .WithMetadata(new LanguageClientAttribute());

        // GET /payment-transactions/summary - Get payment transaction summary
        group.MapGet("/summary", async (ISender sender) =>
        {
            var query = new GetPaymentTransactionSummaryQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetPaymentTransactionSummary")
        .Produces<Response<GetPaymentTransactionSummaryResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get payment transaction summary")
        .WithDescription("Retrieves the summary of payment transactions")
        .WithMetadata(new LanguageClientAttribute());

        // POST /payment-transactions/orders/{orderId} - Create payment transaction for an order
        group.MapPost("/orders/{orderId:guid}", async (Guid orderId, CreatePaymentTransactionRequest request, ISender sender) =>
        {
            var command = new CreatePaymentTransactionCommand
            {
                OrderId = orderId,
                PaymentMethod = request.PaymentMethod,
                Amount = request.Amount,
                TransactionCode = request.TransactionCode
            };
            
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("CreatePaymentTransaction")
        .Produces<Response<PaymentTransactionResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Create payment transaction for order")
        .WithDescription("Creates a new payment transaction for the specified order ID")
        .WithMetadata(new LanguageClientAttribute());

        // GET /payment-transactions/{id} - Get payment transaction status by order ID
        group.MapGet("/{id:guid}", async (Guid id, ISender sender) =>
        {
            var query = new GetPaymentTransactionByIdQuery(id);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetPaymentTransactionStatus")
        .Produces<Response<PaymentTransactionStatusResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get payment transaction status")
        .WithDescription("Gets the payment transaction status")
        .WithMetadata(new LanguageClientAttribute());

        // GET /payment-transactions/export - Export payment transactions
        group.MapGet("/export", async ([AsParameters] ExportPaymentTransactionCommand query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        }).WithName("ExportPaymentTransactions")
        .Produces<Response<ExportPaymentTransactionResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Export payment transactions")
        .WithDescription("Exports payment transactions based on the specified criteria")
        .WithMetadata(new LanguageClientAttribute());

    }
}
