using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.TicketTypes.Commands;
using Bitexco.Ticket.Application.Features.TicketTypes.Dtos;
using Bitexco.Ticket.Application.Features.TicketTypes.Queries;
using Microsoft.AspNetCore.Mvc;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Request model for submitting price approval
/// </summary>
public record SubmitPriceApprovalRequest(List<TicketPriceDto> ProposedPrices, string Comment);

/// <summary>
/// Request model for approving price changes
/// </summary>
public record ApprovePriceChangesRequest(Guid PriceApprovalId, bool IsApproved, string Comment);

/// <summary>
/// Carter module for ticket type endpoints
/// </summary>
public class TicketTypeModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/ticket-types")
            .WithTags("Ticket Types")
            .WithSummary("Ticket Type Management")
            .WithDescription("Endpoints for managing ticket types in the system");

        // GET /ticket-types - Get all ticket types
        group.MapGet("", async (ISender sender) =>
        {
            var query = new GetAllTicketTypesQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetAllTicketTypes")
        .Produces<Response<List<GetAllTicketTypesResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get all ticket types")
        .WithDescription("Retrieve a list of all active ticket types")
        .WithMetadata(new LanguageClientAttribute());

        // GET /ticket-types/{id} - Get ticket type by ID
        group.MapGet("/{id}", async (Guid id, ISender sender) =>
        {
            var query = new GetTicketTypeByIdQuery(id);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTicketTypeById")
        .Produces<Response<TicketTypeResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get ticket type by ID")
        .WithDescription("Retrieve detailed information for a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());

        // POST /ticket-types - Create new ticket type
        group.MapPost("", async (TicketTypeUpsertDto request, ISender sender) =>
        {
            var command = new CreateTicketTypeCommand(request);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("CreateTicketType")
        .Produces<Response<CreateTicketTypeResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create a new ticket type")
        .WithDescription("Create a new ticket type with the specified details")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /ticket-types/{id} - Update ticket type
        group.MapPut("/{id}", async (Guid id, TicketTypeUpsertDto request, ISender sender) =>
        {
            var command = new UpdateTicketTypeCommand(id, request);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("UpdateTicketType")
        .Produces<Response<UpdateTicketTypeResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update ticket type")
        .WithDescription("Update an existing ticket type with the specified details")
        .WithMetadata(new LanguageClientAttribute());

        // DELETE /ticket-types/{id} - Delete ticket type
        group.MapDelete("/{id}", async (Guid id, ISender sender) =>
        {
            var command = new DeleteTicketTypeCommand(id);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("DeleteTicketType")
        .Produces<Response<DeleteTicketTypeResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete ticket type")
        .WithDescription("Soft delete a ticket type if there are no existing tickets associated with it")
        .WithMetadata(new LanguageClientAttribute());
        
        #region Ticket Type Conditions Endpoints
        
        // GET /ticket-types/{ticketTypeId}/conditions - Get all conditions for a ticket type
        group.MapGet("/{ticketTypeId}/conditions", async (Guid ticketTypeId, ISender sender) =>
        {
            var query = new GetTicketTypeConditionsQuery(ticketTypeId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTicketTypeConditions")
        .Produces<Response<List<TicketTypeConditionResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get ticket type conditions")
        .WithDescription("Retrieve all conditions associated with a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // POST /ticket-types/{ticketTypeId}/conditions - Create new ticket type condition
        group.MapPost("/{ticketTypeId}/conditions", async (Guid ticketTypeId, TicketTypeConditionDto request, ISender sender) =>
        {
            var command = new CreateTicketTypeConditionCommand(
                ticketTypeId,
                request.Adapt<TicketTypeConditionDto>()
                );

            var result = await sender.Send(command);
             return Results.Ok(result);
        })
        .WithName("CreateTicketTypeCondition")
        .Produces<Response<CreateTicketTypeConditionResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create ticket type condition")
        .WithDescription("Create a new condition for a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // PUT /ticket-types/{ticketTypeId}/conditions/{conditionId} - Update ticket type condition
        group.MapPut("/{ticketTypeId}/conditions/{conditionId}", async (Guid ticketTypeId, Guid conditionId, TicketTypeConditionDto request, ISender sender) =>
        {
            var command = new UpdateTicketTypeConditionCommand(
                    ticketTypeId,
                    conditionId,
                    request.Adapt<TicketTypeConditionDto>()
            );
            
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("UpdateTicketTypeCondition")
        .Produces<Response<UpdateTicketTypeConditionResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update ticket type condition")
        .WithDescription("Update an existing condition for a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // DELETE /ticket-types/{ticketTypeId}/conditions/{conditionId} - Delete ticket type condition
        group.MapDelete("/{ticketTypeId}/conditions/{conditionId}", async (Guid ticketTypeId, Guid conditionId, ISender sender) =>
        {
            var command = new DeleteTicketTypeConditionCommand (ticketTypeId, conditionId);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("DeleteTicketTypeCondition")
        .Produces<Response<DeleteTicketTypeConditionResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete ticket type condition")
        .WithDescription("Delete a condition from a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());

        #endregion


        #region Ticket Prices Endpoints
        // GET /ticket-types/{ticketTypeId}/prices - Get all prices for a ticket type
        group.MapGet("/{ticketTypeId}/prices", async (Guid ticketTypeId, ISender sender) =>
        {
            var query = new GetTicketTypePricesQuery(ticketTypeId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTicketTypePrices")
        .Produces<Response<List<TicketPriceResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get ticket type prices")
        .WithDescription("Retrieve all prices associated with a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // POST /ticket-types/{ticketTypeId}/prices - Create new ticket price
        group.MapPost("/{ticketTypeId}/prices", async (Guid ticketTypeId, TicketPriceDto request, ISender sender) =>
        {
            var command = new CreateTicketPriceCommand(
                ticketTypeId,
                request.Adapt<TicketPriceDto>()
            );
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("CreateTicketPrice")
        .Produces<Response<CreateTicketPriceResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create ticket price")
        .WithDescription("Create a new price for a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // PUT /ticket-types/{ticketTypeId}/prices/{priceId} - Update ticket price
        group.MapPut("/{ticketTypeId}/prices/{priceId}", async (Guid ticketTypeId, Guid priceId, TicketPriceDto request, ISender sender) =>
        {
            var command = new UpdateTicketPriceCommand(
                ticketTypeId,
                priceId,
                request.Adapt<TicketPriceDto>()
            );
            
            var result = await sender.Send(command);
            return Results.Ok(result.Adapt<Response<UpdateTicketPriceResult>>());
        })
        .WithName("UpdateTicketPrice")
        .Produces<Response<UpdateTicketPriceResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update ticket price")
        .WithDescription("Update an existing price for a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // DELETE /ticket-types/{ticketTypeId}/prices/{priceId} - Delete ticket price
        group.MapDelete("/{ticketTypeId}/prices/{priceId}", async (Guid ticketTypeId, Guid priceId, ISender sender) =>
        {
            var command = new DeleteTicketPriceCommand(ticketTypeId, priceId );
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("DeleteTicketPrice")
        .Produces<Response<DeleteTicketPriceResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete ticket price")
        .WithDescription("Delete a price from a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // POST /ticket-types/{ticketTypeId}/submit-price-approval - Submit price changes for approval
        group.MapPost("/{ticketTypeId}/submit-price-approval", async (Guid ticketTypeId,
            SubmitPriceApprovalRequest request, ISender sender) =>
        {
            var command = new SubmitPriceApprovalCommand(
                ticketTypeId,
                request.ProposedPrices,
                request.Comment
            );
            
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("SubmitPriceApproval")
        .Produces<Response<SubmitPriceApprovalResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Submit price approval")
        .WithDescription("Submit price changes for approval by an administrator")
        .WithMetadata(new LanguageClientAttribute());
        
        // POST /ticket-types/approve-price-changes - Approve or reject price changes
        group.MapPost("/approve-price-changes", async (ApprovePriceChangesRequest request, ISender sender) =>
        {
            var command = request.Adapt<ApprovePriceChangesCommand>();
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("ApprovePriceChanges")
        .Produces<Response<ApprovePriceChangesResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Approve price changes")
        .WithDescription("Approve or reject submitted price changes by an administrator")
        .WithMetadata(new LanguageClientAttribute());
        
        #endregion
        
        #region Visit Time Rules Endpoints
        
        // GET /ticket-types/{ticketTypeId}/visit-time-rules - Get all visit time rules for a ticket type
        group.MapGet("/{ticketTypeId}/visit-time-rules", async (Guid ticketTypeId, ISender sender) =>
        {
            var query = new GetVisitTimeRulesQuery(ticketTypeId);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetVisitTimeRules")
        .Produces<Response<List<VisitTimeRuleResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get visit time rules")
        .WithDescription("Retrieve all visit time rules associated with a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // POST /ticket-types/{ticketTypeId}/visit-time-rules - Create new visit time rule
        group.MapPost("/{ticketTypeId}/visit-time-rules", async (Guid ticketTypeId, VisitTimeRuleDto request, ISender sender) =>
        {
            var command = new CreateVisitTimeRuleCommand(
                ticketTypeId,
                request
            );
            
            var result = await sender.Send(command);
            
            return Results.Ok(result);
        })
        .WithName("CreateVisitTimeRule")
        .Produces<Response<CreateVisitTimeRuleResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create visit time rule")
        .WithDescription("Create a new visit time rule for a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // PUT /ticket-types/{ticketTypeId}/visit-time-rules/{ruleId} - Update visit time rule
        group.MapPut("/{ticketTypeId}/visit-time-rules/{ruleId}", async (Guid ticketTypeId, Guid ruleId, VisitTimeRuleDto request, ISender sender) =>
        {
            var command = new UpdateVisitTimeRuleCommand(
                ticketTypeId,
                ruleId,
                request
            );
            
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("UpdateVisitTimeRule")
        .Produces<Response<UpdateVisitTimeRuleResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update visit time rule")
        .WithDescription("Update an existing visit time rule for a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        // DELETE /ticket-types/{ticketTypeId}/visit-time-rules/{ruleId} - Delete visit time rule
        group.MapDelete("/{ticketTypeId}/visit-time-rules/{ruleId}", async (Guid ticketTypeId, Guid ruleId, ISender sender) =>
        {
            var command = new DeleteVisitTimeRuleCommand{
                VisitTimeRuleId = ruleId,
                TicketTypeId = ticketTypeId
            };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("DeleteVisitTimeRule")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete visit time rule")
        .WithDescription("Delete a visit time rule from a specific ticket type")
        .WithMetadata(new LanguageClientAttribute());
        
        #endregion
        
        #region Audit Logs Endpoints
        
        // GET /ticket-types/{ticketTypeId}/audit-logs - Get audit logs for a ticket type
        group.MapGet("/{ticketTypeId}/audit-logs", async (
            Guid ticketTypeId,
            [FromQuery] DateTime? fromDate,
            [FromQuery] DateTime? toDate,
            ISender sender) =>
        {
            var query = new GetTicketTypeAuditLogsQuery 
            { 
                TicketTypeId = ticketTypeId,
                FromDate = fromDate,
                ToDate = toDate
            };
            
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("GetTicketTypeAuditLogs")
        .Produces<Response<List<AuditLogResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get ticket type audit logs")
        .WithDescription("Retrieve audit logs for a specific ticket type and its related entities")
        .WithMetadata(new LanguageClientAttribute());
        
        #endregion
    }
}
