using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.TicketPrices.GetCurrentTicketPrices;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for ticket price endpoints
/// </summary>
public class TicketPriceModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/ticket-prices")
            .WithTags("Ticket Prices")
            .WithSummary("Ticket Price Management")
            .WithDescription("Endpoints for managing ticket prices in the system");

        // GET /ticket-prices/current - Get list of ticket prices at the current time
        group.MapGet("/current", async ([AsParameters] GetCurrentTicketPricesQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetCurrentTicketPrices")
        .Produces<Response<List<TicketPriceResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get current ticket prices")
        .WithDescription("Get a list of ticket prices at the current time")
        .WithMetadata(new LanguageClientAttribute());
    }
}
