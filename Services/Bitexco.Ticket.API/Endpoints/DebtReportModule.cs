using Bitexco.Ticket.Application.Features.Reports.DebtReports.GetAgentDebtDetails;
using Bitexco.Ticket.Application.Features.Reports.DebtReports.GetDebtOverview;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for debt report endpoints
/// </summary>
public class DebtReportModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/debt-reports")
            .WithTags("Debt Reports")
            .WithSummary("Debt Report Management")
            .WithDescription("Endpoints for debt reporting and analysis");
        
        // GET /debt-reports/overview - Get debt overview with comparison to previous period
        group.MapGet("/overview", async ([AsParameters] GetDebtOverviewQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetDebtOverview")
        .Produces<Response<DebtOverviewResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get debt overview with comparison")
        .WithDescription(@"Get total debt statistics for the specified date range with comparison to previous period.
        
**Returns:**
- TotalAmountDue: Tổng số tiền mà các đại lý cần trả
- TotalAmountPaid: Tổng số tiền mà các đại lý đã thanh toán  
- AmountDueChangePercent: Phần trăm thay đổi so với cùng kỳ trước đó
- AmountPaidChangePercent: Phần trăm thay đổi so với cùng kỳ trước đó

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /debt-reports/agent-details - Get paginated agent debt details
        group.MapGet("/agent-details", async ([AsParameters] GetAgentDebtDetailsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetAgentDebtDetails")
        .Produces<PaginationResponse<AgentDebtDetailResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get agent debt details with pagination")
        .WithDescription(@"Get detailed debt information for each agent in the specified date range with pagination and sorting.
        
**Returns for each agent:**
- AgentName: Tên đại lý
- AgentCode: Mã đại lý
- OrderCount: Số đơn hàng
- TotalDebtAmount: Tổng công nợ
- TotalPaidAmount: Số tiền đã trả
- TotalDueAmount: Số tiền cần trả

**Parameters:**
- searchTerm: Filter by agent name or code (optional)
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)
- pageIndex: Page number (default: 1)
- pageSize: Records per page (default: 100)
- sortBy: Field to sort by (agentname, agentcode, ordercount, totaldebtamount, totalpaidamount, totaldueamount)
- isDescending: Sort direction (true for desc, false for asc)")
        .WithMetadata(new LanguageClientAttribute());
    }
}
