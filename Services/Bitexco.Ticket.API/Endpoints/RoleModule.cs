namespace Bitexco.Ticket.API.Endpoints;

using Bitexco.Ticket.Application.Features.Roles.Queries.GetUserRoleFunctions;
using Bitexco.Ticket.Application.Features.Roles.Queries.GetRoles;
using Bitexco.Ticket.Application.Features.Roles.Queries.GetRoleFunctions;
using Bitexco.Ticket.Application.Features.Roles.Commands.UpdateRoleFunctions;
using Bitexco.Ticket.Application.Dtos;
using Microsoft.AspNetCore.Authorization;

/// <summary>
/// Request body for updating role functions
/// </summary>
public record UpdateRoleFunctionsRequest
{
    public List<UpdateRoleFunctionRequestDto> RoleFunctionList { get; init; } = [];
}

/// <summary>
/// Role Module containing endpoints for role and function management
/// </summary>
public class RoleModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/roles").WithTags("Roles");

        // 1. Get user role functions for FE menu (requires user management read permission)
        group.MapGet("/user-functions", async (ISender sender) =>
        {
            var query = new GetUserRoleFunctionsQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .RequireAuthorization()
        .WithName("GetUserRoleFunctions")
        .Produces<Response<List<UserRoleFunctionResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .WithSummary("Get user role functions")
        .WithDescription("Retrieves the list of functions available to the current user for FE menu display. " +
                        "Admin users get all functions, POS users get functions based on their role permissions. " +
                        "Requires: User Management Read (UMR) permission.")
        .WithMetadata(new LanguageClientAttribute());

        // 2. Get all roles (only POS type, requires role management read permission)
        group.MapGet("", async (ISender sender) =>
        {
            var query = new GetRolesQuery();
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = "RMR" })
        .WithName("GetRoles")
        .Produces<Response<List<RoleResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .WithSummary("Get all POS roles")
        .WithDescription("Retrieves all roles with type 'pos' for role management. " +
                        "Only returns roles that can be managed through the system. " +
                        "Requires: Role Management Read (RMR) permission.")
        .WithMetadata(new LanguageClientAttribute());

        // 3. Get role functions with permissions (requires role management read permission)
        group.MapGet("/{id:guid}/functions", async (Guid id, ISender sender) =>
        {
            var query = new GetRoleFunctionsQuery(id);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = "RMR" })
        .WithName("GetRoleFunctions")
        .Produces<Response<GetRoleFunctionsResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get role functions and permissions")
        .WithDescription("Gets the functions and permissions assigned to a specific role. " +
                        "Returns all available functions, permissions, and current role-function assignments. " +
                        "Requires: Role Management Read (RMR) permission.")
        .WithMetadata(new LanguageClientAttribute());

        // 4. Update role functions with permissions (requires role management update permission)
        group.MapPut("/{id:guid}/functions", async (Guid id, UpdateRoleFunctionsRequest body, ISender sender) =>
        {
            var command = new UpdateRoleFunctionsCommand(id, body.RoleFunctionList);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = "RMU" }) // Role Management Update
        .WithName("UpdateRoleFunctions")
        .Produces<Response<UpdateRoleFunctionsResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Update role functions and permissions")
        .WithDescription("Updates the functions and permissions assigned to a role. " +
                        "Replaces all existing role-function assignments with the provided list. " +
                        "Requires: Role Management Update (RMU) permission.")
        .WithMetadata(new LanguageClientAttribute());
    }
}
