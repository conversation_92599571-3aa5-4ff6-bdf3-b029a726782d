using Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetCommissionOverview;
using Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetTopCommissions;
using Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetAgentCommissionDetails;
using Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetCollaboratorCommissionDetails;
using Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetAgentDiscountDetails;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for commission report endpoints
/// </summary>
public class CommissionReportModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/commission-reports")
            .WithTags("Commission Reports")
            .WithSummary("Commission Report Management")
            .WithDescription("Endpoints for commission and discount reporting and analysis");

        // GET /commission-reports/overview - Get commission overview with comparison to previous period
        group.MapGet("/overview", async ([AsParameters] GetCommissionOverviewQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetCommissionOverview")
        .Produces<Response<CommissionOverviewResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get commission overview with comparison")
        .WithDescription(@"Get commission and discount overview for the specified date range with comparison to previous period.        
**Returns:**
- TotalPartnerCost: Tổng chi phí đối tác (tổng cộng tất cả chi phí hoa hồng và chiết khấu)
- AgentDiscount: Chiết khấu Đại lý
- CollaboratorCommissionPaid: Hoa hồng CTV đã chi
- CollaboratorCommissionRecognized: Hoa hồng CTV ghi nhận
- AgentCommissionPaid: Hoa hồng Đại lý đã chi
- AgentCommissionRecognized: Hoa hồng Đại lý ghi nhận
- Percentage changes: Tỉ lệ % thay đổi so với cùng kỳ trước đó cho tất cả các giá trị trên

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /commission-reports/top - Get top 3 commission details by agent type
        group.MapGet("/top", async ([AsParameters] GetTopCommissionsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetTopCommissions")
        .Produces<Response<List<TopCommissionResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get top 3 commission details by agent type")
        .WithDescription(@"Get top 3 commission details by CTV (collaborator) or Agency (agent) in the specified date range.        
**Returns for each of top 3:**
- Name: Tên CTV hoặc Đại lý
- Code: Mã CTV hoặc Đại lý
- Revenue: Doanh thu
- CommissionPercentage: Phần trăm hoa hồng
- CommissionRecognized: Hoa hồng ghi nhận
- CommissionPaid: Hoa hồng đã chi

**Parameters:**
- dateFrom: Start date for calculation (required)
- dateTo: End date for calculation (required)
- type: Agent type filter - 'ctv' for collaborator or 'agency' for agent (required)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /commission-reports/agent-commission-details - Get paginated list of agent commission details
        group.MapGet("/agent-commission-details", async ([AsParameters] GetAgentCommissionDetailsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetAgentCommissionDetails")
        .Produces<PaginationResponse<AgentCommissionDetailResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get agent commission details with pagination")
        .WithDescription(@"Get detailed commission information for agents in the specified date range with pagination and sorting.
        
**Returns for each agent:**
- Name: Tên đại lý
- Code: Mã đại lý
- Revenue: Doanh thu
- CommissionPercentage: Phần trăm hoa hồng
- OrderCount: Số đơn hàng
- CommissionRecognized: Hoa hồng ghi nhận
- CommissionPaid: Hoa hồng đã chi

**Parameters:**
- dateFrom: Start date for calculation (optional)
- dateTo: End date for calculation (optional)
- search: Search by agent name or code (optional)
- pageIndex: Page number (default: 1)
- pageSize: Records per page (default: 100)
- sortBy: Field to sort by (name, code, revenue, commissionpercentage, ordercount, commissionrecognized, commissionpaid)
- isDescending: Sort direction (true for desc, false for asc)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /commission-reports/collaborator-commission-details - Get paginated list of collaborator commission details
        group.MapGet("/collaborator-commission-details", async ([AsParameters] GetCollaboratorCommissionDetailsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetCollaboratorCommissionDetails")
        .Produces<PaginationResponse<CollaboratorCommissionDetailResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get collaborator commission details with pagination")
        .WithDescription(@"Get detailed commission information for collaborators in the specified date range with pagination and sorting.
        
**Returns for each collaborator:**
- Name: Tên cộng tác viên
- Code: Mã cộng tác viên
- Revenue: Doanh thu
- CommissionPercentage: Phần trăm hoa hồng
- OrderCount: Số đơn hàng
- CommissionRecognized: Hoa hồng ghi nhận
- CommissionPaid: Hoa hồng đã chi

**Parameters:**
- dateFrom: Start date for calculation (optional)
- dateTo: End date for calculation (optional)
- search: Search by collaborator name or code (optional)
- pageIndex: Page number (default: 1)
- pageSize: Records per page (default: 100)
- sortBy: Field to sort by (name, code, revenue, commissionpercentage, ordercount, commissionrecognized, commissionpaid)
- isDescending: Sort direction (true for desc, false for asc)")
        .WithMetadata(new LanguageClientAttribute());

        // GET /commission-reports/agent-discount-details - Get paginated list of agent discount details
        group.MapGet("/agent-discount-details", async ([AsParameters] GetAgentDiscountDetailsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetAgentDiscountDetails")
        .Produces<PaginationResponse<AgentDiscountDetailResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get agent discount details with pagination")
        .WithDescription(@"Get detailed discount information for agents in the specified date range with pagination and sorting.
        
**Returns for each agent:**
- Name: Tên đại lý
- Code: Mã đại lý
- OrderCount: Số đơn hàng
- Revenue: Doanh thu
- Discount: Chiết khấu

**Parameters:**
- dateFrom: Start date for calculation (optional)
- dateTo: End date for calculation (optional)
- search: Search by agent name or code (optional)
- pageIndex: Page number (default: 1)
- pageSize: Records per page (default: 100)
- sortBy: Field to sort by (name, code, ordercount, revenue, discount)
- isDescending: Sort direction (true for desc, false for asc)")
        .WithMetadata(new LanguageClientAttribute());
    }
}