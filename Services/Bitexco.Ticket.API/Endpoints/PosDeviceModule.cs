using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.PosDevices.CreatePosDevice;
using Bitexco.Ticket.Application.Features.PosDevices.DeletePosDevice;
using Bitexco.Ticket.Application.Features.PosDevices.DisconnectionNumber;
using Bitexco.Ticket.Application.Features.PosDevices.GetNewConnectionNumberById;
using Bitexco.Ticket.Application.Features.PosDevices.GetPosDeviceById;
using Bitexco.Ticket.Application.Features.PosDevices.GetPosDeviceBySerial;
using Bitexco.Ticket.Application.Features.PosDevices.GetPosDeviceConnectionStatusById;
using Bitexco.Ticket.Application.Features.PosDevices.GetPosDevices;
using Bitexco.Ticket.Application.Features.PosDevices.UpdateNewConnectionNumber;
using Bitexco.Ticket.Application.Features.PosDevices.UpdatePosDevice;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for POS device management endpoints
/// </summary>
public class PosDeviceModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/pos-devices")
            .WithTags("POS Devices")
            .WithSummary("POS Device Management")
            .WithDescription("Endpoints for managing Point of Sale devices and kiosks in the system");

        // GET /pos-devices - Get all POS devices
        group.MapGet("", async (ISender sender) =>
        {
            var result = await sender.Send(new GetPosDevicesQuery());
            return result;
        })
        .WithName("GetPosDevices")
        .Produces<Response<List<PosDeviceResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get all POS devices")
        .WithDescription("Get all POS devices in the system")
        .WithMetadata(new LanguageClientAttribute());

        // GET /pos-devices/{id} - Get POS device details by ID
        group.MapGet("/{id}", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetPosDeviceByIdQuery { Id = id });
            return result;
        })
        .WithName("GetPosDeviceById")
        .Produces<Response<PosDeviceResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get POS device by ID")
        .WithDescription("Get information for a specific POS device")
        .WithMetadata(new LanguageClientAttribute());

        // GET /pos-devices/{id}/connecttion-status - Get connection status of POS device
        group.MapGet("/{id}/connection-status", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetPosDeviceConnectionStatusByIdQuery { Id = id });
            return result;
        })
        .WithName("GetPosDeviceConnectionStatus")
        .Produces<Response<GetPosDeviceConnectionStatusByIdResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get POS device connection status")
        .WithDescription("Check if a specific POS device is currently connected")
        .WithMetadata(new LanguageClientAttribute());

        // GET /pos-devices/serial/{serial} - Get POS device by serial number
        group.MapGet("/serial/{serial}", async (string serial, ISender sender) =>
        {
            var result = await sender.Send(new GetPosDeviceBySerialQuery { SerialNumber = serial });
            return result;
        })
        .WithName("GetPosDeviceBySerial")
        .Produces<Response<PosDeviceResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get POS device by serial number")
        .WithDescription("Get information for a specific POS device by its serial number")
        .WithMetadata(new LanguageClientAttribute());

        // GET /pos-devices/{id}/new-connection-number - Get new connection number for POS device
        group.MapGet("/{id}/new-connection-number", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetNewConnectionNumberByIdQuery { Id = id });
            return result;
        })
        .WithName("GetNewConnectionNumber")
        .Produces<Response<string>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get new connection number")
        .WithDescription("Get a new connection number for a POS device by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /pos-devices/new-connection - Update new serial number for POS device
        group.MapPut("/new-connection", async (UpdateNewConnectionNumberCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return result;
        })
        .WithName("UpdateNewConnectionNumber")
        .Produces<Response<PosDeviceResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update new connection number")
        .WithDescription("Update the new connection number for a POS device by its session code and serial number")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /pos-devices/{id}/disconnection - Update disconnection number for POS device
        group.MapPut("/{id}/disconnection", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new DisconnectionNumberCommand { Id = id });
            return result;
        })
        .WithName("DisconnectionNumber")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update disconnection number")
        .WithDescription("Update the disconnection number for a POS device by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // POST /pos-devices - Create a new POS device
        group.MapPost("", async (PosDeviceDto request, ISender sender) =>
        {
            var command = new CreatePosDeviceCommand { PosDevice = request };
            var result = await sender.Send(command);
            return result;
        })
        .WithName("CreatePosDevice")
        .Produces<Response<CreatePosDeviceResult>>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create POS device")
        .WithDescription("Create a new Point of Sale device or kiosk")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /pos-devices/{id} - Update an existing POS device
        group.MapPut("/{id}", async (Guid id, PosDeviceDto request, ISender sender) =>
        {
            var command = new UpdatePosDeviceCommand { Id = id, PosDevice = request };
            var result = await sender.Send(command);
            return result;
        })
        .WithName("UpdatePosDevice")
        .Produces<Response<UpdatePosDeviceResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update POS device")
        .WithDescription("Update an existing POS device's information")
        .WithMetadata(new LanguageClientAttribute());

        // DELETE /pos-devices/{id} - Delete a POS device
        group.MapDelete("/{id}", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new DeletePosDeviceCommand { Id = id });
            return result;
        })
        .WithName("DeletePosDevice")
        .Produces<Response<object>>(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete POS device")
        .WithDescription("Remove a POS device from the system (only if no active orders or shifts)")
        .WithMetadata(new LanguageClientAttribute());
    }
}
