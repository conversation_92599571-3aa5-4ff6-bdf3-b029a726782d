namespace Bitexco.Ticket.API.Endpoints;

using Bitexco.Ticket.Application.Features.Auth.Login;
using Bitexco.Ticket.Application.Features.Auth.Logout;
using Bitexco.Ticket.Application.Features.Auth.RefreshToken;
using Bitexco.Ticket.Application.Features.Auth.ForgotPasswordBySession;
using Bitexco.Ticket.Application.Features.Auth.ResetPasswordBySession;
using Bitexco.Ticket.Application.Features.Auth.Registers;

public record RegisterRequest
{
    public string Email { get; init; } = string.Empty;
    public string Password { get; init; } = string.Empty;
    public string FullName { get; init; } = string.Empty;
    public string PhoneNumber { get; init; } = string.Empty;
}

public record LoginRequest
{
    public string Email { get; init; } = string.Empty;
    public string Password { get; init; } = string.Empty;
}

public record RefreshTokenRequest
{
    public string RefreshToken { get; init; } = string.Empty;
}

public record LogoutRequest
{
    public string RefreshToken { get; init; } = string.Empty; 
}

public record ForgotPasswordBySessionRequest
{
    public string Account { get; init; } = string.Empty;
}

public record ResetPasswordBySessionRequest
{
    public string Account { get; init; } = string.Empty;
    public string Password { get; init; } = string.Empty;
    public Guid Session { get; init; }
}

public class AuthModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/auth").WithTags("Auth");

        group.MapPost("/register", async (RegisterRequestDto User, ISender sender) =>
        {
            var command = new RegisterCommand(User);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .WithName("Register")
        .Produces<Response<RegisterResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Register User")
        .WithDescription("Registers a new user with the provided details")
        .WithMetadata(new LanguageClientAttribute());

        group.MapPost("/login", async (LoginRequest request, ISender sender) =>
        {
            var command = request.Adapt<LoginCommand>();

            var result = await sender.Send(command);

            return Results.Ok(result);
        })
        .WithName("Login")
        .Produces<Response<LoginResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Authenticate User")
        .WithDescription("Authenticates a user and returns JWT tokens")
        .WithMetadata(new LanguageClientAttribute());

        group.MapPost("/refresh", async (RefreshTokenRequest request, ISender sender) =>
        {
            var command = request.Adapt<RefreshTokenCommand>();

            var result = await sender.Send(command);

            return Results.Ok(result);
        })
        .WithName("RefreshToken")
        .Produces<Response<LoginResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Refresh Token")
        .WithDescription("Validates refresh token and issues new access and refresh tokens")
        .WithMetadata(new LanguageClientAttribute());

        group.MapPost("/logout", async (LogoutRequest request, ISender sender) =>
        {
            var command = request.Adapt<LogoutCommand>();

            var result = await sender.Send(command);

            return Results.Ok(result);
        })
        .WithName("Logout")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Logout User")
        .WithDescription("Invalidates the refresh token to logout the user")
        .WithMetadata(new LanguageClientAttribute());

        group.MapPost("/forgot-password-by-session", async (ForgotPasswordBySessionRequest request, ISender sender) =>
        {
            var command = request.Adapt<ForgotPasswordBySessionCommand>();

            var result = await sender.Send(command);

            return Results.Ok(result);
        })
        .WithName("ForgotPasswordBySession")
        .Produces<Response<ForgotPasswordResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Forgot password")
        .WithDescription("Initiates forgot password process and creates a session for password reset")
        .WithMetadata(new LanguageClientAttribute());

        group.MapPatch("/reset-password-by-session", async (ResetPasswordBySessionRequest request, ISender sender) =>
        {
            var command = request.Adapt<ResetPasswordBySessionCommand>();

            var result = await sender.Send(command);

            return Results.Ok(result);
        })
        .WithName("ResetPasswordBySession")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Validate new password")
        .WithDescription("Resets user password using forgot password session")
        .WithMetadata(new LanguageClientAttribute());
    }
}
