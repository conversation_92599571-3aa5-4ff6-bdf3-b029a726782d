namespace Bitexco.Ticket.API.Endpoints;

using Bitexco.Ticket.Application.Features.PosManagement.Commands.DeletePosAccount;
using Bitexco.Ticket.Application.Features.PosManagement.Commands.UpdatePosAccount;
using Bitexco.Ticket.Application.Features.PosManagement.Dtos;
using Bitexco.Ticket.Application.Features.PosManagement.Queries.GetPosAccounts;
using Bitexco.Ticket.Application.Features.PosManagement.Queries.GetPosAccountById;
using Bitexco.Ticket.Domain.Enums;

/// <summary>
/// Request DTOs for POS Management API
/// </summary>
public record UpdatePosAccountRequest
{
    public PosRoles? PosRole { get; set; }
    public string? Password { get; set; }
    public string? FullName { get; set; }
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
}

/// <summary>
/// POS Management Module containing endpoints for managing POS accounts
/// </summary>
public class PosManagementModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/pos-accounts")
            .WithTags("POS Management")
            .WithSummary("POS Account Management")
            .WithDescription("Endpoints for managing POS accounts in the system");

        // GET /pos-accounts - Get paginated POS accounts with role sorting
        group.MapGet("", async ([AsParameters] GetPosAccountsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        }).RequireAuthorization()
        .WithName("GetPosAccounts")
        .Produces<PaginationResponse<PosAccountResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .WithSummary("Get POS accounts")
        .WithDescription("Retrieves a paginated list of POS accounts with role-based sorting")
        .WithMetadata(new LanguageClientAttribute());

        // GET /pos-accounts/{id} - Get POS account details by ID
        group.MapGet("/{id:guid}", async (Guid id, ISender sender) =>
        {
            var query = new GetPosAccountByIdQuery { Id = id };
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .RequireAuthorization()
        .WithName("GetPosAccountById")
        .Produces<Response<PosAccountResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get POS account by ID")
        .WithDescription("Retrieves detailed information for a specific POS account by its ID")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /pos-accounts/{id} - Update POS account information
        group.MapPut("/{id:guid}", async (Guid id, UpdatePosAccountRequest request, ISender sender) =>
        {
            var posAccount = request.Adapt<PosAccountDto>();

            var command = new UpdatePosAccountCommand
            {
                Id = id,
                PosAccount = posAccount
            };

            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .RequireAuthorization() 
        .WithName("UpdatePosAccount")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Update POS account")
        .WithDescription("Updates POS account information including role, password, and personal details")
        .WithMetadata(new LanguageClientAttribute());
        
        // DELETE /pos-accounts/{id} - Soft delete POS account
        group.MapDelete("/{id:guid}", async (Guid id, ISender sender) =>
        {
            var command = new DeletePosAccountCommand { Id = id };
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .RequireAuthorization() 
        .WithName("DeletePosAccount")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Delete POS account")
        .WithDescription("Soft deletes a POS account from the system")
        .WithMetadata(new LanguageClientAttribute());
    }
}