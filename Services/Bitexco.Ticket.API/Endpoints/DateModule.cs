using Bitexco.Ticket.Application.Features.DateManagement.GetDateRangeForPeriod;

namespace Bitexco.Ticket.API.Endpoints
{
    public class DateModule : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            var group = app.MapGroup("/date")
                .WithTags("Date Management")
                .WithSummary("Date Management Endpoints")
                .WithDescription("Endpoints for managing date-related operations");
            
            // GET /date/range - Get date range for period
            group.MapGet("/range", async ([AsParameters] GetDateRangeForPeriodQuery request, ISender sender) =>
            {
                var result = await sender.Send(request);
                return result;
            })
            .WithName("GetDateRangeForPeriod")
            .Produces<Response<DateRangeResponseDto>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Get date range for period")
            .WithDescription(@"Calculate start and end dates based on period type and year.
        
**Period Types:**
- Today (0): Hôm nay - always uses current date, ignores year parameter
- ThisWeek (1): Tuần này - always uses current week, ignores year parameter  
- ThisMonth (2): Tháng này - always uses current month, ignores year parameter
- QuarterI (3): Quí I (Jan-Mar) - uses year parameter
- QuarterII (4): Quí II (Apr-Jun) - uses year parameter
- QuarterIII (5): Quý III (Jul-Sep) - uses year parameter
- QuarterIV (6): Quý IV (Oct-Dec) - uses year parameter
- ThisYear (7): Năm này - uses year parameter

**Year Parameter:**
- Optional, defaults to current year
- Only affects Quarter I-IV and ThisYear periods
- Today, ThisWeek, ThisMonth always use current year regardless of year parameter")
            .WithMetadata(new LanguageClientAttribute());
        }
    }
}