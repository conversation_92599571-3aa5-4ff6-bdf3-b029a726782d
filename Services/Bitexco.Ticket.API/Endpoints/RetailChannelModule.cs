using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.RetailChannels.CreateRetailChannel;
using Bitexco.Ticket.Application.Features.RetailChannels.DeleteRetailChannel;
using Bitexco.Ticket.Application.Features.RetailChannels.GetRetailChannelById;
using Bitexco.Ticket.Application.Features.RetailChannels.GetRetailChannels;
using Bitexco.Ticket.Application.Features.RetailChannels.UpdateRetailChannel;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for retail channel management endpoints
/// </summary>
public class RetailChannelModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/retail-channels")
            .WithTags("Retail Channels")
            .WithSummary("Retail Channel Management")
            .WithDescription("Endpoints for managing retail channels in the system");

        // GET /retail-channels - Get all retail channels
        group.MapGet("", async (ISender sender) =>
        {
            var result = await sender.Send(new GetRetailChannelsQuery());
            return result;
        })
        .WithName("GetRetailChannels")
        .Produces<Response<List<RetailChannelResponseDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get all retail channels")
        .WithDescription("Get all retail channels in the system")
        .WithMetadata(new LanguageClientAttribute());

        // GET /retail-channels/{id} - Get retail channel details by ID
        group.MapGet("/{id}", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetRetailChannelByIdQuery { Id = id });
            return result;
        })
        .WithName("GetRetailChannelById")
        .Produces<Response<RetailChannelResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get retail channel by ID")
        .WithDescription("Get information for a specific retail channel")
        .WithMetadata(new LanguageClientAttribute());

        // POST /retail-channels - Create a new retail channel
        group.MapPost("", async (RetailChannelDto request, ISender sender) =>
        {
            var command = new CreateRetailChannelCommand { RetailChannel = request };
            var result = await sender.Send(command);
            return result;
        })
        .WithName("CreateRetailChannel")
        .Produces<Response<CreateRetailChannelResult>>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Create retail channel")
        .WithDescription("Create a new retail channel")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /retail-channels/{id} - Update an existing retail channel
        group.MapPut("/{id}", async (Guid id, RetailChannelDto request, ISender sender) =>
        {
            var command = new UpdateRetailChannelCommand { Id = id, RetailChannel = request };
            var result = await sender.Send(command);
            return result;
        })
        .WithName("UpdateRetailChannel")
        .Produces<Response<UpdateRetailChannelResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Update retail channel")
        .WithDescription("Update an existing retail channel's information")
        .WithMetadata(new LanguageClientAttribute());

        // DELETE /retail-channels/{id} - Delete a retail channel
        group.MapDelete("/{id}", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new DeleteRetailChannelCommand { Id = id });
            return result;
        })
        .WithName("DeleteRetailChannel")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Delete retail channel")
        .WithDescription("Remove a retail channel from the system (soft delete)")
        .WithMetadata(new LanguageClientAttribute());
    }
}