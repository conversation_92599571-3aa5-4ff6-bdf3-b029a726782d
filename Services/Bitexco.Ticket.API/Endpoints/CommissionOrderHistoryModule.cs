using Bitexco.Ticket.Application.Features.CommissionOrderHistories.ConfirmPayments;
using Bitexco.Ticket.Application.Features.CommissionOrderHistories.GetCommissionOrderHistoryDetail;
using Bitexco.Ticket.Application.Features.CommissionOrderHistories.GetCommissionOrderHistories;
using Bitexco.Ticket.Application.Features.CommissionOrderHistories.ExportCommissionOrderHistories;

namespace Bitexco.Ticket.API.Endpoints;

/// <summary>
/// Carter module for Commission Order History management endpoints
/// </summary>
public class CommissionOrderHistoryModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/commission-order-histories")
            .WithTags("Commission Order History")
            .WithSummary("Commission Order History Management")
            .WithDescription("Endpoints for managing commission order history and payment confirmations");

        // PUT /commission-order-histories/confirm-payments - Xá<PERSON> nhận thanh toán cho nhiều commission order history
        group.MapPut("/confirm-payments", async (ConfirmPaymentsRequestDto request, ISender sender) =>
        {
            var command = new ConfirmPaymentsCommand { Request = request };
            var result = await sender.Send(command);
            return result;
        })
        .WithName("ConfirmCommissionPayments")
        .Produces<Response<ConfirmPaymentsResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Confirm payments for multiple commission order histories")
        .WithDescription("Process payment confirmations for multiple commission order histories at once with payment proof")
        .WithMetadata(new LanguageClientAttribute());

        // GET /commission-order-histories/{id} - Get chi tiết Commission Order History
        group.MapGet("/{id}", async (Guid id, ISender sender) =>
        {
            var query = new GetCommissionOrderHistoryDetailQuery { Id = id };
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetCommissionOrderHistoryDetail")
        .Produces<Response<CommissionOrderHistoryDetailResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get commission order history detail")
        .WithDescription("Get detailed information of a specific commission order history including order, tickets, customer and agent info")
        .WithMetadata(new LanguageClientAttribute());

        // GET /commission-order-histories - Get all commission order histories với phân trang
        group.MapGet("", async (
            [AsParameters] GetCommissionOrderHistoriesQuery query, 
            ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetCommissionOrderHistories")
        .Produces<PaginationResponse<CustomCommissionOrderHistoryResponseDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get paginated commission order histories")
        .WithDescription("Get paginated list of commission order histories with filtering options by search term, status, date and agent type")
        .WithMetadata(new LanguageClientAttribute());

        // GET /commission-order-histories/export - Export commission order histories
        group.MapGet("/export", async (
            [AsParameters] GetCommissionOrderHistoriesQuery query,
            ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        }).WithName("ExportCommissionOrderHistories")
        .Produces<ExportCommissionOrderHistoryResponse>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Export commission order histories")
        .WithDescription("Export commission order histories to an Excel file with filtering options by search term, status, date and agent type")
        .WithMetadata(new LanguageClientAttribute());
    }
}
