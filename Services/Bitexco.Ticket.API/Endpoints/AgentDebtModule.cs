using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.AgentDebts.GetAgentDebts;
using Bitexco.Ticket.Application.Features.AgentDebts.ProcessDebtPayment;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.API.Endpoints;

public record ProcessDebtPaymentRequest
{
    public DateTime? PaidAt { get; init; }
    public AgentDebtPaymentMethod? PaymentMethod { get; init; }
    public string? PaymentProofFilePath { get; init; }
}

/// <summary>
/// Carter module for agent debt management endpoints
/// </summary>
public class AgentDebtModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/agent-debts")
            .WithTags("Agent Debts")
            .WithSummary("Agent Debt Management")
            .WithDescription("Endpoints for managing agent debts in the system");

        // GET /agent-debts - Get paginated list of agent debts
        group.MapGet("", async ([AsParameters] GetAgentDebtsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return result;
        })
        .WithName("GetAgentDebts")
        .Produces<PaginationResponse<GetAgentDebtResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Get Agent Debts")
        .WithDescription("Get a paginated list of agent debts with optional filtering and sorting")
        .WithMetadata(new LanguageClientAttribute());

        // PUT /agent-debts/{id}/process-payment - Process a payment towards an agent debt
        group.MapPut("/{id}/process-payment", async (Guid id, ProcessDebtPaymentRequest request, ISender sender) =>
        {
            var command = request.Adapt<ProcessDebtPaymentCommand>();
            command.Id = id; // Set the debt ID from the route
            var result = await sender.Send(command);
            return result;
        })
        .WithName("ProcessDebtPayment")
        .Accepts<ProcessDebtPaymentRequest>("application/json")
        .Produces<Response<object>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Process Debt Payment")
        .WithDescription("Process a payment towards an agent debt")
        .WithMetadata(new LanguageClientAttribute());
    }
}
