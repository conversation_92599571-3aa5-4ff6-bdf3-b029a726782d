﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.PromotionCampaign.Commands;
using Bitexco.Ticket.Application.Features.PromotionCampaign.Queries;

namespace Bitexco.Ticket.API.Endpoints
{
    public class PromotionCampaignModule : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            var group = app.MapGroup("/promotion-campaigns")
                .WithTags("Promotion Campaigns")
                .WithSummary("Promotion Campaign Management")
                .WithDescription("Endpoints for managing promotion campaigns in the system");

            // POST /promotion-campaigns - Create a new promotion campaign
            group.MapPost("", async (CreatePromotionCampaignCommand request, ISender sender) =>
            {
                var command = request;
                var result = await sender.Send(command);
                return Results.Ok(result);
            }).WithName("CreatePromotionCampaign")
            .Produces<Response<CreatePromotionCampaignResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)

            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Create a new promotion campaign")
            .WithDescription("Create a new promotion campaign with the provided details")
            .WithMetadata(new LanguageClientAttribute());

            // PUT /promotion-campaigns/{id} - Update an existing promotion campaign
            group.MapPatch("/{id:guid}/active", async (Guid id, ISender sender) =>
            {
                var result = await sender.Send(new ActivePromotionCampaignCommand { Id = id });
                return Results.Ok(result);
            }).WithName("PatchPromotionCampaign")
            .Produces<Response<PromotionCampaignResponseDto>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Activate/Deactivate a promotion campaign")
            .WithDescription("Activate or deactivate a promotion campaign by its ID")
            .WithMetadata(new LanguageClientAttribute());

            // GET /promotion-campaigns - Get all promotion campaigns with pagination
            group.MapGet("", async ([AsParameters] PromotionCampaignQuery query, ISender sender) =>
            {
                var result = await sender.Send(query);
                return Results.Ok(result);
            }).WithName("GetPromotionCampaigns")
            .Produces<Response<List<PromotionCampaignResponseDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Get all promotion campaigns")
            .WithDescription("Retrieve all promotion campaigns with pagination support")
            .WithMetadata(new LanguageClientAttribute());

            // DELETE /promotion-campaigns/{id} - Delete a promotion campaign by ID
            group.MapDelete("/{id:guid}", async (Guid id, ISender sender) =>
            {
                var command = new DeletePromotionCampaignCommand { Id = id };
                var result = await sender.Send(command);
                return Results.Ok(result);
            }).WithName("DeletePromotionCampaign")
            .Produces<Response<object>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Delete a promotion campaign")
            .WithDescription("Delete a promotion campaign by its ID")
            .WithMetadata(new LanguageClientAttribute());

            // PUT /promotion-campaigns
            group.MapPut("/{id:guid}", async (Guid id, UpdatePromotionCampaignCommand request, ISender sender) =>
            {
                request.Id = id;
                var result = await sender.Send(request);
                return Results.Ok(result);
            }).WithName("UpdatePromotionCampaign")
            .Produces<Response<PromotionCampaignResponseDto>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesProblem(StatusCodes.Status500InternalServerError)
            .WithSummary("Update a promotion campaign")
            .WithDescription("Update a promotion campaign by its ID")
            .WithMetadata(new LanguageClientAttribute());
        }
    }
}