﻿using Bitexco.Ticket.API;
using Bitexco.Ticket.API.Extensions;
using Bitexco.Ticket.Application;
using Bitexco.Ticket.Infrastructure;
using Bitexco.Ticket.Infrastructure.Data.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services
    .AddApplicationServices(builder.Configuration)
    .AddInfrastructureServices(builder.Configuration)
    .AddApiServices(builder.Configuration, builder.Host);

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseApiServices();

if (app.Environment.IsDevelopment())
{
    await app.InitialiseDatabaseAsync();
}

// Initialize Language Services (LanguageDataStorage + ExceptionMessageHelper)
await app.InitializeLanguageServicesAsync();

app.Run();
