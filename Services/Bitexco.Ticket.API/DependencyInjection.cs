﻿using BuildingBlocks.Exceptions.Handler;
using BuildingBlocks.HttpClients.Discords;
using Bitexco.Ticket.API.ApiFilters;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.OpenApi.Models;
using Serilog.Events;
using Serilog;
using Serilog.Sinks.Discord;
using Refit;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Security.Claims;

namespace Bitexco.Ticket.API;

public static class DependencyInjection
{
    public static IServiceCollection AddApiServices(this IServiceCollection services,
        IConfiguration configuration,
        ConfigureHostBuilder host)
    {
        // Log configuration
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.Async(a => a.Console())
            .WriteTo.Async(a => a.Discord(
                ulong.Parse(configuration["DISCORD_LOGGING_WEBHOOK_ID"]!),
                configuration["DISCORD_LOGGING_WEBHOOK_TOKEN"],
                restrictedToMinimumLevel: LogEventLevel.Error))
            .CreateLogger();

        host.UseSerilog();

        services.AddHttpContextAccessor();

        // Add swagger
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(opt =>
        {
            opt.SwaggerDoc("v1", new OpenApiInfo { Title = "Bitexco.Ticket.API", Version = "v1" });
            opt.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                In = ParameterLocation.Header,
                Description = "Please enter token",
                Name = "Authorization",
                Type = SecuritySchemeType.Http,
                BearerFormat = "JWT",
                Scheme = "bearer"
            });
            opt.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // Add Language Client Filter
            opt.OperationFilter<LanguageClientFilter>();
        });

        services.AddCarter();
        services.AddExceptionHandler<CustomExceptionHandler>();
        services.AddHealthChecks()
            .AddNpgSql(configuration.GetConnectionString("Database")!);

        //Refit client
        services.AddRefitClient<IDiscordWebhookClient>()
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri(configuration["DISCORD_LOGGING_WEBHOOK_URL"]!);
            });

        services.AddCors(options =>
        {
            options.AddPolicy(name: "CorsPolicy",
                            policy =>
                            {
                                policy.AllowAnyOrigin();
                                policy.AllowAnyHeader();
                                policy.AllowAnyMethod();
                            });
        });

        // Configure JWT authentication
        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.SaveToken = true;
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                ValidIssuer = configuration["JwtSettings:Issuer"],
                ValidateIssuer = true,
                ValidAudience = configuration["JwtSettings:Audience"],
                ValidateAudience = true,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero,
                RoleClaimType = ClaimTypes.Role,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(
                    configuration["JwtSettings:Key"] ?? throw new InvalidOperationException("JWT key is not configured")))
            };
        });

        services.AddAuthorizationBuilder()
            .AddPolicy("AdminOnly", policy => policy.RequireRole("ADMIN"))
            .AddPolicy("PosOnly", policy => policy.RequireRole("POS"));

        return services;
    }

    public static WebApplication UseApiServices(this WebApplication app)
    {
        app.UseSwagger();
        app.UseSwaggerUI();

        app.MapCarter();

        app.UseExceptionHandler(options => { });
        app.UseHealthChecks("/health",
            new HealthCheckOptions
            {
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });

        app.UseHttpsRedirection();
        app.UseCors("CorsPolicy");
       
        app.UseAuthentication();
        app.UseAuthorization();
        app.UseStaticFiles();
        return app;
    }
}