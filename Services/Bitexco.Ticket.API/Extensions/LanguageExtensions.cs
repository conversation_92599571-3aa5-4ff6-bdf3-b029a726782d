using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.API.Extensions;

/// <summary>
/// Extension methods for language services initialization
/// </summary>
public static class LanguageExtensions
{
    /// <summary>
    /// Initialize language services (LanguageDataStorage + ExceptionMessageHelper)
    /// Combines both initializations for cleaner Program.cs
    /// </summary>
    /// <param name="app">WebApplication instance</param>
    /// <returns>Task</returns>
    public static async Task InitializeLanguageServicesAsync(this WebApplication app)
    {
        // Initialize Language Cache (per boss requirement - load at startup)
        using (var scope = app.Services.CreateScope())
        {
            var languageServiceHelper = scope.ServiceProvider.GetRequiredService<ILanguageServiceHelper>();
            await languageServiceHelper.InitializeAsync(app.Services);
        }

        // Initialize Exception Message Helper for static access
        ExceptionMessageHelper.Initialize(app.Services);
    }
}
