using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Bitexco.Ticket.API.ApiFilters;

/// <summary>
/// Swagger filter to add language header for endpoints marked with LanguageClientAttribute
/// </summary>
public class LanguageClientFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Only apply to endpoints with LanguageClientAttribute
        if (context.ApiDescription.ActionDescriptor.EndpointMetadata
            .Any(em => em is LanguageClientAttribute))
        {
            operation.Parameters ??= [];

            // Add lang header if not already present
            if (operation.Parameters.FirstOrDefault(x => x.Name == "lang") is null)
            {
                operation.Parameters.Add(new OpenApiParameter
                {
                    Name = "lang",
                    In = ParameterLocation.Header,
                    Required = false,
                    Description = "Language code (e.g., 'en', 'vi'). Default is 'en'.",
                    Schema = new OpenApiSchema
                    {
                        Type = "string",
                        Default = new Microsoft.OpenApi.Any.OpenApiString("en")
                    }
                });
            }
        }
    }
}
