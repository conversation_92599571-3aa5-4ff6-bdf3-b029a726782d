# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080
EXPOSE 8081


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Bitexco.Ticket.API/Services/Bitexco.Ticket.API/Bitexco.Ticket.API.csproj", "Bitexco.Ticket.API/Services/Bitexco.Ticket.API/"]
COPY ["Bitexco.Ticket.API/Services/Bitexco.Ticket.Application/Bitexco.Ticket.Application.csproj", "Bitexco.Ticket.API/Services/Bitexco.Ticket.Application/"]
COPY ["Bitexco.Ticket.API/BuildingBlocks/BuildingBlocks.Messaging/BuildingBlocks.Messaging.csproj", "Bitexco.Ticket.API/BuildingBlocks/BuildingBlocks.Messaging/"]
COPY ["Bitexco.Ticket.API/BuildingBlocks/BuildingBlocks/BuildingBlocks.csproj", "Bitexco.Ticket.API/BuildingBlocks/BuildingBlocks/"]
COPY ["Bitexco.Ticket.API/Services/Bitexco.Ticket.Domain/Bitexco.Ticket.Domain.csproj", "Bitexco.Ticket.API/Services/Bitexco.Ticket.Domain/"]
COPY ["Bitexco.Ticket.API/Services/Bitexco.Ticket.Infrastructure/Bitexco.Ticket.Infrastructure.csproj", "Bitexco.Ticket.API/Services/Bitexco.Ticket.Infrastructure/"]
RUN dotnet restore "./Bitexco.Ticket.API/Services/Bitexco.Ticket.API/Bitexco.Ticket.API.csproj"
COPY . .
WORKDIR "/src/Bitexco.Ticket.API/Services/Bitexco.Ticket.API"
RUN dotnet build "./Bitexco.Ticket.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Bitexco.Ticket.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Bitexco.Ticket.API.dll"]