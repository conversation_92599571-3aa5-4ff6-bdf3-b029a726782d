using Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueGrowthChart;
using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Bitexco.Ticket.Application.Tests.Features.Reports.RevenueReports.GetRevenueGrowthChart;

/// <summary>
/// Unit tests for GetRevenueGrowthChartQueryHandler timezone handling
/// </summary>
public class GetRevenueGrowthChartQueryHandlerTests : IDisposable
{
    private readonly TestDbContext _context;
    private readonly GetRevenueGrowthChartQueryHandler _handler;

    public GetRevenueGrowthChartQueryHandlerTests()
    {
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TestDbContext(options);
        _handler = new GetRevenueGrowthChartQueryHandler(_context);
    }

    [Fact]
    public async Task Handle_ShouldConvertTimezoneCorrectly_WhenGroupingByHour()
    {
        // Arrange: Create test orders with UTC times
        var utcTime1 = new DateTime(2024, 1, 15, 2, 30, 0, DateTimeKind.Utc); // 2:30 UTC = 9:30 Vietnam
        var utcTime2 = new DateTime(2024, 1, 15, 14, 30, 0, DateTimeKind.Utc); // 14:30 UTC = 21:30 Vietnam

        var orders = new List<Order>
        {
            new Order
            {
                Id = Guid.NewGuid(),
                CreatedAt = utcTime1,
                Status = OrderStatus.paid,
                FinalAmount = 100m
            },
            new Order
            {
                Id = Guid.NewGuid(),
                CreatedAt = utcTime2,
                Status = OrderStatus.paid,
                FinalAmount = 200m
            }
        };

        await _context.Orders.AddRangeAsync(orders);
        await _context.SaveChangesAsync();

        // Vietnam time: 9:30 and 21:30 on same day
        var vietnamDate = new DateTime(2024, 1, 15, 0, 0, 0, DateTimeKind.Unspecified);
        var query = new GetRevenueGrowthChartQuery
        {
            DateFrom = vietnamDate,
            DateTo = vietnamDate.AddDays(1).AddSeconds(-1)
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Data);
        
        // Should have 24 hour data points
        Assert.Equal(24, result.Data.Count);
        
        // Check that revenue is grouped by Vietnam local time hours
        var hour9Data = result.Data.FirstOrDefault(d => d.Period == "09:00");
        var hour21Data = result.Data.FirstOrDefault(d => d.Period == "21:00");
        
        Assert.NotNull(hour9Data);
        Assert.NotNull(hour21Data);
        Assert.Equal(100m, hour9Data.Revenue);
        Assert.Equal(200m, hour21Data.Revenue);
    }

    [Fact]
    public async Task Handle_ShouldHandleWeekCalculation_StartingFromMonday()
    {
        // Arrange: Test week calculation logic
        var monday = new DateTime(2024, 1, 15); // Assuming this is a Monday
        var sunday = monday.AddDays(6);

        // Create orders throughout the week
        var orders = new List<Order>();
        for (int i = 0; i < 7; i++)
        {
            var date = monday.AddDays(i);
            var utcTime = TimeZoneInfo.ConvertTimeToUtc(date.AddHours(12), 
                TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time"));
            
            orders.Add(new Order
            {
                Id = Guid.NewGuid(),
                CreatedAt = utcTime,
                Status = OrderStatus.paid,
                FinalAmount = (i + 1) * 100m
            });
        }

        await _context.Orders.AddRangeAsync(orders);
        await _context.SaveChangesAsync();

        var query = new GetRevenueGrowthChartQuery
        {
            DateFrom = monday,
            DateTo = sunday
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Data);
        Assert.Equal(7, result.Data.Count); // 7 days in a week
        
        // Verify each day has correct revenue
        for (int i = 0; i < 7; i++)
        {
            var expectedDate = monday.AddDays(i).ToString("yyyy-MM-dd");
            var dayData = result.Data.FirstOrDefault(d => d.Period == expectedDate);
            Assert.NotNull(dayData);
            Assert.Equal((i + 1) * 100m, dayData.Revenue);
        }
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}

/// <summary>
/// Test DbContext for unit testing
/// </summary>
public class TestDbContext : DbContext, IApplicationDbContext
{
    public TestDbContext(DbContextOptions<TestDbContext> options) : base(options) { }

    public DbSet<Order> Orders { get; set; } = null!;
    
    // Implement other required DbSets as needed for testing
    public DbSet<Agent> Agents => throw new NotImplementedException();
    public DbSet<AgentApiKey> AgentApiKeys => throw new NotImplementedException();
    public DbSet<AgentDebt> AgentDebts => throw new NotImplementedException();
    public DbSet<AuditLog> AuditLogs => throw new NotImplementedException();
    public DbSet<CommissionOrderHistory> CommissionOrderHistories => throw new NotImplementedException();
    public DbSet<CommissionPayment> CommissionPayments => throw new NotImplementedException();
    public DbSet<CommissionPolicy> CommissionPolicies => throw new NotImplementedException();
    public DbSet<CommissionReport> CommissionReports => throw new NotImplementedException();
    public DbSet<ContentPage> ContentPages => throw new NotImplementedException();
    public DbSet<Customer> Customers => throw new NotImplementedException();
    public DbSet<Faq> Faqs => throw new NotImplementedException();
    public DbSet<Invoice> Invoices => throw new NotImplementedException();
    public DbSet<NotificationTemplate> NotificationTemplates => throw new NotImplementedException();
    public DbSet<OperatingHour> OperatingHours => throw new NotImplementedException();
    public DbSet<OrderItem> OrderItems => throw new NotImplementedException();
    public DbSet<PaymentTransaction> PaymentTransactions => throw new NotImplementedException();
    public DbSet<Permission> Permissions => throw new NotImplementedException();
    public DbSet<PosDevice> PosDevices => throw new NotImplementedException();
    public DbSet<PricingRule> PricingRules => throw new NotImplementedException();
    public DbSet<RetailChannel> RetailChannels => throw new NotImplementedException();
    public DbSet<Role> Roles => throw new NotImplementedException();
    public DbSet<RolePermission> RolePermissions => throw new NotImplementedException();
    public DbSet<Shift> Shifts => throw new NotImplementedException();
    public DbSet<SupportTicket> SupportTickets => throw new NotImplementedException();
    public DbSet<SupportTicketMessage> SupportTicketMessages => throw new NotImplementedException();
    public DbSet<SystemConfig> SystemConfigs => throw new NotImplementedException();
    public DbSet<Domain.Models.Ticket> Tickets => throw new NotImplementedException();
    public DbSet<TicketHistory> TicketHistories => throw new NotImplementedException();
    public DbSet<TicketPrice> TicketPrices => throw new NotImplementedException();
    public DbSet<TicketType> TicketTypes => throw new NotImplementedException();
    public DbSet<TicketTypeCondition> TicketTypeConditions => throw new NotImplementedException();
    public DbSet<Function> Functions => throw new NotImplementedException();
    public DbSet<RoleFunction> RoleFunctions => throw new NotImplementedException();
    public DbSet<VisitTimeRule> VisitTimeRules => throw new NotImplementedException();
    public DbSet<PriceApproval> PriceApprovals => throw new NotImplementedException();
    public DbSet<KioskSyncStatus> KioskSyncStatuses => throw new NotImplementedException();
    public DbSet<User> Users => throw new NotImplementedException();
    public DbSet<Voucher> Vouchers => throw new NotImplementedException();
    public DbSet<VoucherCampaign> VoucherCampaigns => throw new NotImplementedException();
    public DbSet<VoucherChannel> VoucherChannels => throw new NotImplementedException();
    public DbSet<BusinessHour> BusinessHours => throw new NotImplementedException();
    public DbSet<Notification> Notifications => throw new NotImplementedException();
    public DbSet<OtpLog> OtpLogs => throw new NotImplementedException();
    public DbSet<ScheduledTask> ScheduledTasks => throw new NotImplementedException();
    public DbSet<VisitorFlow> VisitorFlows => throw new NotImplementedException();
    public DbSet<VisitorStat> VisitorStats => throw new NotImplementedException();
    public DbSet<LanguageMessage> LanguageMessages => throw new NotImplementedException();
    public DbSet<ForgotPasswordSession> ForgotPasswordSessions => throw new NotImplementedException();
    public DbSet<GroupBooking> GroupBookings => throw new NotImplementedException();
    public DbSet<GroupBookingBus> GroupBookingBuses => throw new NotImplementedException();
    public DbSet<PromotionCampaign> PromotionCampaigns => throw new NotImplementedException();
    public DbSet<Contract> Contracts => throw new NotImplementedException();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure Order entity for testing
        modelBuilder.Entity<Order>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.CustomerType).HasConversion<string>();
        });
        
        base.OnModelCreating(modelBuilder);
    }
}
