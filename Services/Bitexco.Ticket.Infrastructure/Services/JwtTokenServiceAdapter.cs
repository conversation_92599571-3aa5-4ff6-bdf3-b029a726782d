namespace Bitexco.Ticket.Infrastructure.Services;

using Bitexco.Ticket.Domain.Models;

public class JwtTokenServiceAdapter(IJwtTokenService jwtTokenService) : Application.Services.IJwtTokenService
{

    public async Task<string> GenerateAccessToken(User user)
    {
        return await jwtTokenService.GenerateAccessTokenAsync(user);
    }

    public string GenerateRefreshToken()
    {
        return jwtTokenService.GenerateRefreshToken();
    }

    public DateTime GetRefreshTokenExpiryTime()
    {
        return jwtTokenService.GetRefreshTokenExpiryTime();
    }
}
