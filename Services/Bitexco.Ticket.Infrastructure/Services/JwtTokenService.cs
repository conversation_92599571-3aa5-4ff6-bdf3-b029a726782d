namespace Bitexco.Ticket.Infrastructure.Services;

using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Helpers;
using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

public class JwtTokenService(IConfiguration configuration, IApplicationDbContext dbContext) : IJwtTokenService
{

    public async Task<string> GenerateAccessTokenAsync(User user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(configuration["JwtSettings:Key"] ?? throw new InvalidOperationException("JWT key is not configured"));
        
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(JwtRegisteredClaimNames.Email, user.Email ?? string.Empty),
            new(JwtRegisteredClaimNames.Name, user.UserName),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new("fullName", user.FullName ?? string.Empty)
        };
        
        string roleName = string.Empty;
        string roleType = string.Empty;
        if (user.RoleId.HasValue)
        {
            var role = await dbContext.Roles
                .FirstOrDefaultAsync(r => r.Id == user.RoleId.Value);
            roleName = role?.Name ?? string.Empty;
            roleType = role?.Type.ToString() ?? string.Empty;
        }

        // Set permission for type = admin
        if (roleType != RoleType.admin.ToString())
        {
            // Get permissions if user has a role (EMR pattern: Function Code + Permission Type)
            if (user.RoleId.HasValue && !string.IsNullOrEmpty(roleName))
            {
                var permissions = await dbContext.RoleFunctions
                    .Include(rf => rf.Function)
                    .Where(rf => rf.RoleId == user.RoleId.Value &&
                                !string.IsNullOrEmpty(rf.PermissionType))
                    .Select(rf => rf.Function.Code + rf.PermissionType)
                    .ToListAsync();

                // Add all permissions as role claims
                claims.AddRange(permissions.Distinct().Select(permission => new Claim(ClaimTypes.Role, permission)));
            }
        }
        else
        {
            // Get function codes if user has a role (EMR pattern: Function Code + Permission Type)
            var functionCodes = await dbContext.Functions
                .Select(f => f.Code)
                .ToListAsync();

            // Get permission by helper
            var permissions = RolePermissionHelper.GetPermissions();

            // Add all permissions as role claims: per function code + permission type
            foreach (var permission in permissions)
            {
                foreach (var functionCode in functionCodes)
                {
                    claims.Add(new Claim(ClaimTypes.Role, functionCode + permission.Id));
                }
            }
        }

        // Add role name as role claim
        if (!string.IsNullOrEmpty(roleName))
        {
            claims.Add(new Claim(ClaimTypes.Role, roleName));
        }
        
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(Convert.ToDouble(configuration["JwtSettings:AccessTokenExpirationMinutes"] ?? "30")),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
            Issuer = configuration["JwtSettings:Issuer"],
            Audience = configuration["JwtSettings:Audience"]
        };
        
        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
    
    public string GenerateRefreshToken()
    {
        var randomNumber = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }
    
    public DateTime GetRefreshTokenExpiryTime()
    {
        return DateTime.UtcNow.AddDays(Convert.ToDouble(configuration["JwtSettings:RefreshTokenExpirationDays"] ?? "7"));
    }
}
