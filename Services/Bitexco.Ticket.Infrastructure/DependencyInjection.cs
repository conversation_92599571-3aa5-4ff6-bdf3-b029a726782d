﻿using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Infrastructure.Repositories;
using Bitexco.Ticket.Infrastructure.Services;
using Npgsql;
using Bitexco.Ticket.Application.Services;
using BuildingBlocks.CacheServices;

namespace Bitexco.Ticket.Infrastructure;
public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices
        (this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("Database");

        // Add services to the container.
        services.AddSingleton<ISaveChangesInterceptor, AuditableEntityInterceptor>();
        services.AddSingleton<ISaveChangesInterceptor, DispatchDomainEventsInterceptor>();

        services.AddDbContextPool<ApplicationDbContext>((sp, o) =>
        {
            o.AddInterceptors(sp.GetServices<ISaveChangesInterceptor>());

            var dataSource = new NpgsqlDataSourceBuilder(connectionString)
                .EnableDynamicJson()
                .Build();

            o.UseNpgsql(dataSource, c =>
            {
                c.EnableRetryOnFailure(5);
            });
            o.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTrackingWithIdentityResolution);
        });

        // Register distributed cache
        services.AddDistributedMemoryCache();
        services.AddSingleton<ICachedService, MemoryCachedService>();

        services.AddScoped<IApplicationDbContext, ApplicationDbContext>();
        // Register repositories
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IOrderRepository, OrderRepository>();
        
        // Register JWT services
        services.AddScoped<Services.IJwtTokenService, JwtTokenService>();
        services.AddScoped<Application.Services.IJwtTokenService, JwtTokenServiceAdapter>();
        
        // Register ticket type services
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        
        return services;
    }
}
