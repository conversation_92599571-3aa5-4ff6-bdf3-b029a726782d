﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddVoucherCampaign : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AgentIds",
                table: "Vouchers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VoucherApplyType",
                table: "Vouchers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "VoucherCampaignId",
                table: "Vouchers",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateTable(
                name: "VoucherCampaigns",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "percentage"),
                    DiscountValue = table.Column<decimal>(type: "numeric(19,4)", nullable: false),
                    UsageLimit = table.Column<int>(type: "integer", nullable: true),
                    VoucherApplyType = table.Column<string>(type: "text", nullable: true),
                    AgentIds = table.Column<string>(type: "text", nullable: true),
                    CurrentUsage = table.Column<int>(type: "integer", nullable: false),
                    ValidFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    ValidTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VoucherCampaigns", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Vouchers_VoucherCampaignId",
                table: "Vouchers",
                column: "VoucherCampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_VoucherCampaigns_Id",
                table: "VoucherCampaigns",
                column: "Id",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Vouchers_VoucherCampaigns_VoucherCampaignId",
                table: "Vouchers",
                column: "VoucherCampaignId",
                principalTable: "VoucherCampaigns",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Vouchers_VoucherCampaigns_VoucherCampaignId",
                table: "Vouchers");

            migrationBuilder.DropTable(
                name: "VoucherCampaigns");

            migrationBuilder.DropIndex(
                name: "IX_Vouchers_VoucherCampaignId",
                table: "Vouchers");

            migrationBuilder.DropColumn(
                name: "AgentIds",
                table: "Vouchers");

            migrationBuilder.DropColumn(
                name: "VoucherApplyType",
                table: "Vouchers");

            migrationBuilder.DropColumn(
                name: "VoucherCampaignId",
                table: "Vouchers");
        }
    }
}
