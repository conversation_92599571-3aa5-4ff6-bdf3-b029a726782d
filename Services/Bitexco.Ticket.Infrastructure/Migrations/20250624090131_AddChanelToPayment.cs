﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddChanelToPayment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "PaymentTransactions",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValueSql: "CONCAT('P-', TO_CHAR(NOW(), 'YYYYMMDDHH24MISS'))");

            migrationBuilder.AddColumn<Guid>(
                name: "RetailChannelId",
                table: "PaymentTransactions",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TransactionType",
                table: "PaymentTransactions",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "income");

            migrationBuilder.CreateIndex(
                name: "IX_PaymentTransactions_Code",
                table: "PaymentTransactions",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_PaymentTransactions_RetailChannelId",
                table: "PaymentTransactions",
                column: "RetailChannelId");

            migrationBuilder.AddForeignKey(
                name: "FK_PaymentTransactions_RetailChannels_RetailChannelId",
                table: "PaymentTransactions",
                column: "RetailChannelId",
                principalTable: "RetailChannels",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PaymentTransactions_RetailChannels_RetailChannelId",
                table: "PaymentTransactions");

            migrationBuilder.DropIndex(
                name: "IX_PaymentTransactions_Code",
                table: "PaymentTransactions");

            migrationBuilder.DropIndex(
                name: "IX_PaymentTransactions_RetailChannelId",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "RetailChannelId",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "TransactionType",
                table: "PaymentTransactions");
        }
    }
}
