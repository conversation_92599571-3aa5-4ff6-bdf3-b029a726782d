﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddOrderRefundInfo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "RefundPaymentMethod",
                table: "Orders",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true,
                defaultValue: "cash");

            migrationBuilder.AddColumn<string>(
                name: "RefundPaymentProofFilePath",
                table: "Orders",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RefundedAt",
                table: "Orders",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RefundPaymentMethod",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "RefundPaymentProofFilePath",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "RefundedAt",
                table: "Orders");
        }
    }
}
