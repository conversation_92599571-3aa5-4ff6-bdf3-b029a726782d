﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddRetailChanel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "Customers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "RetailChannelId",
                table: "Customers",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Customers_RetailChannelId",
                table: "Customers",
                column: "RetailChannelId");

            migrationBuilder.AddForeignKey(
                name: "FK_Customers_RetailChannels_RetailChannelId",
                table: "Customers",
                column: "RetailChannelId",
                principalTable: "RetailChannels",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Customers_RetailChannels_RetailChannelId",
                table: "Customers");

            migrationBuilder.DropIndex(
                name: "IX_Customers_RetailChannelId",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "RetailChannelId",
                table: "Customers");
        }
    }
}
