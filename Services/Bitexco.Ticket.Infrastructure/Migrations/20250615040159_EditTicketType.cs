﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class EditTicketType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MaxValue",
                table: "TicketTypeConditions");

            migrationBuilder.DropColumn(
                name: "MinValue",
                table: "TicketTypeConditions");

            migrationBuilder.AddColumn<decimal>(
                name: "DefaultPrice",
                table: "TicketTypes",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "TicketCodeLengthAfterPrefix",
                table: "TicketTypes",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "TicketCodePrefix",
                table: "TicketTypes",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "TicketExpirationDays",
                table: "TicketTypes",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "FromValue",
                table: "TicketTypeConditions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ToValue",
                table: "TicketTypeConditions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "TicketTypeConditions",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "TimeRange");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DefaultPrice",
                table: "TicketTypes");

            migrationBuilder.DropColumn(
                name: "TicketCodeLengthAfterPrefix",
                table: "TicketTypes");

            migrationBuilder.DropColumn(
                name: "TicketCodePrefix",
                table: "TicketTypes");

            migrationBuilder.DropColumn(
                name: "TicketExpirationDays",
                table: "TicketTypes");

            migrationBuilder.DropColumn(
                name: "FromValue",
                table: "TicketTypeConditions");

            migrationBuilder.DropColumn(
                name: "ToValue",
                table: "TicketTypeConditions");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "TicketTypeConditions");

            migrationBuilder.AddColumn<int>(
                name: "MaxValue",
                table: "TicketTypeConditions",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MinValue",
                table: "TicketTypeConditions",
                type: "integer",
                nullable: true);
        }
    }
}
