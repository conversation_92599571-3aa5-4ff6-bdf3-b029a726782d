﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAgentDebt : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AgentDebts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AgentId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalAmount = table.Column<decimal>(type: "numeric(19,4)", nullable: false),
                    PaidAmount = table.Column<decimal>(type: "numeric(19,4)", nullable: false, defaultValue: 0m),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "outstanding"),
                    PaymentMethod = table.Column<string>(type: "character varying(30)", maxLength: 30, nullable: true),
                    PaidAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    PaymentProofFilePath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    InvoiceFilePath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AgentId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentDebts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentDebts_Agents_AgentId",
                        column: x => x.AgentId,
                        principalTable: "Agents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AgentDebts_Agents_AgentId1",
                        column: x => x.AgentId1,
                        principalTable: "Agents",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentDebts_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AgentDebts_Users_ApprovedByUserId",
                        column: x => x.ApprovedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AgentDebts_AgentId",
                table: "AgentDebts",
                column: "AgentId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentDebts_AgentId_Status",
                table: "AgentDebts",
                columns: new[] { "AgentId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_AgentDebts_AgentId1",
                table: "AgentDebts",
                column: "AgentId1");

            migrationBuilder.CreateIndex(
                name: "IX_AgentDebts_ApprovedByUserId",
                table: "AgentDebts",
                column: "ApprovedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentDebts_Id",
                table: "AgentDebts",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AgentDebts_OrderId",
                table: "AgentDebts",
                column: "OrderId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AgentDebts_Status",
                table: "AgentDebts",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgentDebts");
        }
    }
}
