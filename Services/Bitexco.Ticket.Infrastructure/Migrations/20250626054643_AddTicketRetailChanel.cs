﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddTicketRetailChanel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "Users",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<Guid>(
                name: "RetailChannelId",
                table: "Tickets",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Tickets_RetailChannelId",
                table: "Tickets",
                column: "RetailChannelId");

            migrationBuilder.AddForeignKey(
                name: "FK_Tickets_RetailChannels_RetailChannelId",
                table: "Tickets",
                column: "RetailChannelId",
                principalTable: "RetailChannels",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Tickets_RetailChannels_RetailChannelId",
                table: "Tickets");

            migrationBuilder.DropIndex(
                name: "IX_Tickets_RetailChannelId",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "RetailChannelId",
                table: "Tickets");
        }
    }
}
