﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class EditAgent : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "CommissionRatePercentage",
                table: "Agents",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ContactPosition",
                table: "Agents",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DistrictId",
                table: "Agents",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "FixedCommissionAmount",
                table: "Agents",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDebtAllowed",
                table: "Agents",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsDirectDiscount",
                table: "Agents",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "ProvinceId",
                table: "Agents",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SapCode",
                table: "Agents",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WardId",
                table: "Agents",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CommissionRatePercentage",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "ContactPosition",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "DistrictId",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "FixedCommissionAmount",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "IsDebtAllowed",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "IsDirectDiscount",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "ProvinceId",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "SapCode",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "WardId",
                table: "Agents");
        }
    }
}
