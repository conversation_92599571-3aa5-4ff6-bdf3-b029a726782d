﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddColTicketTypeByVoucherCampaginAndPromotionCampaign : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "TicketTypeId",
                table: "VoucherCampaigns",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TicketTypeId",
                table: "PromotionCampaigns",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_VoucherCampaigns_TicketTypeId",
                table: "VoucherCampaigns",
                column: "TicketTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionCampaigns_TicketTypeId",
                table: "PromotionCampaigns",
                column: "TicketTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_PromotionCampaigns_TicketTypes_TicketTypeId",
                table: "PromotionCampaigns",
                column: "TicketTypeId",
                principalTable: "TicketTypes",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_VoucherCampaigns_TicketTypes_TicketTypeId",
                table: "VoucherCampaigns",
                column: "TicketTypeId",
                principalTable: "TicketTypes",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PromotionCampaigns_TicketTypes_TicketTypeId",
                table: "PromotionCampaigns");

            migrationBuilder.DropForeignKey(
                name: "FK_VoucherCampaigns_TicketTypes_TicketTypeId",
                table: "VoucherCampaigns");

            migrationBuilder.DropIndex(
                name: "IX_VoucherCampaigns_TicketTypeId",
                table: "VoucherCampaigns");

            migrationBuilder.DropIndex(
                name: "IX_PromotionCampaigns_TicketTypeId",
                table: "PromotionCampaigns");

            migrationBuilder.DropColumn(
                name: "TicketTypeId",
                table: "VoucherCampaigns");

            migrationBuilder.DropColumn(
                name: "TicketTypeId",
                table: "PromotionCampaigns");
        }
    }
}
