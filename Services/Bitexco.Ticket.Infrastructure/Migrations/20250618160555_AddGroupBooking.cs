﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddGroupBooking : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "GroupBookingId",
                table: "Orders",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "GroupBookings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AgentId = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    VisitDate = table.Column<DateOnly>(type: "date", nullable: false),
                    VisitTime = table.Column<TimeOnly>(type: "time without time zone", nullable: false),
                    EstimatedTotalAmount = table.Column<decimal>(type: "numeric(19,4)", nullable: false),
                    DiscountAmount = table.Column<decimal>(type: "numeric(19,4)", nullable: false, defaultValue: 0m),
                    DepositAmount = table.Column<decimal>(type: "numeric(19,4)", nullable: false, defaultValue: 0m),
                    EstimatedFinalAmount = table.Column<decimal>(type: "numeric(19,4)", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CheckedInAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GroupBookings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GroupBookings_Agents_AgentId",
                        column: x => x.AgentId,
                        principalTable: "Agents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "GroupBookingTicketRequests",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    GroupBookingId = table.Column<Guid>(type: "uuid", nullable: false),
                    TicketTypeId = table.Column<Guid>(type: "uuid", nullable: false),
                    Quantity = table.Column<int>(type: "integer", nullable: false),
                    UnitPrice = table.Column<decimal>(type: "numeric(19,4)", nullable: false),
                    TotalAmount = table.Column<decimal>(type: "numeric(19,4)", nullable: false),
                    ActualQuantityIssued = table.Column<int>(type: "integer", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GroupBookingTicketRequests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GroupBookingTicketRequests_GroupBookings_GroupBookingId",
                        column: x => x.GroupBookingId,
                        principalTable: "GroupBookings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_GroupBookingTicketRequests_TicketTypes_TicketTypeId",
                        column: x => x.TicketTypeId,
                        principalTable: "TicketTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Orders_GroupBookingId",
                table: "Orders",
                column: "GroupBookingId");

            migrationBuilder.CreateIndex(
                name: "IX_GroupBookings_AgentId",
                table: "GroupBookings",
                column: "AgentId");

            migrationBuilder.CreateIndex(
                name: "IX_GroupBookings_Id",
                table: "GroupBookings",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_GroupBookingTicketRequests_GroupBookingId",
                table: "GroupBookingTicketRequests",
                column: "GroupBookingId");

            migrationBuilder.CreateIndex(
                name: "IX_GroupBookingTicketRequests_Id",
                table: "GroupBookingTicketRequests",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_GroupBookingTicketRequests_TicketTypeId",
                table: "GroupBookingTicketRequests",
                column: "TicketTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_Orders_GroupBookings_GroupBookingId",
                table: "Orders",
                column: "GroupBookingId",
                principalTable: "GroupBookings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Orders_GroupBookings_GroupBookingId",
                table: "Orders");

            migrationBuilder.DropTable(
                name: "GroupBookingTicketRequests");

            migrationBuilder.DropTable(
                name: "GroupBookings");

            migrationBuilder.DropIndex(
                name: "IX_Orders_GroupBookingId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "GroupBookingId",
                table: "Orders");
        }
    }
}
