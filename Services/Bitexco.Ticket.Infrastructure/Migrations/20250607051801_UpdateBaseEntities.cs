﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bitexco.Ticket.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateBaseEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Agents_Agents_ParentAgentId",
                table: "Agents");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "Vouchers",
                type: "boolean",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "Users",
                type: "boolean",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AddColumn<string>(
                name: "LastOtpCode",
                table: "Users",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "OtpExpiresAt",
                table: "Users",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PreferredOtpMethod",
                table: "Users",
                type: "text",
                nullable: false,
                defaultValue: "email");

            migrationBuilder.AddColumn<bool>(
                name: "TwoFactorEnabled",
                table: "Users",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "TwoFactorSecret",
                table: "Users",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "TicketTypes",
                type: "boolean",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AddColumn<string>(
                name: "AccessLocation",
                table: "Tickets",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastReprintedAt",
                table: "Tickets",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateOnly>(
                name: "PlannedVisitDate",
                table: "Tickets",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<TimeOnly>(
                name: "PlannedVisitTime",
                table: "Tickets",
                type: "time without time zone",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PosDeviceId",
                table: "Tickets",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ReprintCount",
                table: "Tickets",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<Guid>(
                name: "ValidatedAtDeviceId",
                table: "Tickets",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ValidatedByUserId",
                table: "Tickets",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "RetailChannels",
                type: "boolean",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AlterColumn<bool>(
                name: "IsHoliday",
                table: "PricingRules",
                type: "boolean",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "PricingRules",
                type: "boolean",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AddColumn<string>(
                name: "ErrorDetails",
                table: "PosDevices",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Floor",
                table: "PosDevices",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HardwareModel",
                table: "PosDevices",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IpAddress",
                table: "PosDevices",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastErrorAt",
                table: "PosDevices",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastMaintenanceAt",
                table: "PosDevices",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastUpdateAt",
                table: "PosDevices",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Latitude",
                table: "PosDevices",
                type: "numeric(9,6)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Longitude",
                table: "PosDevices",
                type: "numeric(9,6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MacAddress",
                table: "PosDevices",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OfflineCount",
                table: "PosDevices",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "OperatingSystem",
                table: "PosDevices",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PrinterStatus",
                table: "PosDevices",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true,
                defaultValue: "operational");

            migrationBuilder.AddColumn<string>(
                name: "SerialNumber",
                table: "PosDevices",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UpdateStatus",
                table: "PosDevices",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true,
                defaultValue: "up_to_date");

            migrationBuilder.AlterColumn<decimal>(
                name: "PaidAmount",
                table: "PaymentTransactions",
                type: "numeric(19,4)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "numeric(19,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountAmount",
                table: "Orders",
                type: "numeric(19,4)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "numeric(19,4)");

            migrationBuilder.AddColumn<Guid>(
                name: "VoucherId1",
                table: "Orders",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsSpecialDay",
                table: "OperatingHours",
                type: "boolean",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AddColumn<string>(
                name: "AvailableVariables",
                table: "NotificationTemplates",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "NotificationTemplates",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EventType",
                table: "NotificationTemplates",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "NotificationTemplates",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "LanguageCode",
                table: "NotificationTemplates",
                type: "character varying(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "vi");

            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                table: "CommissionPayments",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EntityType",
                table: "AuditLogs",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AddColumn<Guid>(
                name: "UserId1",
                table: "AuditLogs",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "Agents",
                type: "boolean",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AddColumn<string>(
                name: "ApprovalStatus",
                table: "Agents",
                type: "text",
                nullable: false,
                defaultValue: "pending");

            migrationBuilder.AddColumn<int>(
                name: "CurrentMonthTicketsSold",
                table: "Agents",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateOnly>(
                name: "LastLimitResetDate",
                table: "Agents",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MonthlyTicketLimit",
                table: "Agents",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SuspensionReason",
                table: "Agents",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "AgentApiKeys",
                type: "boolean",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.CreateTable(
                name: "BusinessHours",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DayOfWeek = table.Column<int>(type: "integer", maxLength: 50, nullable: false),
                    IsOpen = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    OpenTime = table.Column<TimeOnly>(type: "time without time zone", nullable: false),
                    CloseTime = table.Column<TimeOnly>(type: "time without time zone", nullable: false),
                    Notes = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    IsHoliday = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SpecificDate = table.Column<DateOnly>(type: "date", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BusinessHours", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Notifications",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Subject = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Content = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "email"),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "pending"),
                    Recipient = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelatedEntityId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelatedEntityType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ScheduledAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SentAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeliveredAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ErrorDetails = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Notifications", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Notifications_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "OtpLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    OtpCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Method = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "email"),
                    SentTo = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "sent"),
                    VerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FailedAttempts = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IpAddress = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UserAgent = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ValidationHash = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OtpLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OtpLogs_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ScheduledTasks",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaskName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    TaskDescription = table.Column<string>(type: "text", nullable: false),
                    TaskType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Schedule = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    IsEnabled = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    LastRunTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastRunStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    LastRunResult = table.Column<string>(type: "text", nullable: true),
                    NextRunTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Parameters = table.Column<string>(type: "jsonb", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScheduledTasks", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VisitorFlows",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EntryPoint = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "main"),
                    EntryCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    ExitCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CurrentOccupancy = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VisitorFlows", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VisitorStats",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    StatDate = table.Column<DateOnly>(type: "date", nullable: false),
                    StatHour = table.Column<TimeOnly>(type: "time without time zone", nullable: true),
                    TicketTypeName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    VisitorCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    OnlineTicketCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    OfflineTicketCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    AgentTicketCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalRevenue = table.Column<decimal>(type: "numeric(19,4)", nullable: false, defaultValue: 0m),
                    AdultCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    ChildCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    SeniorCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CashPaymentCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    PosPaymentCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TransferPaymentCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    EWalletPaymentCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    PeakHour = table.Column<TimeOnly>(type: "time without time zone", nullable: true),
                    PeakHourCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VisitorStats", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Tickets_PosDeviceId",
                table: "Tickets",
                column: "PosDeviceId");

            migrationBuilder.CreateIndex(
                name: "IX_Tickets_ValidatedAtDeviceId",
                table: "Tickets",
                column: "ValidatedAtDeviceId");

            migrationBuilder.CreateIndex(
                name: "IX_Tickets_ValidatedByUserId",
                table: "Tickets",
                column: "ValidatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_VoucherId1",
                table: "Orders",
                column: "VoucherId1");

            migrationBuilder.CreateIndex(
                name: "IX_CommissionPayments_UserId",
                table: "CommissionPayments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_UserId1",
                table: "AuditLogs",
                column: "UserId1");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessHours_Id",
                table: "BusinessHours",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Notifications_Id",
                table: "Notifications",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Notifications_UserId",
                table: "Notifications",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_OtpLogs_Id",
                table: "OtpLogs",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OtpLogs_UserId",
                table: "OtpLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_ScheduledTasks_Id",
                table: "ScheduledTasks",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VisitorFlows_Id",
                table: "VisitorFlows",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VisitorStats_Id",
                table: "VisitorStats",
                column: "Id",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Agents_Agents_ParentAgentId",
                table: "Agents",
                column: "ParentAgentId",
                principalTable: "Agents",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditLogs_Users_UserId1",
                table: "AuditLogs",
                column: "UserId1",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CommissionPayments_Users_UserId",
                table: "CommissionPayments",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Orders_Vouchers_VoucherId1",
                table: "Orders",
                column: "VoucherId1",
                principalTable: "Vouchers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tickets_PosDevices_PosDeviceId",
                table: "Tickets",
                column: "PosDeviceId",
                principalTable: "PosDevices",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tickets_PosDevices_ValidatedAtDeviceId",
                table: "Tickets",
                column: "ValidatedAtDeviceId",
                principalTable: "PosDevices",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tickets_Users_ValidatedByUserId",
                table: "Tickets",
                column: "ValidatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Agents_Agents_ParentAgentId",
                table: "Agents");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditLogs_Users_UserId1",
                table: "AuditLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_CommissionPayments_Users_UserId",
                table: "CommissionPayments");

            migrationBuilder.DropForeignKey(
                name: "FK_Orders_Vouchers_VoucherId1",
                table: "Orders");

            migrationBuilder.DropForeignKey(
                name: "FK_Tickets_PosDevices_PosDeviceId",
                table: "Tickets");

            migrationBuilder.DropForeignKey(
                name: "FK_Tickets_PosDevices_ValidatedAtDeviceId",
                table: "Tickets");

            migrationBuilder.DropForeignKey(
                name: "FK_Tickets_Users_ValidatedByUserId",
                table: "Tickets");

            migrationBuilder.DropTable(
                name: "BusinessHours");

            migrationBuilder.DropTable(
                name: "Notifications");

            migrationBuilder.DropTable(
                name: "OtpLogs");

            migrationBuilder.DropTable(
                name: "ScheduledTasks");

            migrationBuilder.DropTable(
                name: "VisitorFlows");

            migrationBuilder.DropTable(
                name: "VisitorStats");

            migrationBuilder.DropIndex(
                name: "IX_Tickets_PosDeviceId",
                table: "Tickets");

            migrationBuilder.DropIndex(
                name: "IX_Tickets_ValidatedAtDeviceId",
                table: "Tickets");

            migrationBuilder.DropIndex(
                name: "IX_Tickets_ValidatedByUserId",
                table: "Tickets");

            migrationBuilder.DropIndex(
                name: "IX_Orders_VoucherId1",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_CommissionPayments_UserId",
                table: "CommissionPayments");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_UserId1",
                table: "AuditLogs");

            migrationBuilder.DropColumn(
                name: "LastOtpCode",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "OtpExpiresAt",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "PreferredOtpMethod",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "TwoFactorEnabled",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "TwoFactorSecret",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "AccessLocation",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "LastReprintedAt",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "PlannedVisitDate",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "PlannedVisitTime",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "PosDeviceId",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "ReprintCount",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "ValidatedAtDeviceId",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "ValidatedByUserId",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "ErrorDetails",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "Floor",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "HardwareModel",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "IpAddress",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "LastErrorAt",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "LastMaintenanceAt",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "LastUpdateAt",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "Longitude",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "MacAddress",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "OfflineCount",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "OperatingSystem",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "PrinterStatus",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "SerialNumber",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "UpdateStatus",
                table: "PosDevices");

            migrationBuilder.DropColumn(
                name: "VoucherId1",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "AvailableVariables",
                table: "NotificationTemplates");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "NotificationTemplates");

            migrationBuilder.DropColumn(
                name: "EventType",
                table: "NotificationTemplates");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "NotificationTemplates");

            migrationBuilder.DropColumn(
                name: "LanguageCode",
                table: "NotificationTemplates");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "CommissionPayments");

            migrationBuilder.DropColumn(
                name: "UserId1",
                table: "AuditLogs");

            migrationBuilder.DropColumn(
                name: "ApprovalStatus",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "CurrentMonthTicketsSold",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "LastLimitResetDate",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "MonthlyTicketLimit",
                table: "Agents");

            migrationBuilder.DropColumn(
                name: "SuspensionReason",
                table: "Agents");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "Vouchers",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "Users",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "TicketTypes",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "RetailChannels",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsHoliday",
                table: "PricingRules",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: false);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "PricingRules",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "PaidAmount",
                table: "PaymentTransactions",
                type: "numeric(19,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(19,4)",
                oldDefaultValue: 0m);

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountAmount",
                table: "Orders",
                type: "numeric(19,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(19,4)",
                oldDefaultValue: 0m);

            migrationBuilder.AlterColumn<bool>(
                name: "IsSpecialDay",
                table: "OperatingHours",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: false);

            migrationBuilder.AlterColumn<string>(
                name: "EntityType",
                table: "AuditLogs",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50,
                oldDefaultValue: "");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "Agents",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "AgentApiKeys",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Agents_Agents_ParentAgentId",
                table: "Agents",
                column: "ParentAgentId",
                principalTable: "Agents",
                principalColumn: "Id");
        }
    }
}
