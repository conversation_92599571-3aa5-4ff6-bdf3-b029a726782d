namespace Bitexco.Ticket.Infrastructure.Repositories;

using System.Threading;
using System.Threading.Tasks;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;

public class UserRepository(IApplicationDbContext dbContext) : IUserRepository
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<bool> CreateAsync(User user, CancellationToken cancellationToken)
    {
        await _dbContext.Users.AddAsync(user, cancellationToken);
        return await _dbContext.SaveChangesAsync(cancellationToken) > 0;
    }
    
    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken)
    {
        return await _dbContext.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.Email == email, cancellationToken);
    }
    
    public async Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken)
    {
        return await _dbContext.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.UserName == username, cancellationToken);
    }
    
    public async Task<bool> UpdateRefreshTokenAsync(Guid userId, string refreshToken, DateTime refreshTokenExpiryTime, CancellationToken cancellationToken)
    {
        var user = await _dbContext.Users.AsTracking()
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
        if (user == null) return false;
        
        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiryTime = refreshTokenExpiryTime;
        
        await _dbContext.SaveChangesAsync(cancellationToken);
        return true;
    }
    
    public async Task UpdateLastLoginAsync(Guid userId, CancellationToken cancellationToken)
    {
        var user = await _dbContext.Users.AsTracking()
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
        if (user == null) return;
        
        user.LastLoginAt = DateTime.UtcNow;
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
    
    public async Task<User?> GetByRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken)
    {
        return await _dbContext.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.RefreshToken == refreshToken, cancellationToken);
    }
    
    public async Task<bool> RemoveRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.RefreshToken == refreshToken, cancellationToken);
        if (user == null) return false;
        
        user.RefreshToken = null;
        user.RefreshTokenExpiryTime = null;
        
        await _dbContext.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<bool> UpdatePasswordAsync(Guid userId, string newPasswordHash, CancellationToken cancellationToken)
    {
        var user = await _dbContext.Users.AsTracking()
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
        if (user == null) return false;
        
        user.PasswordHash = newPasswordHash;
        user.UpdatedAt = DateTime.UtcNow;
        
        await _dbContext.SaveChangesAsync(cancellationToken);
        return true;
    }
}
