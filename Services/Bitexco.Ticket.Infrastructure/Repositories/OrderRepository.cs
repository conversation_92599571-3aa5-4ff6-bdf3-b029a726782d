namespace Bitexco.Ticket.Infrastructure.Repositories;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Repository implementation for Order-related operations
/// </summary>
public class OrderRepository(ApplicationDbContext dbContext) : IOrderRepository
{
    private readonly ApplicationDbContext _dbContext = dbContext;

    /// <inheritdoc />
    public async Task<Order> CreateOrderAsync(Order order, CancellationToken cancellationToken)
    {
        await _dbContext.Orders.AddAsync(order, cancellationToken);
        var result = await _dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new InvalidOperationException("Failed to create order");
        }
        return order;
    }

    /// <inheritdoc />
    public async Task<Order?> GetOrderByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        return await _dbContext.Orders
            .AsTracking()
            .FirstOrDefaultAsync(o => o.Id == id, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<Order?> GetOrderDetailByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        return await _dbContext.Orders
            .AsNoTracking()
            .Include(o => o.Customer)
            .Include(o => o.Agent)
            .Include(o => o.RetailChannel)
            .Include(o => o.PosUser)
            .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.TicketType)
            .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Tickets)
            .Include(o => o.PaymentTransactions)
            .Include(o => o.Invoice)
            .FirstOrDefaultAsync(o => o.Id == id, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<(IEnumerable<Order> Orders, int TotalCount)> GetOrdersAsync(
        int pageNumber,
        int pageSize,
        DateOnly? startDate = null,
        DateOnly? endDate = null,
        Guid? customerId = null,
        Guid? agentId = null,
        OrderStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        // Basic query that will be refined with filters
        IQueryable<Order> query = _dbContext.Orders
            .Include(o => o.Customer)
            .Include(o => o.Agent)
            .Include(o => o.RetailChannel)
            .AsQueryable();

        // Apply filters if provided
        if (startDate.HasValue)
        {
            query = query.Where(o => o.OrderDate >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(o => o.OrderDate <= endDate.Value);
        }

        if (customerId.HasValue)
        {
            query = query.Where(o => o.CustomerId == customerId.Value);
        }

        if (agentId.HasValue)
        {
            query = query.Where(o => o.AgentId == agentId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(o => o.Status == status.Value);
        }

        // Get total count for pagination
        int totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var paginatedOrders = await query
            .OrderByDescending(o => o.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (paginatedOrders, totalCount);
    }

    /// <inheritdoc />
    public async Task<Order?> UpdateOrderStatusAsync(Guid id, OrderStatus status, CancellationToken cancellationToken)
    {
        var order = await _dbContext.Orders.AsTracking()
            .FirstOrDefaultAsync(o => o.Id == id, cancellationToken);

        if (order == null)
        {
            return null;
        }

        order.Status = status;

        await _dbContext.SaveChangesAsync(cancellationToken);
        return order;
    }

    /// <inheritdoc />
    public async Task<Order?> ProcessRefundAsync(Guid id, string reason, CancellationToken cancellationToken)
    {
        var order = await _dbContext.Orders.AsTracking()
            .FirstOrDefaultAsync(o => o.Id == id, cancellationToken);

        if (order == null)
        {
            return null;
        }

        order.Status = OrderStatus.refunded;
        order.RefundStatus = OrderRefundStatus.refunded;
        order.RefundReason = reason;

        await _dbContext.SaveChangesAsync(cancellationToken);
        return order;
    }

    /// <inheritdoc />
    public async Task<bool> UserCanApproveOrderActionAsync(Guid orderId, Guid userId, CancellationToken cancellationToken)
    {
        // Check if user has permission to approve actions on this order
        // This is a simplified implementation, real implementation would check user roles
        var user = await _dbContext.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);

        if (user == null)
        {
            return false;
        }

        // Admin users can approve any order action
        if (user.Role?.Name.ToLower() == "admin")
        {
            return true;
        }

        // POS users can approve actions on orders they created
        var order = await _dbContext.Orders.FindAsync([orderId], cancellationToken);
        if (order?.PosUserId == userId)
        {
            return true;
        }

        // Other user types would be checked here as needed
        // e.g., managers can approve orders within their department

        return false;
    }

    /// <inheritdoc />
    public async Task<bool> CancelOrderAsync(Guid id, CancellationToken cancellationToken)
    {
        var order = await _dbContext.Orders.AsTracking()
            .FirstOrDefaultAsync(o => o.Id == id, cancellationToken);

        if (order == null)
        {
            return false;
        }

        // Soft delete by updating status to cancelled
        order.Status = OrderStatus.cancelled;

        await _dbContext.SaveChangesAsync(cancellationToken);
        return true;
    }
}
