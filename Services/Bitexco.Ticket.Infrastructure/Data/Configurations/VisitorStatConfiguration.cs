using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class VisitorStatConfiguration : IEntityTypeConfiguration<VisitorStat>
{
    public void Configure(EntityTypeBuilder<VisitorStat> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Required properties
        builder.Property(c => c.StatDate)
            .IsRequired();

        builder.Property(c => c.TicketTypeName)
            .HasMaxLength(50);

        // Configure decimal properties
        builder.Property(c => c.TotalRevenue)
            .HasColumnType("decimal(19,4)")
            .HasDefaultValue(0);

        // Configure int properties with defaults
        builder.Property(c => c.VisitorCount)
            .HasDefaultValue(0);

        builder.Property(c => c.OnlineTicketCount)
            .HasDefaultValue(0);

        builder.Property(c => c.OfflineTicketCount)
            .HasDefaultValue(0);

        builder.Property(c => c.AgentTicketCount)
            .HasDefaultValue(0);

        builder.Property(c => c.AdultCount)
            .HasDefaultValue(0);

        builder.Property(c => c.ChildCount)
            .HasDefaultValue(0);

        builder.Property(c => c.SeniorCount)
            .HasDefaultValue(0);

        builder.Property(c => c.CashPaymentCount)
            .HasDefaultValue(0);

        builder.Property(c => c.PosPaymentCount)
            .HasDefaultValue(0);

        builder.Property(c => c.TransferPaymentCount)
            .HasDefaultValue(0);

        builder.Property(c => c.EWalletPaymentCount)
            .HasDefaultValue(0);

        builder.Property(c => c.PeakHourCount)
            .HasDefaultValue(0);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
