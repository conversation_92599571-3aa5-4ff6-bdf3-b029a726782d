﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class OrderItemConfiguration : IEntityTypeConfiguration<OrderItem>
{
    public void Configure(EntityTypeBuilder<OrderItem> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id).IsUnique();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.OrderId)
            .IsRequired();

        builder.Property(c => c.TicketTypeId)
            .IsRequired();

        builder.Property(c => c.Quantity)
            .IsRequired();

        builder.Property(c => c.UnitPrice)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.SubTotal)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.VisitDate)
            .IsRequired();

        builder.Property(c => c.VisitTime)
            .IsRequired();

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure relationships
        builder.HasOne(o => o.Order)
            .WithMany(o => o.OrderItems)
            .HasForeignKey(o => o.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(o => o.TicketType)
            .WithMany(tt => tt.OrderItems)
            .HasForeignKey(o => o.TicketTypeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(o => o.Tickets)
            .WithOne(t => t.OrderItem)
            .HasForeignKey(t => t.OrderItemId);
    }
}
