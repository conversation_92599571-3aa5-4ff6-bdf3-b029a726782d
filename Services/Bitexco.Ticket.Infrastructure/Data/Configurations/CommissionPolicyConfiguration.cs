﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class CommissionPolicyConfiguration : IEntityTypeConfiguration<CommissionPolicy>
{
    public void Configure(EntityTypeBuilder<CommissionPolicy> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure required properties
        builder.Property(c => c.AgentId)
            .IsRequired();

        builder.Property(c => c.PolicyName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.CommissionRatePercentage)
            .HasColumnType("decimal(5,2)");

        builder.Property(c => c.FixedCommissionAmount)
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.EffectiveFromDate)
            .IsRequired();

        // Configure relationships
        builder.HasOne(c => c.Agent)
            .WithMany(a => a.CommissionPolicies)
            .HasForeignKey(c => c.AgentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
