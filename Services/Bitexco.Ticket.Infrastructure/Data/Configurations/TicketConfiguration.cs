﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class TicketConfiguration : IEntityTypeConfiguration<Domain.Models.Ticket>
{
    public void Configure(EntityTypeBuilder<Domain.Models.Ticket> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id).IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.Code)
            .IsRequired()
            .HasMaxLength(50);

        builder.HasIndex(c => c.Code);

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.TicketTypeId)
            .IsRequired();

        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(TicketStatus.created)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (TicketStatus)Enum.Parse(typeof(TicketStatus), dbStatus));

        builder.Property(c => c.AccessLocation)
            .HasMaxLength(50);

        builder.Property(c => c.AuditInfo)
            .HasColumnType("jsonb");

        // Navigation properties
        builder.HasOne(c => c.OrderItem)
            .WithMany(o => o.Tickets)
            .HasForeignKey(c => c.OrderItemId);

        builder.HasOne(c => c.Agent)
            .WithMany()
            .HasForeignKey(c => c.AgentId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        builder.HasOne(c => c.TicketType)
            .WithMany(tt => tt.Tickets)
            .HasForeignKey(c => c.TicketTypeId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        builder.HasOne(c => c.Customer)
            .WithMany(c => c.Tickets)
            .HasForeignKey(c => c.CustomerId);

        builder.HasOne(c => c.ValidatedByUser)
            .WithMany()
            .HasForeignKey(c => c.ValidatedByUserId);

        builder.HasOne(c => c.ValidatedAtDevice)
            .WithMany()
            .HasForeignKey(c => c.ValidatedAtDeviceId);

        builder.HasMany(c => c.TicketHistories)
            .WithOne(th => th.Ticket)
            .HasForeignKey(th => th.TicketId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasOne(c => c.RetailChannel)
            .WithMany()
            .HasForeignKey(c => c.RetailChannelId)
            .OnDelete(DeleteBehavior.Restrict);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
