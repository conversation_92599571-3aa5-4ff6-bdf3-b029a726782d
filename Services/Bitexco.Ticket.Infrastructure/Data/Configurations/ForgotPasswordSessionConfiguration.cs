﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class ForgotPasswordSessionConfiguration : IEntityTypeConfiguration<ForgotPasswordSession>
{
    public void Configure(EntityTypeBuilder<ForgotPasswordSession> builder)
    {
        builder.HasKey(s => s.Id);
        builder.HasIndex(s => s.Id)
            .IsUnique();

        builder.Property(s => s.Id)
            .ValueGeneratedOnAdd();
        
        builder.HasQueryFilter(s => !s.IsDeleted);
        
        builder.Property(s => s.UserId)
            .IsRequired();

        builder.Property(s => s.ExpiresAt)
            .IsRequired();
        
        builder.Property(s => s.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(s => s.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
        
        builder.HasOne(s => s.User)
            .WithMany()
            .HasForeignKey(s => s.UserId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
    }
}
