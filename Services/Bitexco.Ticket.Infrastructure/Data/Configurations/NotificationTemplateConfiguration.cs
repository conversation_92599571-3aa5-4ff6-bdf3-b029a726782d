﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class NotificationTemplateConfiguration : IEntityTypeConfiguration<NotificationTemplate>
{
    public void Configure(EntityTypeBuilder<NotificationTemplate> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.Type)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(NotificationTemplateType.email)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (NotificationTemplateType)Enum.Parse(typeof(NotificationTemplateType), dbStatus));

        builder.Property(c => c.Subject)
            .HasMaxLength(255);

        builder.Property(c => c.Body)
            .IsRequired()
            .HasColumnType("text");

        builder.Property(c => c.LanguageCode)
            .HasMaxLength(10)
            .HasDefaultValue("vi");

        builder.Property(c => c.Description)
            .HasMaxLength(255);

        builder.Property(c => c.AvailableVariables)
            .HasColumnType("jsonb");

        builder.Property(c => c.EventType)
            .HasMaxLength(50);

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
