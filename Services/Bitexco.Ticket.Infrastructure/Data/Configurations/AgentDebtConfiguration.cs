using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class AgentDebtConfiguration : IEntityTypeConfiguration<AgentDebt>
{
    public void Configure(EntityTypeBuilder<AgentDebt> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Property configurations
        builder.Property(c => c.AgentId)
            .IsRequired();

        builder.Property(c => c.OrderId)
            .IsRequired();

        builder.Property(c => c.OriginalAmount)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.PaidAmount)
            .HasColumnType("decimal(19,4)")
            .HasDefaultValue(0);

        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(AgentDebtStatus.outstanding)
            .HasConversion<string>();

        builder.Property(c => c.PaymentMethod)
            .HasMaxLength(30)
            .HasConversion<string>();

        builder.Property(c => c.Notes)
            .HasColumnType("text");

        builder.Property(c => c.PaymentProofFilePath)
            .HasMaxLength(500);

        builder.Property(c => c.InvoiceFilePath)
            .HasMaxLength(500);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Relationships
        builder.HasOne(c => c.Agent)
            .WithMany()
            .HasForeignKey(c => c.AgentId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.Order)
            .WithMany()
            .HasForeignKey(c => c.OrderId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.ApprovedByUser)
            .WithMany()
            .HasForeignKey(c => c.ApprovedByUserId)
            .OnDelete(DeleteBehavior.SetNull);

        // Indexes for performance
        builder.HasIndex(c => c.AgentId)
            .HasDatabaseName("IX_AgentDebts_AgentId");

        builder.HasIndex(c => c.OrderId)
            .HasDatabaseName("IX_AgentDebts_OrderId")
            .IsUnique(); // One debt per order

        builder.HasIndex(c => c.Status)
            .HasDatabaseName("IX_AgentDebts_Status");

        builder.HasIndex(c => new { c.AgentId, c.Status })
            .HasDatabaseName("IX_AgentDebts_AgentId_Status");
    }
}
