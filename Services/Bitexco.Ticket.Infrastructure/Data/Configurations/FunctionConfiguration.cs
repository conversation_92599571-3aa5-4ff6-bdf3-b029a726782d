﻿using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class FunctionConfiguration : IEntityTypeConfiguration<Function>
{
    public void Configure(EntityTypeBuilder<Function> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure properties
        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.Code)
            .HasMaxLength(100);

        builder.Property(c => c.Description)
            .HasMaxLength(500);

        builder.Property(c => c.Path)
            .HasMaxLength(500);

        builder.Property(c => c.IconPath)
            .HasMaxLength(500);

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
