﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class CustomerConfiguration : IEntityTypeConfiguration<Customer>
{
    public void Configure(EntityTypeBuilder<Customer> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.Code)
            .IsRequired()
            .HasMaxLength(50);

        // Configure required properties
        builder.Property(c => c.FullName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.PhoneNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(c => c.Email)
            .HasMaxLength(100);

        builder.Property(c => c.Type)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(CustomerType.individual)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (CustomerType)Enum.Parse(typeof(CustomerType), dbStatus));

        builder.Property(c => c.CompanyName)
            .HasMaxLength(100);

        builder.Property(c => c.TaxCode)
            .HasMaxLength(50);

        builder.Property(c => c.Address)
            .HasMaxLength(255);

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure relationships
        builder.HasMany(c => c.Orders)
            .WithOne(o => o.Customer)
            .HasForeignKey(o => o.CustomerId);

        builder.HasMany(c => c.Tickets)
            .WithOne(t => t.Customer)
            .HasForeignKey(t => t.CustomerId);

        builder.HasMany(c => c.Invoices)
            .WithOne(i => i.Customer)
            .HasForeignKey(i => i.CustomerId);

        builder.HasMany(c => c.SupportTickets)
            .WithOne(st => st.Customer)
            .HasForeignKey(st => st.CustomerId);
    }
}
