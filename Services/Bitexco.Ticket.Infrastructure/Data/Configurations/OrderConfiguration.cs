﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class OrderConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id).IsUnique();

        builder.HasIndex(c => c.Code);

        builder.Property(c => c.Code)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValueSql("CONCAT('ORD-', TO_CHAR(NOW(), 'YYYYMMDDHH24MISS'))");

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.OrderDate)
            .IsRequired();

        builder.Property(c => c.OrderTime)
            .IsRequired();

        builder.Property(c => c.TotalAmount)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.DiscountAmount)
            .HasColumnType("decimal(19,4)")
            .HasDefaultValue(0);

        builder.Property(c => c.CouponAmount)
            .HasColumnType("decimal(19,4)")
            .HasDefaultValue(0);

        builder.Property(c => c.DepositAmount)
            .HasColumnType("decimal(19,4)")
            .HasDefaultValue(0);

        builder.Property(c => c.FinalAmount)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(o => o.Status)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(OrderStatus.pending_payment)
            .HasConversion<string>();

        builder.Property(c => c.LastPaymentMethod)
            .HasMaxLength(50)
            .HasConversion<string>();

        builder.Property(o => o.CustomerType)
            .IsRequired()
            .HasDefaultValue(CustomerType.individual)
            .HasConversion<string>();

        builder.Property(o => o.RefundStatus)
            .HasMaxLength(50)
            .HasConversion<string>();
        
        builder.Property(c => c.RefundPaymentMethod)
            .HasMaxLength(50)
            .HasDefaultValue(RefundPaymentMethod.cash)
            .HasConversion<string>();

        builder.Property(c => c.RefundReason)
            .HasColumnType("text");

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure relationships
        builder.HasOne(o => o.Customer)
            .WithMany(c => c.Orders)
            .HasForeignKey(o => o.CustomerId);

        builder.HasOne(o => o.Agent)
            .WithMany(a => a.Orders)
            .HasForeignKey(o => o.AgentId);

        builder.HasOne(o => o.RetailChannel)
            .WithMany(rc => rc.Orders)
            .HasForeignKey(o => o.RetailChannelId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(o => o.PosUser)
            .WithMany(u => u.Orders)
            .HasForeignKey(o => o.PosUserId);

        builder.HasOne(o => o.PosDevice)
            .WithMany(d => d.Orders)
            .HasForeignKey(o => o.PosDeviceId);

        builder.HasOne(o => o.Voucher)
            .WithMany(v => v.Orders)
            .HasForeignKey(o => o.VoucherId);

        builder.HasMany(o => o.OrderItems)
            .WithOne(oi => oi.Order)
            .HasForeignKey(oi => oi.OrderId);

        builder.HasMany(o => o.PaymentTransactions)
            .WithOne(pt => pt.Order)
            .HasForeignKey(pt => pt.OrderId);
    }
}
