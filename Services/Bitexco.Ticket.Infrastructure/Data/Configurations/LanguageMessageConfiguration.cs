using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class LanguageMessageConfiguration : IEntityTypeConfiguration<LanguageMessage>
{

    public void Configure(EntityTypeBuilder<LanguageMessage> builder)
    {
        builder.HasKey(c => c.Key);
        builder.HasIndex(c => c.Key)
            .IsUnique();

        // Configure properties
        builder.Property(c => c.Key)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.Message)
            .IsRequired()
            .HasColumnType("text");
    }
}