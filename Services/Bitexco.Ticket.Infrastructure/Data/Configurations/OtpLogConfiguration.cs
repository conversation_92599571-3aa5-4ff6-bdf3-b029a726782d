using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Bitexco.Ticket.Domain.Models;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class OtpLogConfiguration : IEntityTypeConfiguration<OtpLog>
{
    public void Configure(EntityTypeBuilder<OtpLog> builder)
    {
        // Primary key and index configuration
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        // Filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Required properties
        builder.Property(c => c.UserId)
            .IsRequired();

        builder.Property(c => c.OtpCode)
            .IsRequired()
            .HasMaxLength(10);

        builder.Property(c => c.ExpiresAt)
            .IsRequired();

        builder.Property(c => c.Method)
            .HasMaxLength(20)
            .HasDefaultValue("email");

        builder.Property(c => c.SentTo)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue("sent");

        builder.Property(c => c.VerifiedAt)
            .IsRequired(false);

        builder.Property(c => c.FailedAttempts)
            .HasDefaultValue(0);

        builder.Property(c => c.IpAddress)
            .HasMaxLength(50);

        builder.Property(c => c.UserAgent)
            .HasMaxLength(255);

        builder.Property(c => c.ValidationHash)
            .HasMaxLength(255);

        // Navigation property
        builder.HasOne(o => o.User)
            .WithMany()
            .HasForeignKey(o => o.UserId)
            .IsRequired();

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
