﻿using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class AuditLogConfiguration : IEntityTypeConfiguration<AuditLog>
{
    public void Configure(EntityTypeBuilder<AuditLog> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Property configurations (moved from attributes)
        builder.Property(c => c.ActionType)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(AuditLogActionType.create)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (AuditLogActionType)Enum.Parse(typeof(AuditLogActionType), dbStatus));

        builder.Property(c => c.EntityType)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(string.Empty);

        builder.Property(c => c.OldValue)
            .HasColumnType("jsonb");

        builder.Property(c => c.NewValue)
            .HasColumnType("jsonb");

        builder.Property(c => c.IpAddress)
            .HasMaxLength(50);

        builder.Property(c => c.Timestamp)
            .IsRequired();

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Relationships
        builder.HasOne(al => al.User)
            .WithMany()
            .HasForeignKey(al => al.UserId)
            .IsRequired(false);
    }
}
