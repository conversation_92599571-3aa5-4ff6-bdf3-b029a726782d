using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class VisitorFlowConfiguration : IEntityTypeConfiguration<VisitorFlow>
{
    public void Configure(EntityTypeBuilder<VisitorFlow> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Required properties
        builder.Property(c => c.Timestamp)
            .IsRequired();

        builder.Property(c => c.EntryPoint)
            .HasMaxLength(50)
            .HasDefaultValue("main");

        builder.Property(c => c.EntryCount)
            .HasDefaultValue(0);

        builder.Property(c => c.ExitCount)
            .HasDefaultValue(0);

        builder.Property(c => c.CurrentOccupancy)
            .HasDefaultValue(0);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
