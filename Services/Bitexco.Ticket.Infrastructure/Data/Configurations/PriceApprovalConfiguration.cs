using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class PriceApprovalConfiguration : IEntityTypeConfiguration<PriceApproval>
{
    public void Configure(EntityTypeBuilder<PriceApproval> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Required properties
        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(PriceApprovalStatus.pending)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (PriceApprovalStatus)Enum.Parse(typeof(PriceApprovalStatus), dbStatus));

        builder.Property(c => c.ProposedChanges)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(c => c.RequestComments)
            .HasColumnType("text");

        builder.Property(c => c.RequestedByUserId)
            .IsRequired();

        builder.Property(c => c.RequestedAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.ApprovalComments)
            .HasColumnType("text");

        // Navigation properties
        builder.HasOne(c => c.TicketType)
            .WithMany(tt => tt.PriceApprovals)
            .HasForeignKey(c => c.TicketTypeId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        builder.HasOne(c => c.RequestedByUser)
            .WithMany()
            .HasForeignKey(c => c.RequestedByUserId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.ApprovedByUser)
            .WithMany()
            .HasForeignKey(c => c.ApprovedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
