﻿using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class VoucherConfiguration : IEntityTypeConfiguration<Voucher>
{
    public void Configure(EntityTypeBuilder<Voucher> builder)
    {
        builder.<PERSON><PERSON>ey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Property configurations (moved from attributes)
        builder.Property(c => c.Code)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(c => c.Type)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(VoucherType.percentage)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (VoucherType)Enum.Parse(typeof(VoucherType), dbStatus));

        builder.Property(c => c.VoucherApplyType)
            .HasConversion(
                s => s.ToString(),
                dbStatus => dbStatus != null 
                    ? (VoucherApplyType)Enum.Parse(typeof(VoucherApplyType), dbStatus) 
                    : null);

        builder.Property(c => c.AgentIds)
            .HasConversion(
                v => v != null ? string.Join(',', v) : null,
                v => v != null ? v.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToList() : null);

        builder.Property(c => c.DiscountValue)
            .IsRequired()
            .HasColumnType("decimal(19,4)");
        
        builder.HasOne(c => c.VoucherCampaign)
            .WithMany(vc => vc.Vouchers)
            .HasForeignKey(c => c.VoucherCampaignId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(c => c.ValidFrom)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
            
        builder.Property(c => c.ValidTo)
            .IsRequired();

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
            
        // Relationships
        builder.HasMany(c => c.VoucherChannels)
            .WithOne(vc => vc.Voucher)
            .HasForeignKey(vc => vc.VoucherId);
            
        builder.HasMany(c => c.Orders)
            .WithOne()
            .HasForeignKey("VoucherId");
    }
}
