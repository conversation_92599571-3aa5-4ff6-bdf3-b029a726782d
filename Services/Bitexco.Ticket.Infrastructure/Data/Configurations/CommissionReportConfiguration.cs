﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class CommissionReportConfiguration : IEntityTypeConfiguration<CommissionReport>
{
    public void Configure(EntityTypeBuilder<CommissionReport> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.AgentId)
            .IsRequired();

        builder.Property(c => c.StartDate)
            .IsRequired();

        builder.Property(c => c.EndDate)
            .IsRequired();

        builder.Property(c => c.TotalSalesAmount)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.CalculatedCommissionAmount)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(CommissionReportStatus.generated)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (CommissionReportStatus)Enum.Parse(typeof(CommissionReportStatus), dbStatus));

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure relationships
        builder.HasOne(c => c.Agent)
            .WithMany(a => a.CommissionReports)
            .HasForeignKey(c => c.AgentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(c => c.ApprovedByUser)
            .WithMany(u => u.ApprovedCommissionReports)
            .HasForeignKey(c => c.ApprovedByUserId);

        builder.HasMany(c => c.CommissionPayments)
            .WithOne(p => p.CommissionReport)
            .HasForeignKey(p => p.CommissionReportId);
    }
}
