using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class TicketTypeConditionConfiguration : IEntityTypeConfiguration<TicketTypeCondition>
{
    public void Configure(EntityTypeBuilder<TicketTypeCondition> builder)
    {
        builder.<PERSON>Key(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Required properties
        builder.Property(c => c.Name)
            .HasMaxLength(100);

        builder.Property(c => c.Description)
            .HasColumnType("text");

        builder.Property(c => c.FromValue)
            .IsRequired(false);

        builder.Property(c => c.ToValue)
            .IsRequired(false);

        builder.Property(c => c.DisplayOrder)
            .HasDefaultValue(0);

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        builder.Property(c => c.Type)
         .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(TicketTypeConditionType.TimeRange)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (TicketTypeConditionType)Enum.Parse(typeof(TicketTypeConditionType), dbStatus));

        // Navigation properties
        builder.HasOne(c => c.TicketType)
            .WithMany(tt => tt.Conditions)
            .HasForeignKey(c => c.TicketTypeId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
