﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class GroupBookingConfiguration : IEntityTypeConfiguration<GroupBooking>
{
    public void Configure(EntityTypeBuilder<GroupBooking> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        builder.Property(c => c.Code)
            .HasMaxLength(50)
            .IsRequired();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.Type)
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(c => c.DepositAmount)
            .HasColumnType("decimal(19,4)")
            .HasDefaultValue(0)
            .IsRequired();

        builder.Property(c => c.Status)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Relationships
        builder.HasOne(a => a.Agent)
            .WithMany()
            .HasForeignKey(a => a.AgentId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(a => a.Orders)
            .WithOne(o => o.GroupBooking)
            .HasForeignKey(o => o.GroupBookingId)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.HasMany(a => a.GroupBookingBuses)
            .WithOne(o => o.GroupBooking)
            .HasForeignKey(o => o.GroupBookingId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
