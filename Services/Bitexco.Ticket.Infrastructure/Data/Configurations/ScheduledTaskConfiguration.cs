﻿using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class ScheduledTaskConfiguration : IEntityTypeConfiguration<ScheduledTask>
{
    public void Configure(EntityTypeBuilder<ScheduledTask> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();
        
        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure properties
        builder.Property(c => c.TaskName)
            .IsRequired()
            .HasMaxLength(100);
        
        builder.Property(c => c.TaskDescription)
            .IsRequired()
            .HasColumnType("text");

        builder.Property(c => c.TaskType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(c => c.Schedule)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(c => c.IsEnabled)
            .HasDefaultValue(true);

        builder.Property(c => c.LastRunStatus)
            .HasMaxLength(50);

        builder.Property(c => c.LastRunResult)
            .HasColumnType("text");

        builder.Property(c => c.Parameters)
            .HasColumnType("jsonb");

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
