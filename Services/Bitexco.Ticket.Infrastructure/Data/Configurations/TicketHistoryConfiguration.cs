﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class TicketHistoryConfiguration : IEntityTypeConfiguration<TicketHistory>
{
    public void Configure(EntityTypeBuilder<TicketHistory> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id).IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        builder.Property(c => c.FromStatus)
            .HasMaxLength(50)
            .HasConversion(
                s => s == null ? null : s.ToString(),  
                dbStatus => string.IsNullOrEmpty(dbStatus) ? null : (TicketStatus)Enum.Parse(typeof(TicketStatus), dbStatus));

        builder.Property(c => c.ToStatus)
            .IsRequired()
            .HasMaxLength(50)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (TicketStatus)Enum.Parse(typeof(TicketStatus), dbStatus));

        builder.HasOne(c => c.Ticket)
            .WithMany(t => t.TicketHistories)
            .HasForeignKey(c => c.TicketId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
