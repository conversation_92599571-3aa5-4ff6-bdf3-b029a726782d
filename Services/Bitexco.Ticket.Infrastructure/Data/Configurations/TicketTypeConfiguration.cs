﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class TicketTypeConfiguration : IEntityTypeConfiguration<TicketType>
{
    public void Configure(EntityTypeBuilder<TicketType> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.Code)
            .IsRequired()
            .HasMaxLength(50);

        // Required properties
        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(c => c.Description)
            .HasColumnType("text");

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        builder.Property(c => c.TicketExpirationDurationUnit)
            .HasConversion(
                v => v.ToString(),
                v => v != null ? (TicketExpirationDurationUnit)Enum.Parse(typeof(TicketExpirationDurationUnit), v) : null)
            .HasMaxLength(50);

        // Navigation properties
        builder.HasMany(c => c.TicketPrices)
            .WithOne(tp => tp.TicketType)
            .HasForeignKey(tp => tp.TicketTypeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.OrderItems)
            .WithOne(oi => oi.TicketType)
            .HasForeignKey(oi => oi.TicketTypeId);

        builder.HasMany(c => c.Tickets)
            .WithOne(t => t.TicketType)
            .HasForeignKey(t => t.TicketTypeId);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
