﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class CommissionPaymentConfiguration : IEntityTypeConfiguration<CommissionPayment>
{
    public void Configure(EntityTypeBuilder<CommissionPayment> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Property configurations (moved from attributes)
        builder.Property(c => c.AgentId)
            .IsRequired();

        builder.Property(c => c.CommissionReportId)
            .IsRequired();

        builder.Property(c => c.Amount)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.PaymentDate)
            .IsRequired();

        builder.Property(c => c.PaymentMethod)
            .HasMaxLength(50)
            .HasDefaultValue(CommissionPaymentMethod.bank_transfer)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (CommissionPaymentMethod)Enum.Parse(typeof(CommissionPaymentMethod), dbStatus));

        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(CommissionPaymentStatus.pending)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (CommissionPaymentStatus)Enum.Parse(typeof(CommissionPaymentStatus), dbStatus));

        // Relationships
        builder.HasOne(cp => cp.Agent)
            .WithMany(a => a.CommissionPayments)
            .HasForeignKey(cp => cp.AgentId);

        builder.HasOne(cp => cp.CommissionReport)
            .WithMany(cr => cr.CommissionPayments)
            .HasForeignKey(cp => cp.CommissionReportId);

        builder.HasOne(cp => cp.ApprovedByUser)
            .WithMany()
            .HasForeignKey(cp => cp.ApprovedByUserId);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}