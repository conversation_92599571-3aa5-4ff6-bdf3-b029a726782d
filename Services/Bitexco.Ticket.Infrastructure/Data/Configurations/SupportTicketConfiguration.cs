﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class SupportTicketConfiguration : IEntityTypeConfiguration<SupportTicket>
{
    public void Configure(EntityTypeBuilder<SupportTicket> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.Subject)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(c => c.Description)
            .IsRequired()
            .HasColumnType("text");

        builder.Property(c => c.Status)
            .HasDefaultValue(SupportTicketStatus.open)
            .HasMaxLength(50)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (SupportTicketStatus)Enum.Parse(typeof(SupportTicketStatus), dbStatus));

        builder.Property(c => c.Priority)
            .HasDefaultValue(SupportTicketPriority.low)
            .HasMaxLength(20)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (SupportTicketPriority)Enum.Parse(typeof(SupportTicketPriority), dbStatus));

        // Navigation properties
        builder.HasOne(c => c.Customer)
            .WithMany(c => c.SupportTickets)
            .HasForeignKey(c => c.CustomerId);

        builder.HasOne(c => c.AssignedToUser)
            .WithMany(u => u.AssignedSupportTickets)
            .HasForeignKey(c => c.AssignedToUserId);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
