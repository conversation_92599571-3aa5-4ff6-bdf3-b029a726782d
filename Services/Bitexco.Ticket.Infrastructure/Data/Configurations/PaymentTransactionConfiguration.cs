﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class PaymentTransactionConfiguration : IEntityTypeConfiguration<PaymentTransaction>
{
    public void Configure(EntityTypeBuilder<PaymentTransaction> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        builder.HasIndex(c => c.Code);

        builder.Property(c => c.Code)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValueSql("CONCAT('P-', TO_CHAR(NOW(), 'YYYYMMDDHH24MISS'))");

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.OrderId)
            .IsRequired();

        builder.Property(c => c.TransactionType)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(PaymentTransactionType.income)
            .HasConversion<string>();

        builder.Property(c => c.PaymentMethod)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(PaymentTransactionMethod.pos)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (PaymentTransactionMethod)Enum.Parse(typeof(PaymentTransactionMethod), dbStatus));

        builder.Property(c => c.Amount)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.PaidAmount)
            .IsRequired()
            .HasColumnType("decimal(19,4)")
            .HasDefaultValue(0);

        builder.Property(c => c.TransactionCode)
            .HasMaxLength(100);

        builder.Property(c => c.TransactionStatus)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(PaymentTransactionStatus.pending)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (PaymentTransactionStatus)Enum.Parse(typeof(PaymentTransactionStatus), dbStatus));

        builder.Property(c => c.ReconciliationStatus)
            .HasMaxLength(50)
            .HasDefaultValue(PaymentReconciliationStatus.pending)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (PaymentReconciliationStatus)Enum.Parse(typeof(PaymentReconciliationStatus), dbStatus));

        builder.Property(c => c.TransactionAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure relationships
        builder.HasOne(p => p.Order)
            .WithMany(o => o.PaymentTransactions)
            .HasForeignKey(p => p.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(p => p.RetailChannel)
            .WithMany()
            .HasForeignKey(p => p.RetailChannelId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
