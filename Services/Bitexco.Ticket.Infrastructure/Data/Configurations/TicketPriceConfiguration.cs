﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class TicketPriceConfiguration : IEntityTypeConfiguration<TicketPrice>
{
    public void Configure(EntityTypeBuilder<TicketPrice> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Required properties
        builder.Property(c => c.Price)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.EffectiveDateFrom)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_DATE")
            .ValueGeneratedOnAdd();

        // Navigation properties
        builder.HasOne(c => c.TicketType)
            .WithMany(tt => tt.TicketPrices)
            .HasForeignKey(c => c.TicketTypeId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        builder.HasOne(c => c.PricingRule)
            .WithMany(pr => pr.TicketPrices)
            .HasForeignKey(c => c.PricingRuleId);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
