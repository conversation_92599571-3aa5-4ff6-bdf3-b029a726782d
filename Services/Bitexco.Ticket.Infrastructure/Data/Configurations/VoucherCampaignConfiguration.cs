﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class VoucherCampaignConfiguration : IEntityTypeConfiguration<VoucherCampaign>
{
    public void Configure(EntityTypeBuilder<VoucherCampaign> builder)
    {
        builder.<PERSON><PERSON><PERSON>(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(c => c.Type)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(VoucherType.percentage)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (VoucherType)Enum.Parse(typeof(VoucherType), dbStatus));

        builder.Property(c => c.VoucherApplyType)
            .HasConversion(
                s => s.ToString(),
                dbStatus => dbStatus != null 
                    ? (VoucherApplyType)Enum.Parse(typeof(VoucherApplyType), dbStatus) 
                    : null);

        builder.Property(c => c.AgentIds)
            .HasConversion(
                v => v != null ? string.Join(',', v) : null,
                v => v != null ? v.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToList() : null);

        builder.Property(c => c.DiscountValue)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.HasMany(c => c.Vouchers)
            .WithOne(v => v.VoucherCampaign)
            .HasForeignKey(v => v.VoucherCampaignId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(c => c.ValidFrom)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
            
        builder.Property(c => c.ValidTo)
            .IsRequired();

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
