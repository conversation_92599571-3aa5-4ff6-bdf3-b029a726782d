using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class ContractConfiguration : IEntityTypeConfiguration<Contract>
{
    public void Configure(EntityTypeBuilder<Contract> builder)
    {
        builder.HasKey(x => x.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(x => x.SapContractCode)
            .HasMaxLength(50);

        builder.Property(x => x.ContractName)
            .HasMaxLength(200);

        builder.Property(x => x.ContractFileUrl)
            .HasMaxLength(500);

        builder.Property(x => x.Status)
            .HasConversion<string>();

        builder.Property(x => x.Notes)
            .HasMaxLength(1000);

        // Foreign key relationship
        builder.HasOne(x => x.Agent)
            .WithMany(x => x.Contracts)
            .HasForeignKey(x => x.AgentId)
            .OnDelete(DeleteBehavior.Cascade);

        // Index for SAP contract code
        builder.HasIndex(x => x.SapContractCode);

        // Index for agent and status
        builder.HasIndex(x => new { x.AgentId, x.Status });

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);
        
        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
