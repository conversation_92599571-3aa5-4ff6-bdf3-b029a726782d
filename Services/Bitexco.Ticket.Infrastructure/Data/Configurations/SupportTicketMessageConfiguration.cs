﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class SupportTicketMessageConfiguration : IEntityTypeConfiguration<SupportTicketMessage>
{
    public void Configure(EntityTypeBuilder<SupportTicketMessage> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.SenderType)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(SupportTicketMessageSenderType.customer)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (SupportTicketMessageSenderType)Enum.Parse(typeof(SupportTicketMessageSenderType), dbStatus));

        builder.Property(c => c.MessageContent)
            .IsRequired()
            .HasColumnType("text");

        builder.Property(c => c.SentAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        // Navigation properties
        builder.HasOne(c => c.SupportTicket)
            .WithMany(st => st.SupportTicketMessages)
            .HasForeignKey(c => c.SupportTicketId)
            .OnDelete(DeleteBehavior.Cascade);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
