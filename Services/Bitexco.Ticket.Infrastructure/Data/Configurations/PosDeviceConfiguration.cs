﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class PosDeviceConfiguration : IEntityTypeConfiguration<PosDevice>
{
    public void Configure(EntityTypeBuilder<PosDevice> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();
        
        builder.HasIndex(c => c.SerialNumber);

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure properties
        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.Location)
            .HasMaxLength(255);

        builder.Property(c => c.Latitude)
            .HasColumnType("decimal(9,6)");

        builder.Property(c => c.Longitude)
            .HasColumnType("decimal(9,6)");

        builder.Property(c => c.Floor)
            .HasMaxLength(50);

        builder.Property(c => c.Type)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(PosDeviceType.POS)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (PosDeviceType)Enum.Parse(typeof(PosDeviceType), dbStatus));

        builder.Property(c => c.FirmwareVersion)
            .HasMaxLength(50);

        builder.Property(c => c.SoftwareVersion)
            .HasMaxLength(50);

        builder.Property(c => c.OperatingSystem)
            .HasMaxLength(100);

        builder.Property(c => c.HardwareModel)
            .HasMaxLength(100);

        builder.Property(c => c.SerialNumber)
            .HasMaxLength(100);

        builder.Property(c => c.IpAddress)
            .HasMaxLength(50);

        builder.Property(c => c.MacAddress)
            .HasMaxLength(100);

        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(PosDeviceStatus.online)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (PosDeviceStatus)Enum.Parse(typeof(PosDeviceStatus), dbStatus));

        builder.Property(c => c.ErrorDetails)
            .HasColumnType("text");

        builder.Property(c => c.OfflineCount)
            .HasDefaultValue(0);

        builder.Property(c => c.PrinterStatus)
            .HasMaxLength(50)
            .HasDefaultValue("operational");

        builder.Property(c => c.UpdateStatus)
            .HasMaxLength(50)
            .HasDefaultValue("up_to_date");

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
