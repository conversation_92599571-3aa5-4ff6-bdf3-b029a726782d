using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class NotificationConfiguration : IEntityTypeConfiguration<Notification>
{
    public void Configure(EntityTypeBuilder<Notification> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.Subject)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.Content)
            .IsRequired()
            .HasColumnType("text");

        builder.Property(c => c.Type)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(NotificationType.email)
            .HasConversion(
                v => v.ToString(),
                v => (NotificationType)Enum.Parse(typeof(NotificationType), v));

        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue(NotificationStatus.pending)
            .HasConversion(
                v => v.ToString(),
                v => (NotificationStatus)Enum.Parse(typeof(NotificationStatus), v));

        builder.Property(c => c.Recipient)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.RelatedEntityType)
            .HasMaxLength(50);

        builder.Property(c => c.ErrorDetails)
            .HasColumnType("text");

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure relationships
        builder.HasOne(c => c.User)
            .WithMany()
            .HasForeignKey(c => c.UserId);
    }
}
