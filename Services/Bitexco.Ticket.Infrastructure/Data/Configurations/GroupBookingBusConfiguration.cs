﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class GroupBookingBusConfiguration : IEntityTypeConfiguration<GroupBookingBus>
{
    public void Configure(EntityTypeBuilder<GroupBookingBus> builder)
    {
        builder.<PERSON>Key(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Relationships
        builder.HasOne(a => a.GroupBooking)
            .WithMany(a => a.GroupBookingBuses)
            .HasForeignKey(a => a.GroupBookingId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
