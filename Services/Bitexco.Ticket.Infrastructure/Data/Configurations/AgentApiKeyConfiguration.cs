﻿using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class AgentApiKeyConfiguration : IEntityTypeConfiguration<AgentApiKey>
{
    public void Configure(EntityTypeBuilder<AgentApiKey> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();
        
        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Property configurations (moved from attributes)
        builder.Property(c => c.AgentId)
            .IsRequired();

        builder.Property(c => c.ApiKey)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(c => c.ApiSecret)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Relationships
        builder.HasOne(ak => ak.Agent)
            .WithMany(a => a.AgentApiKeys)
            .HasForeignKey(ak => ak.AgentId);
    }
}
