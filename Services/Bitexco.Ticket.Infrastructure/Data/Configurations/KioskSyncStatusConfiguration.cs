using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class KioskSyncStatusConfiguration : IEntityTypeConfiguration<KioskSyncStatus>
{
    public void Configure(EntityTypeBuilder<KioskSyncStatus> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Required properties
        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(c => c.StartedAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.CompletedAt)
            .IsRequired(false);

        builder.Property(c => c.ErrorMessage)
            .HasColumnType("text");

        builder.Property(c => c.SyncDetails)
            .HasColumnType("jsonb");
            
        // Navigation properties
        builder.HasOne(c => c.PosDevice)
            .WithMany()
            .HasForeignKey(c => c.PosDeviceId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
