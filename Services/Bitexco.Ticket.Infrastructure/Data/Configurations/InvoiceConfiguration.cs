﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class InvoiceConfiguration : IEntityTypeConfiguration<Invoice>
{
    public void Configure(EntityTypeBuilder<Invoice> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.Code)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.OrderId)
            .IsRequired();

        builder.Property(c => c.IssueDate)
            .IsRequired();

        builder.Property(c => c.TotalAmount)
            .IsRequired()
            .HasColumnType("decimal(19,4)");

        builder.Property(c => c.Status)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(InvoiceStatus.draft)
            .HasConversion<string>();

         builder.Property(c => c.SendMailStatus)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(SendMailStatus.unsent)
            .HasConversion<string>();

        builder.Property(c => c.CancellationReason)
            .HasColumnType("text");

        builder.Property(c => c.RevisionDetails)
            .HasColumnType("text");

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure relationships
        builder.HasOne(c => c.Order)
            .WithOne(o => o.Invoice)
            .HasForeignKey<Invoice>(i => i.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(c => c.Customer)
            .WithMany(c => c.Invoices)
            .HasForeignKey(i => i.CustomerId);

        builder.HasOne(c => c.IssuedByUser)
            .WithMany(u => u.IssuedInvoices)
            .HasForeignKey(i => i.IssuedByUserId);
    }
}
