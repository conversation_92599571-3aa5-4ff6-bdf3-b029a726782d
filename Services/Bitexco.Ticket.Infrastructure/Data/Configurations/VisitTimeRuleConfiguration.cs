using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class VisitTimeRuleConfiguration : IEntityTypeConfiguration<VisitTimeRule>
{
    public void Configure(EntityTypeBuilder<VisitTimeRule> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Required properties
        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.EarliestEntryTime)
            .IsRequired(false);

        builder.Property(c => c.LatestEntryTime)
            .IsRequired(false);

        builder.Property(c => c.MaxDurationMinutes)
            .IsRequired(false);

        builder.Property(c => c.AppliesToHolidays)
            .HasDefaultValue(false);

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        // Store days as string for EF Core compatibility
        builder.Property(c => c.DaysOfWeek)
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(int.Parse).ToList());

        // Navigation properties
        builder.HasOne(c => c.TicketType)
            .WithMany(tt => tt.VisitTimeRules)
            .HasForeignKey(c => c.TicketTypeId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
