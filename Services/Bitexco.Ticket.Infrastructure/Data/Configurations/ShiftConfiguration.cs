﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class ShiftConfiguration : IEntityTypeConfiguration<Shift>
{
    public void Configure(EntityTypeBuilder<Shift> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.StartTime)
            .IsRequired();

        builder.Property(c => c.Status)
            .HasDefaultValue(ShiftStatus.open)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (ShiftStatus)Enum.Parse(typeof(ShiftStatus), dbStatus))
            .HasMaxLength(50);
            
        builder.Property(c => c.AuditDetails)
            .HasColumnType("text");

        // Cash handling properties
        builder.Property(c => c.TotalCashSales)
            .HasColumnType("decimal(19,4)");
            
        builder.Property(c => c.TotalPosSales)
            .HasColumnType("decimal(19,4)");
            
        builder.Property(c => c.TotalTransferSales)
            .HasColumnType("decimal(19,4)");
            
        builder.Property(c => c.StartCashBalance)
            .HasColumnType("decimal(19,4)");
            
        builder.Property(c => c.EndCashBalance)
            .HasColumnType("decimal(19,4)");

        // Navigation properties
        builder.HasOne(c => c.User)
            .WithMany(u => u.Shifts)
            .HasForeignKey(c => c.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(c => c.PosDevice)
            .WithMany(d => d.Shifts)
            .HasForeignKey(c => c.PosDeviceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Orders)
            .WithOne(t => t.Shift)
            .HasForeignKey(t => t.ShiftId)
            .OnDelete(DeleteBehavior.Cascade);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
