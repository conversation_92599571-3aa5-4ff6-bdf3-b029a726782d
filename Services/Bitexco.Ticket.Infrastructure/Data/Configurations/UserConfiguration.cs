﻿using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Configure required properties
        builder.Property(c => c.UserName)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(c => c.PasswordHash)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(c => c.Email)
            .HasMaxLength(100);
            
        builder.Property(c => c.FullName)
            .HasMaxLength(100);
            
        builder.Property(c => c.PhoneNumber)
            .HasMaxLength(20);

        builder.Property(c => c.TwoFactorSecret)
            .HasMaxLength(255);

        builder.Property(c => c.LastOtpCode)
            .HasMaxLength(50);

        builder.Property(c => c.PreferredOtpMethod)
            .HasDefaultValue(PreferredOtpMethod.email)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (PreferredOtpMethod)Enum.Parse(typeof(PreferredOtpMethod), dbStatus));

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Configure relationships
        builder.Property(c => c.RoleId)
            .IsRequired(false);

        builder.HasOne(u => u.Role)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RoleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
