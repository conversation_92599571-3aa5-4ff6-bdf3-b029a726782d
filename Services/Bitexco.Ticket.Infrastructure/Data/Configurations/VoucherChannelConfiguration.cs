﻿using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;

public class VoucherChannelConfiguration : IEntityTypeConfiguration<VoucherChannel>
{
    public void Configure(EntityTypeBuilder<VoucherChannel> builder)
    {
        builder.HasKey(c => new { c.VoucherId, c.ChannelId });
        builder.HasIndex(c => new { c.VoucherId, c.ChannelId }).IsUnique();

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
            
        // Relationships
        builder.HasOne(vc => vc.Voucher)
            .WithMany(v => v.VoucherChannels)
            .HasForeignKey(vc => vc.VoucherId);
            
        builder.HasOne(vc => vc.RetailChannel)
            .WithMany(rc => rc.VoucherChannels)
            .HasForeignKey(vc => vc.ChannelId);
    }
}
