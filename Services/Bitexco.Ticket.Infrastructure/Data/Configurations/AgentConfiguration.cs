﻿using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations;
public class AgentConfiguration : IEntityTypeConfiguration<Agent>
{
    public void Configure(EntityTypeBuilder<Agent> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        // Property configurations (moved from attributes)
        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.ContactPerson)
            .HasMaxLength(100);

        builder.Property(c => c.TaxCode)
            .HasMaxLength(50);

        builder.Property(c => c.PhoneNumber)
            .HasMaxLength(20);

        builder.Property(c => c.Email)
            .HasMaxLength(100);

        builder.Property(c => c.Address)
            .HasMaxLength(255);

        builder.Property(c => c.SuspensionReason)
            .HasMaxLength(255);

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        builder.Property(c => c.ApprovalStatus)
            .HasDefaultValue(AgentApproveStatus.pending)
            .HasConversion(
                s => s.ToString(),
                dbStatus => (AgentApproveStatus)Enum.Parse(typeof(AgentApproveStatus), dbStatus));

        // Audit properties
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();

        // Relationships
        builder.HasOne(a => a.ParentAgent)
            .WithMany(a => a.ChildAgents)
            .HasForeignKey(a => a.ParentAgentId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure navigation properties for collections
        builder.HasMany(a => a.AgentDebts)
            .WithOne(ad => ad.Agent)
            .HasForeignKey(ad => ad.AgentId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
