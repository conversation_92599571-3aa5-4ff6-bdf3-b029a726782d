using Bitexco.Ticket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bitexco.Ticket.Infrastructure.Data.Configurations
{
    public class CommissionOrderHistoryConfiguration : IEntityTypeConfiguration<CommissionOrderHistory> 
    {
        public void Configure(EntityTypeBuilder<CommissionOrderHistory> builder)
        {
            // Primary key configuration
            builder.HasKey(c => c.Id);
            builder.HasIndex(c => c.Id)
                .IsUnique();

            builder.Property(c => c.Id)
                .ValueGeneratedOnAdd();

            // Soft delete filter
            builder.HasQueryFilter(c => !c.IsDeleted);

            // Property configurations
            builder.Property(c => c.AgentId)
                .IsRequired();

            builder.Property(c => c.OrderId)
                .IsRequired();

            builder.Property(c => c.CommissionRatePercentage)
                .IsRequired()
                .HasColumnType("decimal(19,4)")
                .HasDefaultValue(0);

            builder.Property(c => c.CommissionAmount)
                .IsRequired()
                .HasColumnType("decimal(19,4)")
                .HasDefaultValue(0);

            builder.Property(c => c.PaidAmount)
                .IsRequired()
                .HasColumnType("decimal(19,4)")
                .HasDefaultValue(0);

            builder.Property(c => c.PaymentMethod)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue(CommissionPaymentMethod.bank_transfer)
                .HasConversion(
                    s => s.ToString(),
                    dbValue => (CommissionPaymentMethod)Enum.Parse(typeof(CommissionPaymentMethod), dbValue));

            builder.Property(c => c.PaymentProofUrl)
                .HasMaxLength(500);

            builder.Property(c => c.Status)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue(CommissionPaymentStatus.pending)
                .HasConversion(
                    s => s.ToString(),
                    dbValue => (CommissionPaymentStatus)Enum.Parse(typeof(CommissionPaymentStatus), dbValue));

            // Audit properties
            builder.Property(c => c.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(c => c.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();

            // Relationships
            builder.HasOne(c => c.Agent)
                .WithMany()
                .HasForeignKey(c => c.AgentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(c => c.Order)
                .WithMany()
                .HasForeignKey(c => c.OrderId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}