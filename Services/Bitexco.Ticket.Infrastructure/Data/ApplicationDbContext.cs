﻿using Bitexco.Ticket.Application.Data;
using System.Reflection;

namespace Bitexco.Ticket.Infrastructure.Data;

public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    public ApplicationDbContext() { }

    public ApplicationDbContext(DbContextOptions options) : base(options)
    { }
    
    public DbSet<Agent> Agents => Set<Agent>();
    public DbSet<AgentApiKey> AgentApiKeys => Set<AgentApiKey>();
    public DbSet<AgentDebt> AgentDebts => Set<AgentDebt>();
    public DbSet<AuditLog> AuditLogs => Set<AuditLog>();
    public DbSet<CommissionOrderHistory> CommissionOrderHistories => Set<CommissionOrderHistory>();
    public DbSet<CommissionPayment> CommissionPayments => Set<CommissionPayment>();
    public DbSet<CommissionPolicy> CommissionPolicies => Set<CommissionPolicy>();
    public DbSet<CommissionReport> CommissionReports => Set<CommissionReport>();
    public DbSet<ContentPage> ContentPages => Set<ContentPage>();
    public DbSet<Customer> Customers => Set<Customer>();
    public DbSet<Faq> Faqs => Set<Faq>();
    public DbSet<Invoice> Invoices => Set<Invoice>();
    public DbSet<NotificationTemplate> NotificationTemplates => Set<NotificationTemplate>();
    public DbSet<OperatingHour> OperatingHours => Set<OperatingHour>();
    public DbSet<Order> Orders => Set<Order>();
    public DbSet<OrderItem> OrderItems => Set<OrderItem>();
    public DbSet<PaymentTransaction> PaymentTransactions => Set<PaymentTransaction>();
    public DbSet<Permission> Permissions => Set<Permission>();
    public DbSet<PosDevice> PosDevices => Set<PosDevice>();
    public DbSet<PricingRule> PricingRules => Set<PricingRule>();
    public DbSet<RetailChannel> RetailChannels => Set<RetailChannel>();
    public DbSet<Role> Roles => Set<Role>();
    public DbSet<RolePermission> RolePermissions => Set<RolePermission>();
    public DbSet<Shift> Shifts => Set<Shift>();
    public DbSet<SupportTicket> SupportTickets => Set<SupportTicket>();
    public DbSet<SupportTicketMessage> SupportTicketMessages => Set<SupportTicketMessage>();
    public DbSet<SystemConfig> SystemConfigs => Set<SystemConfig>();
    public DbSet<Domain.Models.Ticket> Tickets => Set<Domain.Models.Ticket>();
    public DbSet<TicketHistory> TicketHistories => Set<TicketHistory>();
    public DbSet<TicketPrice> TicketPrices => Set<TicketPrice>();
    public DbSet<TicketType> TicketTypes => Set<TicketType>();
    public DbSet<TicketTypeCondition> TicketTypeConditions => Set<TicketTypeCondition>();
    public DbSet<Function> Functions => Set<Function>();
    public DbSet<RoleFunction> RoleFunctions => Set<RoleFunction>();
    public DbSet<VisitTimeRule> VisitTimeRules => Set<VisitTimeRule>();
    public DbSet<PriceApproval> PriceApprovals => Set<PriceApproval>();
    public DbSet<KioskSyncStatus> KioskSyncStatuses => Set<KioskSyncStatus>();
    public DbSet<User> Users => Set<User>();
    public DbSet<Voucher> Vouchers => Set<Voucher>();
    public DbSet<VoucherCampaign> VoucherCampaigns => Set<VoucherCampaign>();
    public DbSet<VoucherChannel> VoucherChannels => Set<VoucherChannel>();
    public DbSet<BusinessHour> BusinessHours => Set<BusinessHour>();
    public DbSet<Notification> Notifications => Set<Notification>();
    public DbSet<OtpLog> OtpLogs => Set<OtpLog>();
    public DbSet<ScheduledTask> ScheduledTasks => Set<ScheduledTask>();
    public DbSet<VisitorFlow> VisitorFlows => Set<VisitorFlow>();
    public DbSet<VisitorStat> VisitorStats => Set<VisitorStat>();
    public DbSet<LanguageMessage> LanguageMessages => Set<LanguageMessage>();
    public DbSet<ForgotPasswordSession> ForgotPasswordSessions => Set<ForgotPasswordSession>();
    public DbSet<GroupBooking> GroupBookings => Set<GroupBooking>();
    public DbSet<GroupBookingBus> GroupBookingBuses => Set<GroupBookingBus>();

    public DbSet<PromotionCampaign> PromotionCampaigns => Set<PromotionCampaign>();
    public DbSet<Contract> Contracts => Set<Contract>();

    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(builder);
    }
}
