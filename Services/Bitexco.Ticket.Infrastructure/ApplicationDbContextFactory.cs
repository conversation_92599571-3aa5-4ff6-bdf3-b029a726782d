using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace Bitexco.Ticket.Infrastructure;

public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
{
    public ApplicationDbContext CreateDbContext(string[] args)
    {
        // Get the directory of the infrastructure project
        var baseDirectory = Directory.GetCurrentDirectory();
        
        // Navigate to API project directory - adjust the path as needed
        var apiDirectory = Path.GetFullPath(Path.Combine(baseDirectory, "..", "Bitexco.Ticket.API"));
        
        var configuration = new ConfigurationBuilder()
            .SetBasePath(apiDirectory)  // Use the API project's directory
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile("appsettings.Development.json", optional: true)
            .Build();
        
        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
        var connectionString = configuration.GetConnectionString("Database");
        
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("Connection string 'Database' not found in appsettings.json");
        }
        
        optionsBuilder.UseNpgsql(connectionString);
        return new ApplicationDbContext(optionsBuilder.Options);
    }
}