namespace Bitexco.Ticket.Application.Data;

using Bitexco.Ticket.Domain.Models;

public interface IUserRepository
{
    Task<bool> CreateAsync(User user, CancellationToken cancellationToken);
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken);
    Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken);
    Task<bool> UpdateRefreshTokenAsync(Guid userId, string refreshToken, DateTime refreshTokenExpiryTime, CancellationToken cancellationToken);
    Task UpdateLastLoginAsync(Guid userId, CancellationToken cancellationToken);
    Task<User?> GetByRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken);
    Task<bool> RemoveRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken);
    Task<bool> UpdatePasswordAsync(Guid userId, string newPasswordHash, CancellationToken cancellationToken);
}
