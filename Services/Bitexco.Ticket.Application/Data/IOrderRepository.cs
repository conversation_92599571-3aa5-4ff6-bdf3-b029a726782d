namespace Bitexco.Ticket.Application.Data;

using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// Repository interface for Order-related operations
/// </summary>
public interface IOrderRepository
{
    /// <summary>
    /// Gets an order by its ID with all related items and details
    /// </summary>
    /// <param name="id">Order ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Order if found, null otherwise</returns>
    Task<Order?> GetOrderByIdAsync(Guid id, CancellationToken cancellationToken);

    /// <summary>
    /// Gets an order detail by its ID with all related items and details
    /// </summary>
    /// <param name="id">Order ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Order if found, null otherwise</returns>
    Task<Order?> GetOrderDetailByIdAsync(Guid id, CancellationToken cancellationToken);

    /// <summary>
    /// Gets paginated list of orders with optional filtering
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <param name="startDate">Optional start date filter</param>
    /// <param name="endDate">Optional end date filter</param>
    /// <param name="customerId">Optional customer ID filter</param>
    /// <param name="agentId">Optional agent ID filter</param>
    /// <param name="status">Optional order status filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of orders with total count</returns>
    Task<(IEnumerable<Order> Orders, int TotalCount)> GetOrdersAsync(
        int pageNumber, 
        int pageSize, 
        DateOnly? startDate = null, 
        DateOnly? endDate = null, 
        Guid? customerId = null, 
        Guid? agentId = null, 
        OrderStatus? status = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an order's status
    /// </summary>
    /// <param name="id">Order ID</param>
    /// <param name="status">New status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated order or null if not found</returns>
    Task<Order?> UpdateOrderStatusAsync(Guid id, OrderStatus status, CancellationToken cancellationToken);
    
    /// <summary>
    /// Processes a refund for an order
    /// </summary>
    /// <param name="id">Order ID</param>
    /// <param name="reason">Refund reason</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated order or null if not found</returns>
    Task<Order?> ProcessRefundAsync(Guid id, string reason, CancellationToken cancellationToken);
    
    /// <summary>
    /// Checks if the specified user has approval permission for the order
    /// </summary>
    /// <param name="orderId">Order ID</param>
    /// <param name="userId">User ID checking for approval rights</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user can approve actions on this order</returns>
    Task<bool> UserCanApproveOrderActionAsync(Guid orderId, Guid userId, CancellationToken cancellationToken);
    
    /// <summary>
    /// Soft-deletes/cancels the specified order
    /// </summary>
    /// <param name="id">Order ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful, false otherwise</returns>
    Task<bool> CancelOrderAsync(Guid id, CancellationToken cancellationToken);
}
