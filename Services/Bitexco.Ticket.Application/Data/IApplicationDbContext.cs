﻿namespace Bitexco.Ticket.Application.Data;

public interface IApplicationDbContext
{
    DbSet<Agent> Agents { get; }
    DbSet<AgentApiKey> AgentApiKeys { get; }
    public DbSet<AgentDebt> AgentDebts { get; }
    DbSet<AuditLog> AuditLogs { get; }
    DbSet<CommissionOrderHistory> CommissionOrderHistories { get; }
    DbSet<CommissionPayment> CommissionPayments { get; }
    DbSet<CommissionPolicy> CommissionPolicies { get; }
    DbSet<CommissionReport> CommissionReports { get; }
    DbSet<ContentPage> ContentPages { get; }
    DbSet<Customer> Customers { get; }
    DbSet<Faq> Faqs { get; }
    DbSet<Invoice> Invoices { get; }
    DbSet<NotificationTemplate> NotificationTemplates { get; }
    DbSet<OperatingHour> OperatingHours { get; }
    DbSet<Order> Orders { get; }
    DbSet<OrderItem> OrderItems { get; }
    DbSet<PaymentTransaction> PaymentTransactions { get; }
    DbSet<Permission> Permissions { get; }
    DbSet<PosDevice> PosDevices { get; }
    DbSet<PricingRule> PricingRules { get; }
    DbSet<RetailChannel> RetailChannels { get; }
    DbSet<Role> Roles { get; }
    DbSet<RolePermission> RolePermissions { get; }
    DbSet<Shift> Shifts { get; }
    DbSet<SupportTicket> SupportTickets { get; }
    DbSet<SupportTicketMessage> SupportTicketMessages { get; }
    DbSet<SystemConfig> SystemConfigs { get; }
    DbSet<Domain.Models.Ticket> Tickets { get; }
    DbSet<TicketHistory> TicketHistories { get; }
    DbSet<TicketPrice> TicketPrices { get; }
    DbSet<TicketType> TicketTypes { get; }
    DbSet<TicketTypeCondition> TicketTypeConditions { get; }
    DbSet<Function> Functions { get; }
    DbSet<RoleFunction> RoleFunctions { get; }
    DbSet<VisitTimeRule> VisitTimeRules { get; }
    DbSet<PriceApproval> PriceApprovals { get; }
    DbSet<KioskSyncStatus> KioskSyncStatuses { get; }
    DbSet<User> Users { get; }
    DbSet<Voucher> Vouchers { get; }
    DbSet<VoucherCampaign> VoucherCampaigns { get; }
    DbSet<VoucherChannel> VoucherChannels { get; }
    DbSet<BusinessHour> BusinessHours { get; }
    DbSet<Notification> Notifications { get; }
    DbSet<OtpLog> OtpLogs { get; }
    DbSet<ScheduledTask> ScheduledTasks { get; }
    DbSet<VisitorFlow> VisitorFlows { get; }
    DbSet<VisitorStat> VisitorStats { get; }
    DbSet<LanguageMessage> LanguageMessages { get; }
    DbSet<ForgotPasswordSession> ForgotPasswordSessions { get; }
    DbSet<GroupBooking> GroupBookings { get; }
    DbSet<GroupBookingBus> GroupBookingBuses { get; }
    DbSet<PromotionCampaign>  PromotionCampaigns { get; }
    DbSet<Contract> Contracts { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken);
}
