﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Features\Authorizes\**" />
    <Compile Remove="Features\Orders\EventHandlers\**" />
    <EmbeddedResource Remove="Features\Authorizes\**" />
    <EmbeddedResource Remove="Features\Orders\EventHandlers\**" />
    <None Remove="Features\Authorizes\**" />
    <None Remove="Features\Orders\EventHandlers\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\BuildingBlocks\BuildingBlocks.Messaging\BuildingBlocks.Messaging.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\BuildingBlocks\BuildingBlocks.csproj" />
    <ProjectReference Include="..\Bitexco.Ticket.Domain\Bitexco.Ticket.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Features\Agents\Commands\ImportAgent\" />
    <Folder Include="Features\Customers\Commands\ExportCustomer\" />
    <Folder Include="Features\DateManagement\" />
    <Folder Include="Features\Reports\" />
    <Folder Include="Intergrateds\" />
  </ItemGroup>

</Project>
