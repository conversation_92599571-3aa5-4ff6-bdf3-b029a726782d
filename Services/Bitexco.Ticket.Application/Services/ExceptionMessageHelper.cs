using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Bitexco.Ticket.Application.Services;

/// <summary>
/// Static helper for getting localized exception messages
/// Used by exceptions that cannot inject services
/// </summary>
public static class ExceptionMessageHelper
{
    private static IServiceProvider? _serviceProvider;
    private static IHttpContextAccessor? _httpContextAccessor;

    /// <summary>
    /// Initialize with service provider (called during startup)
    /// </summary>
    /// <param name="serviceProvider">Service provider</param>
    public static void Initialize(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _httpContextAccessor = serviceProvider.GetService<IHttpContextAccessor>();
    }

    /// <summary>
    /// Get localized message for exceptions
    /// </summary>
    /// <param name="key">Message key (typically exception class name)</param>
    /// <param name="parameters">Parameters to format into message</param>
    /// <returns>Localized formatted message</returns>
    public static string GetMessage(string key, params object[] parameters)
    {
        try
        {
            if (_serviceProvider == null)
            {
                // Fallback to key if not initialized
                return FormatFallbackMessage(key, parameters);
            }

            // Get current language from HTTP context or default to English
            var currentLanguage = GetCurrentLanguageFromContext() ?? "en";

            // Get cached message from LanguageDataStorage
            var message = Features.LanguageLogic.LanguageDataStorage.GetMessage(key, currentLanguage);

            // If message not found, use fallback
            if (message == key)
            {
                return FormatFallbackMessage(key, parameters);
            }

            // Format message with parameters if provided
            if (parameters.Length <= 0)
                return message;
            try
            {
                return string.Format(message, parameters);
            }
            catch (FormatException)
            {
                // If formatting fails, return original message
                return message;
            }
        }
        catch
        {
            // Fallback in case of any error
            return FormatFallbackMessage(key, parameters);
        }
    }

    /// <summary>
    /// Get current language from HTTP context
    /// </summary>
    /// <returns>Current language code or null</returns>
    private static string? GetCurrentLanguageFromContext()
    {
        try
        {
            var httpContext = _httpContextAccessor?.HttpContext;
            if (httpContext?.Request?.Headers != null &&
                httpContext.Request.Headers.TryGetValue("lang", out var headerValue))
            {
                var language = headerValue.ToString();
                if (!string.IsNullOrWhiteSpace(language))
                {
                    return language.ToLowerInvariant();
                }
            }
        }
        catch
        {
            // Ignore any errors while getting language from context
            // This allows fallback to default language
        }

        return null;
    }

    /// <summary>
    /// Create fallback message when localized message is not available
    /// </summary>
    /// <param name="key">Message key</param>
    /// <param name="parameters">Parameters</param>
    /// <returns>Fallback message</returns>
    private static string FormatFallbackMessage(string key, params object[] parameters)
    {
        if (parameters == null || parameters.Length == 0)
        {
            return key;
        }

        // Create simple fallback format
        var paramString = string.Join(", ", parameters);
        return $"{key}: {paramString}";
    }
}
