using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Features.LanguageLogic;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Bitexco.Ticket.Application.Services;

/// <summary>
/// Language service helper implementation, following MediTrack pattern but with startup initialization
/// Singleton service that loads data during app startup (per boss requirement)
/// </summary>
public class LanguageServiceHelper(ILogger<LanguageServiceHelper> logger) : ILanguageServiceHelper
{
    private readonly ILogger<LanguageServiceHelper> _logger = logger;

    /// <summary>
    /// Get language message (uses cached data loaded at startup)
    /// </summary>
    public string GetMessageAsync(string key, string languageCode, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(key))
            return key;

        // Check cache (should be loaded during startup)
        var cachedMessage = LanguageDataStorage.GetMessage(key, languageCode);
        
        // If found in cache and it's not the key itself, return it
        if (cachedMessage != key)
        {
            return cachedMessage;
        }

        // If cache is empty, log warning (should not happen if startup init worked)
        if (!LanguageDataStorage.IsInitialized || LanguageDataStorage.Count == 0)
        {
            _logger.LogWarning("Language cache not initialized. This should not happen during normal operation.");
        }

        // Return the key as fallback
        return key;
    }

    /// <summary>
    /// Initialize language cache from database (called once during startup)
    /// Uses service provider to get scoped DbContext safely
    /// </summary>
    public async Task InitializeAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken = default)
    {
        if (LanguageDataStorage.IsInitialized)
        {
            _logger.LogInformation("Language cache already initialized. Skipping...");
            return;
        }

        try
        {
            _logger.LogInformation("Initializing language cache from database...");

            // Create scope to get DbContext safely
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<IApplicationDbContext>();

            var languageMessages = await dbContext.LanguageMessages
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            // Clear cache and add all messages
            LanguageDataStorage.Clear();
            var messageDictionary = languageMessages.ToDictionary(lm => lm.Key, lm => lm.Message);
            LanguageDataStorage.AddRangeMessages(messageDictionary);

            LanguageDataStorage.MarkAsInitialized();

            _logger.LogInformation("Language cache initialized successfully. Loaded {Count} messages.", 
                LanguageDataStorage.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize language cache during startup");
            // Don't rethrow - allow app to continue with empty cache
        }
    }
}
