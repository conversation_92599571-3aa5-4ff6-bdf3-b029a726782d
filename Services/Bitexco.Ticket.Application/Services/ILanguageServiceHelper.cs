namespace Bitexco.Ticket.Application.Services;

/// <summary>
/// Language service helper interface, following MediTrack pattern with startup initialization
/// </summary>
public interface ILanguageServiceHelper
{
    /// <summary>
    /// Get language message (uses cached data loaded at startup)
    /// </summary>
    /// <param name="key">Message key</param>
    /// <param name="languageCode">Language code (default: "en")</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Localized message</returns>
    string GetMessageAsync(string key, string languageCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Initialize language cache from database (called once during startup)
    /// </summary>
    /// <param name="serviceProvider">Service provider to get DbContext</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task InitializeAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken = default);
}
