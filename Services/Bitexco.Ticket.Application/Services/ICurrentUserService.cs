namespace Bitexco.Ticket.Application.Services;

/// <summary>
/// Service to get information about the current user
/// </summary>
public interface ICurrentUserService
{
    /// <summary>
    /// Gets the ID of the current authenticated user
    /// </summary>
    /// <returns>The user ID or null if not authenticated</returns>
    string? GetUserId();

    /// <summary>
    /// Gets the username of the current authenticated user
    /// </summary>
    /// <returns>The username or null if not authenticated</returns>
    string? GetUserName();

    /// <summary>
    /// Checks if the current user has a specific role
    /// </summary>
    /// <param name="roleName">The role name to check</param>
    /// <returns>True if the user has the role, false otherwise</returns>
    bool IsInRole(string roleName);
}
