namespace Bitexco.Ticket.Application.Dtos;

public record ScheduledTaskDto
{
    public string TaskName { get; set; } = null!;
    public string TaskDescription { get; set; } = null!;
    public string TaskType { get; set; } = null!;
    public string Schedule { get; set; } = null!;
    public bool IsEnabled { get; set; } = true;
    public DateTime? LastRunTime { get; set; }
    public string? LastRunStatus { get; set; }
    public string? LastRunResult { get; set; }
    public DateTime? NextRunTime { get; set; }
    public string? Parameters { get; set; }
}

public record ScheduledTaskResponseDto : ScheduledTaskDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
