﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record ShiftDto
{
    public Guid UserId { get; set; }
    public Guid PosDeviceId { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public decimal StartCashBalance { get; set; } = 0;
    public decimal? EndCashBalance { get; set; }
    public decimal TotalCashSales { get; set; } = 0;
    public decimal TotalPosSales { get; set; } = 0;
    public decimal TotalTransferSales { get; set; } = 0;
    public ShiftStatus Status { get; set; }
    public string? AuditDetails { get; set; }
}

public record ShiftResponseDto : ShiftDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UserName { get; set; }
    public string? PosDeviceName { get; set; }
}
