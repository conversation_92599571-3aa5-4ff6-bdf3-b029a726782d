﻿namespace Bitexco.Ticket.Application.Dtos;

public record OrderItemDto
{
    public Guid OrderId { get; set; }
    public Guid TicketTypeId { get; set; }
    public string? TicketTypeName { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal SubTotal { get; set; }
    public DateOnly VisitDate { get; set; }
    public TimeOnly VisitTime { get; set; }
}

public record OrderItemResponseDto : OrderItemDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
