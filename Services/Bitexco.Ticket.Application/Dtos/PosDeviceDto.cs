using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record PosDeviceDto
{
    public string? Code { get; set; } = null!;
    public string? Name { get; set; } = null!;
    public string? Location { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string? Floor { get; set; }
    public bool IsConnected { get; set; }
    public PosDeviceType? Type { get; set; }
    public string? FirmwareVersion { get; set; }
    public string? SoftwareVersion { get; set; }
    public string? OperatingSystem { get; set; }
    public string? HardwareModel { get; set; }
    public string? SerialNumber { get; set; }
    public string? IpAddress { get; set; }
    public string? MacAddress { get; set; }
    public PosDeviceStatus? Status { get; set; }
    public string? ErrorDetails { get; set; }
    public DateTime? LastErrorAt { get; set; }
    public int OfflineCount { get; set; } = 0;
    public DateTime? LastMaintenanceAt { get; set; }
    public DateTime? LastHeartbeatAt { get; set; }
    public string? PrinterStatus { get; set; }
    public DateTime? LastUpdateAt { get; set; }
    public string? UpdateStatus { get; set; }
}

public record PosDeviceResponseDto : PosDeviceDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}