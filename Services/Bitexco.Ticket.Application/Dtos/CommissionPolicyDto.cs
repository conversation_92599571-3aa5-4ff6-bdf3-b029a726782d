﻿namespace Bitexco.Ticket.Application.Dtos;

public record CommissionPolicyDto
{
    public Guid AgentId { get; set; }
    public string PolicyName { get; set; } = null!;
    public decimal? CommissionRatePercentage { get; set; }
    public decimal? FixedCommissionAmount { get; set; }
    public int? TierLevel { get; set; }
    public DateOnly EffectiveFromDate { get; set; }
    public DateOnly? EffectiveToDate { get; set; }
    public bool IsActive { get; set; }
}

public record CommissionPolicyResponseDto : CommissionPolicyDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
