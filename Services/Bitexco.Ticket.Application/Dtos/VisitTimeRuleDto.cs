namespace Bitexco.Ticket.Application.Dtos;

public record VisitTimeRuleDto
{
    public Guid TicketTypeId { get; set; }
    public string Name { get; set; } = null!;
    public TimeOnly? EarliestEntryTime { get; set; }
    public TimeOnly? LatestEntryTime { get; set; }
    public int? MaxDurationMinutes { get; set; }
    public List<int> DaysOfWeek { get; set; } = [];
    public bool AppliesToHolidays { get; set; }
    public bool IsActive { get; set; }
}

public record VisitTimeRuleResponseDto : VisitTimeRuleDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}