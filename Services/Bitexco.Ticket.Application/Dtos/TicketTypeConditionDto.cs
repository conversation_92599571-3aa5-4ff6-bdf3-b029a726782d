using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record TicketTypeConditionDto
{
    public Guid? TicketTypeId { get; set; }
    public TicketTypeConditionType Type { get; set; }
    public string? Name { get; set; } = null!;
    public string? Description { get; set; } = null!;
    public string? FromValue { get; set; }
    public string? ToValue { get; set; }
    public int DisplayOrder { get; set; } = 0;
    public bool IsActive { get; set; }
}

public record TicketTypeConditionResponseDto : TicketTypeConditionDto
{
    public Guid Id { get; set; }
    public string TypeDisplay => Type.ToString();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
