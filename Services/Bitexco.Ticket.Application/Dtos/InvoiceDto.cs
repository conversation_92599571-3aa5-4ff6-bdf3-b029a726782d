﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record InvoiceDto
{
    public string? Code { get; set; }
    public Guid OrderId { get; set; }
    public Guid? CustomerId { get; set; }
    public DateOnly IssueDate { get; set; }
    public decimal TotalAmount { get; set; }
    public InvoiceStatus Status { get; set; }
    public SendMailStatus? SendMailStatus { get; set; }
    public DateTime? EmailSentAt { get; set; }
    public string? EmailAddress { get; set; }
    public string? PdfFilePath { get; set; }
    public Guid? IssuedByUserId { get; set; }
    public string? CancellationReason { get; set; }
    public string? RevisionDetails { get; set; }
}

public record InvoiceResponseDto : InvoiceDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
