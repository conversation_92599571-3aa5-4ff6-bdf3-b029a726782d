namespace Bitexco.Ticket.Application.Dtos;

public record BusinessHourDto
{
    public int DayOfWeek { get; set; }
    public bool IsOpen { get; set; } = true;
    public TimeOnly OpenTime { get; set; }
    public TimeOnly CloseTime { get; set; }
    public string? Notes { get; set; }
    public bool IsHoliday { get; set; } = false;
    public DateOnly? SpecificDate { get; set; }
}

public record BusinessHourResponseDto : BusinessHourDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
