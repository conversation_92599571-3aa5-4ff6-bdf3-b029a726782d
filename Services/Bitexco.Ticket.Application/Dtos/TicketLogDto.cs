using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record TicketLogDto
{
    public Guid UserId { get; set; }
    public Guid TicketId { get; set; }
    public string Action { get; set; } = null!;
    public TicketStatus? OldStatus { get; set; }
    public TicketStatus? NewStatus { get; set; }
}

public record TicketLogResponseDto : TicketLogDto
{
    public Guid Id { get; set; }
    public string? OldStatusDisplay => OldStatus?.ToString();
    public string? NewStatusDisplay => NewStatus?.ToString();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

