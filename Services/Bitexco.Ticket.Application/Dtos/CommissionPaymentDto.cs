﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record CommissionPaymentDto
{
    public Guid AgentId { get; set; }
    public Guid CommissionReportId { get; set; }
    public decimal Amount { get; set; }
    public DateTime PaymentDate { get; set; }
    public CommissionPaymentMethod PaymentMethod { get; set; }
    public CommissionPaymentStatus Status { get; set; }
    public Guid? ApprovedByUserId { get; set; }
}

public record CommissionPaymentResponseDto : CommissionPaymentDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
