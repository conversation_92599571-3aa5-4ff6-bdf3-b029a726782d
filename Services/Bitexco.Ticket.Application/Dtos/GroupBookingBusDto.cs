namespace Bitexco.Ticket.Application.Dtos;

public record GroupBookingBusDto
{
    public string Number { get; set; } = string.Empty;
    public List<Guid> AgentIds { get; set; } = [];
    public Guid GroupBookingId { get; set; }
}

public record GroupBookingBusResponseDto : GroupBookingBusDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}