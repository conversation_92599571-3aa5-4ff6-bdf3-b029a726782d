﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record AuditLogDto
{
    public Guid? UserId { get; set; }
    public AuditLogActionType ActionType { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; } // ID of the affected entity
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public string? IpAddress { get; set; }
    public DateTime Timestamp { get; set; }
}

public record AuditLogResponseDto : AuditLogDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
