﻿namespace Bitexco.Ticket.Application.Dtos;

public record TicketPriceDto
{
    public Guid TicketTypeId { get; set; }
    public Guid? PricingRuleId { get; set; }
    public decimal Price { get; set; }
    public DateOnly EffectiveDateFrom { get; set; }
    public DateOnly? EffectiveDateTo { get; set; }
}

public record TicketPriceResponseDto : TicketPriceDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
