namespace Bitexco.Ticket.Application.Dtos;

public record VisitorStatDto
{
    public DateOnly StatDate { get; set; } // Date of the statistics
    public TimeOnly? StatHour { get; set; } // Optional hour for hourly stats
    public string? TicketTypeName { get; set; } // Name of ticket type for filtering
    public int VisitorCount { get; set; }
    public int OnlineTicketCount { get; set; }
    public int OfflineTicketCount { get; set; }
    public int AgentTicketCount { get; set; }
    public decimal TotalRevenue { get; set; }
    public int AdultCount { get; set; }
    public int ChildCount { get; set; }
    public int SeniorCount { get; set; }
    public int CashPaymentCount { get; set; }
    public int PosPaymentCount { get; set; }
    public int TransferPaymentCount { get; set; }
    public int EWalletPaymentCount { get; set; }
    public TimeOnly? PeakHour { get; set; }
    public int PeakHourCount { get; set; }
}

public record VisitorStatResponseDto : VisitorStatDto
{
    public Guid Id { get; set; } // Unique identifier for the stat record
    public DateTime CreatedAt { get; set; } // Timestamp when the record was created
    public DateTime? UpdatedAt { get; set; } // Optional timestamp for when the record was last updated
}