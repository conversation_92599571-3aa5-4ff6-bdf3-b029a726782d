﻿namespace Bitexco.Ticket.Application.Dtos;

public class VoucherCampaignDto
{
    public string Name { get; set; } = null!;
    public VoucherType Type { get; set; } = VoucherType.percentage;
    public decimal DiscountValue { get; set; }
    public decimal? MaxDiscountAmount { get; set; }
    public int? UsageLimit { get; set; }
    public Guid? TicketTypeId { get; set; } // Optional: If the voucher is specific to a ticket type
    public string? TicketTypeName { get; set; } // Optional: Name of the ticket type if applicable
    public VoucherApplyType? VoucherApplyType { get; set; }
    public List<Guid>? AgentIds { get; set; }
    public int CurrentUsage { get; set; }
    public DateTime ValidFrom { get; set; }
    public DateTime ValidTo { get; set; }
    public bool IsActive { get; set; }
}

public class VoucherCampaignResponseDto : VoucherCampaignDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}