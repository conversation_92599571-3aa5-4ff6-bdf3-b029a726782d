using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record AgentDto
{
    public string Code { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string? ContactPerson { get; set; }
    public string? TaxCode { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? ContactPosition { get; set; }
    public bool IsDebtAllowed { get; set; }
    public bool IsDirectDiscount { get; set; } = false;
    public decimal? CommissionRatePercentage { get; set; }
    public decimal? FixedCommissionAmount { get; set; }
    public string? SapCode { get; set; }
    public string? Address { get; set; }
    public AgentType Type { get; set; }
    public int? TicketSaleLimit { get; set; }
    public int? MonthlyTicketLimit { get; set; }
    public int CurrentMonthTicketsSold { get; set; }
    public DateOnly? LastLimitResetDate { get; set; }
    public AgentApproveStatus ApprovalStatus { get; set; }
    public string? SuspensionReason { get; set; }
    public Guid? ParentAgentId { get; set; } // Supports multi-level agents
    public string? BankName { get; set; }
    public string? BankAccountName { get; set; }
    public string? BankAccountNumber { get; set; }
    public bool IsActive { get; set; } = true; // Default: true
}

public record AgentResponseDto : AgentDto
{
    public Guid Id { get; set; }
    public string ApprovalStatusDisplay => ApprovalStatus.ToString();
    public string AgentTypeDisplay => Type.ToString();
    public DateTime? CreatedAt { get; set; }
    public string? ParentAgentName { get; set; }
}
