﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record SupportTicketMessageDto
{
    public Guid SupportTicketId { get; set; }
    public SupportTicketMessageSenderType SenderType { get; set; }
    public Guid SenderId { get; set; }
    public string MessageContent { get; set; } = string.Empty;
    public DateTime SentAt { get; set; }
}

public record SupportTicketMessageResponseDto : SupportTicketMessageDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
