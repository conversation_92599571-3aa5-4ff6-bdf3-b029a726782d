﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record PaymentTransactionDto
{
    public string? Code { get; set; }
    public Guid? OrderId { get; set; }
    public PaymentTransactionMethod PaymentMethod { get; set; }
    public PaymentTransactionType? TransactionType { get; set; }
    public Guid? RetailChannelId { get; set; }
    public decimal Amount { get; set; }
    public decimal PaidAmount { get; set; }
    public string? TransactionCode { get; set; }
    public PaymentTransactionStatus? TransactionStatus { get; set; }
    public PaymentReconciliationStatus? ReconciliationStatus { get; set; }
    public DateTime? TransactionAt { get; set; }
    public DateTime? PaidAt { get; set; }
}

public record PaymentTransactionResponseDto : PaymentTransactionDto
{
    public Guid Id { get; set; }
    public string? StatusDisplay => TransactionStatus.ToString();
    public string? MethodDisplay => PaymentMethod.ToString();
    public string? ReconciliationStatusDisplay => ReconciliationStatus.ToString();
    public string? RetailChannelName { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
