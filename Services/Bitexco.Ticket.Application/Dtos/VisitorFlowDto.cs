namespace Bitexco.Ticket.Application.Dtos;

public record VisitorFlowDto
{
    public DateTime Timestamp { get; set; }
    public string EntryPoint { get; set; } = null!;
    public int EntryCount { get; set; }
    public int ExitCount { get; set; }
    public int CurrentOccupancy { get; set; }
}

public record VisitorFlowResponseDto : VisitorFlowDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
