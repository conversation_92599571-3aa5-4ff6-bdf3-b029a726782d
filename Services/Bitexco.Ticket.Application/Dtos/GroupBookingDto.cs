using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record GroupBookingDto
{
    public string Code { get; set; } = null!;
    public Guid AgentId { get; set; }
    public GroupBookingType Type { get; set; }
    public DateOnly VisitDate { get; set; }
    public TimeOnly VisitTime { get; set; }
    public PaymentTransactionMethod PaymentMethod { get; set; }
    public decimal DepositAmount { get; set; } = 0;
    public GroupBookingStatus Status { get; set; }
    public DateTime? CheckedInAt { get; set; }
}

public record GroupBookingResponseDto : GroupBookingDto
{
    public Guid Id { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

