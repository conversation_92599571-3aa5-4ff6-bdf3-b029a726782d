using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record PriceApprovalDto
{
    public Guid TicketTypeId { get; set; }
    public PriceApprovalStatus Status { get; set; }
    public string ProposedChanges { get; set; } = null!;
    public string? RequestComments { get; set; }
    public Guid RequestedByUserId { get; set; }
    public DateTime RequestedAt { get; set; }
    public Guid? ApprovedByUserId { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovalComments { get; set; }
    public DateOnly? ScheduledEffectiveDate { get; set; }
}

public record PriceApprovalResponseDto : PriceApprovalDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}