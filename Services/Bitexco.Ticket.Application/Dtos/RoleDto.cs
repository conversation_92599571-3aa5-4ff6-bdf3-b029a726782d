﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

/// <summary>
/// Base DTO for Role - used for create/update operations (no Id)
/// </summary>
public record RoleDto
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public RoleType Type { get; set; }
}

/// <summary>
/// Response DTO for Role - inherits base DTO and adds Id + audit fields
/// </summary>
public record RoleResponseDto : RoleDto
{
    public Guid Id { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Function DTO for display in UI - shared across features
/// </summary>
public record FunctionDto
{
    public string Name { get; set; } = null!;
    public string? Code { get; set; }
    public string? Description { get; set; }
    public string? Path { get; set; }
    public string? IconPath { get; set; }
}

/// <summary>
/// Function Response DTO with Id - shared across features
/// </summary>
public record FunctionResponseDto : FunctionDto
{
    public Guid Id { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Permission definition DTO for role management - shared across features
/// </summary>
public record PermissionLabelDto
{
    public string Id { get; set; } = null!;
    public string Name { get; set; } = null!;
}