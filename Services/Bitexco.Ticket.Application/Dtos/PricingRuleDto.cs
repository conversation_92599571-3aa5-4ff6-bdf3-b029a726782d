﻿namespace Bitexco.Ticket.Application.Dtos;

public record PricingRuleDto
{
    public string Name { get; set; } = null!;
    public TimeOnly? StartTime { get; set; }
    public TimeOnly? EndTime { get; set; }
    public List<int> DayOfWeek { get; set; } = [];
    public bool IsHoliday { get; set; } = false;
    public decimal? PriceMultiplier { get; set; }
    public decimal? FixedSurcharge { get; set; }
    public bool IsActive { get; set; }
}

public record PricingRuleResponseDto : PricingRuleDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
