﻿namespace Bitexco.Ticket.Application.Dtos;

public record OperatingHourDto
{
    public int DayOfWeek { get; set; }
    public TimeOnly OpenTime { get; set; }
    public TimeOnly CloseTime { get; set; }
    public bool IsSpecialDay { get; set; } = false;
    public DateOnly? SpecialDate { get; set; } 
    public string? Description { get; set; }
}

public record OperatingHourResponseDto : OperatingHourDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}