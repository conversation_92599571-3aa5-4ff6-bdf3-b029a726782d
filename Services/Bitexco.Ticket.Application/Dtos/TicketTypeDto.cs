﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record TicketTypeDto
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = null!;
    public string? Icon { get; set; }
    public string? Description { get; set; }
     public decimal DefaultPrice { get; set; } = 0;
    public string TicketCodePrefix { get; set; } = string.Empty;
    public int TicketCodeLengthAfterPrefix { get; set; }
    public TicketExpirationDurationUnit? TicketExpirationDurationUnit { get; set; }
    public int? TicketExpirationAfterDurations { get; set; }
    public DateOnly? TicketExpirationFromDate { get; set; }
    public DateOnly? TicketExpirationToDate { get; set; }
    public bool IsActive { get; set; }
}

public record TicketTypeResponseDto : TicketTypeDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
