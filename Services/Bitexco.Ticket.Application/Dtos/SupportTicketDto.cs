﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record SupportTicketDto
{
    public Guid? CustomerId { get; set; }
    public string Subject { get; set; } = null!;
    public string Description { get; set; } = null!;
    public SupportTicketStatus Status { get; set; }
    public SupportTicketPriority Priority { get; set; }
    public Guid? AssignedToUserId { get; set; }
    public DateTime? ResolvedAt { get; set; }
}

public record SupportTicketResponseDto : SupportTicketDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
