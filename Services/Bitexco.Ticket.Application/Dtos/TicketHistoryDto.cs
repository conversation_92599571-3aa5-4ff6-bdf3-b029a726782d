using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record TicketHistoryDto
{
    public TicketStatus FromStatus { get; set; }
    public TicketStatus ToStatus { get; set; }
    public DateTime ChangedAt { get; set;}
    public Guid? ChangedByUserId { get; set;}
    public Guid? ChangedAtDeviceId { get; set;}
    public string? Location { get; set;}
}

public record TicketHistoryResponseDto : TicketHistoryDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}