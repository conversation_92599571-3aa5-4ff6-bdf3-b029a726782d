﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record OrderDto
{
    public string? Code { get; set; }
    public Guid? CustomerId { get; set; }
    public Guid? AgentId { get; set; }
    public Guid? RetailChannelId { get; set; }
    public Guid? PosUserId { get; set; }
    public Guid? ShiftId { get; set; }
    public Guid? PosDeviceId { get; set; }
    public Guid? VoucherId { get; set; }
    public DateOnly? OrderDate { get; set; }
    public TimeOnly? OrderTime { get; set; }
    public bool IsDebtPayment { get; set; }
    public PaymentTransactionMethod? LastPaymentMethod { get; set; }
    public CustomerType? CustomerType { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal CouponAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal DepositAmount { get; set; }
    public decimal FinalAmount { get; set; }
    public OrderStatus Status { get; set; }
    public string? RefundCode { get; set; }
    public OrderRefundStatus? RefundStatus { get; set; }
    public string? RefundReason { get; set; }
    public RefundPaymentMethod? RefundPaymentMethod { get; set; }
    public string? RefundPaymentProofFilePath { get; set; }
    public string? RefundRejectionReason { get; set; }
    public DateTime? RefundPaidAt { get; set; }
    public DateTime? RefundedAt { get; set; }
}

public record OrderResponseDto : OrderDto
{
    public Guid Id { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
