﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public class PromotionCampaignDto
{
    public string? Name { get; set; }

    public PromotionType Type { get; set; } = PromotionType.percentage;

    public decimal DiscountValue { get; set; }

    public decimal? MaxDiscountAmount { get; set; }
    public Guid? TicketTypeId { get; set; } // Optional: If the promotion is specific to a ticket type
    public string? TicketTypeName { get; set; } // Optional: Name of the ticket type if applicable

    public PromotionApplyType? PromotionApplyType { get; set; }

    public List<Guid>? AgentIds { get; set; }

    public DateTime ValidFrom { get; set; }

    public DateTime ValidTo { get; set; }

    public bool IsActive { get; set; }
}

public class PromotionCampaignResponseDto : PromotionCampaignDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}