﻿namespace Bitexco.Ticket.Application.Dtos;

public record AgentApiKeyDto
{
    public Guid AgentId { get; set; }
    public string ApiKey { get; set; } = null!;
    public string ApiSecret { get; set; } = null!;
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; set; } = true;
}

public record AgentApiKeyResponseDto : AgentApiKeyDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
