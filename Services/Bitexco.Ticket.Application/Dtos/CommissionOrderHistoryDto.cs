using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

/// <summary>
/// Base DTO for CommissionOrderHistory - used for create/update operations (no Id)
/// </summary>
public record CommissionOrderHistoryDto
{
    public Guid AgentId { get; set; }
    public string? Code { get; set; }
    public Guid OrderId { get; set; }
    public decimal CommissionRatePercentage { get; set; } = 0;
    public decimal CommissionAmount { get; set; } = 0;
    public decimal PaidAmount { get; set; } = 0;
    public DateTime? PaymentDate { get; set; }
    public CommissionPaymentMethod PaymentMethod { get; set; } = CommissionPaymentMethod.bank_transfer;
    public string? PaymentProofUrl { get; set; }
    public CommissionPaymentStatus Status { get; set; } = CommissionPaymentStatus.pending;
}

/// <summary>
/// Response DTO for CommissionOrderHistory - extends base DTO with Id and audit fields
/// </summary>
public record CommissionOrderHistoryResponseDto : CommissionOrderHistoryDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Display fields for API responses
    public string StatusDisplay => Status.ToString();
    public string PaymentMethodDisplay => PaymentMethod.ToString();
}
