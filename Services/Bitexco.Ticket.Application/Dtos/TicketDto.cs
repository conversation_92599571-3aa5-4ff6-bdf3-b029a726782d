using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record TicketDto
{
    public string Code { get; set; } = null!;
    public Guid? OrderItemId { get; set; }
    public Guid TicketTypeId { get; set; }
    public Guid? CustomerId { get; set; }
    public Guid? PosDeviceId { get; set; }
    public TicketStatus Status { get; set; } = TicketStatus.created;
    public DateTime? SoldAt { get; set; }
    public DateTime? UsedAt { get; set; }
    public DateTime? CancelledAt { get; set; }
    public DateTime? RefundedAt { get; set; }
    public DateTime? ExpiredAt { get; set; }
    public Guid? AgentId { get; set; }
    public DateOnly? PlannedVisitDate { get; set; }
    public TimeOnly? PlannedVisitTime { get; set; }
    public Guid? ValidatedByUserId { get; set; }
    public Guid? ValidatedAtDeviceId { get; set; }
    public Guid? RetailChannelId { get; set; }
    public int ReprintCount { get; set; } = 0;
    public DateTime? LastReprintedAt { get; set; }
    public string? AccessLocation { get; set; }
    public string? AuditInfo { get; set; }
}

public record TicketResponseDto : TicketDto
{
    public Guid Id { get; set; }
    public string? TicketName { get; set; } // Assuming this is the name of the ticket type
    public string QrCode => Code; // Assuming Code is used for QR code generation
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
