namespace Bitexco.Ticket.Application.Dtos;

public record OtpLogDto
{
    public Guid UserId { get; set; }
    public string OtpCode { get; set; } = null!;
    public DateTime ExpiresAt { get; set; }
    public string Method { get; set; } = null!;
    public string SentTo { get; set; } = null!;
    public string Status { get; set; } = null!;
    public DateTime? VerifiedAt { get; set; }
    public int FailedAttempts { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? ValidationHash { get; set; }
}

public record OtpLogResponseDto : OtpLogDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}