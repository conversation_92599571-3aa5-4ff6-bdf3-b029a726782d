﻿namespace Bitexco.Ticket.Application.Dtos;

public record FaqDto
{
    public string Question { get; set; } = null!;
    public string Answer { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int? OrderDisplay { get; set; }
    public bool IsActive { get; set; }
}

public record FaqResponseDto : FaqDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}