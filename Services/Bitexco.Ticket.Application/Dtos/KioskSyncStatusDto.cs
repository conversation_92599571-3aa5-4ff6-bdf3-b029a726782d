namespace Bitexco.Ticket.Application.Dtos;

public record KioskSyncStatusDto
{
    public Guid PosDeviceId { get; set; }
    public string Status { get; set; } = null!;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string? SyncDetails { get; set; }
}

public record KioskSyncStatusResponseDto : KioskSyncStatusDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
