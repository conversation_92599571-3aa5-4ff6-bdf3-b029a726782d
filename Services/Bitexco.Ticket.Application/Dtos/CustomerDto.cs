using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

/// <summary>
/// DTO for Customer without Id - used for Create/Update operations
/// </summary>
public record CustomerDto
{
    public string? Code { get; set; }
    public string FullName { get; set; } = null!;
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public CustomerType Type { get; set; } = CustomerType.individual;
    public string? CompanyName { get; set; }
    public string? Country { get; set; }
    public Guid? RetailChannelId { get; set; }
    public string? TaxCode { get; set; }
    public string? Address { get; set; }
    public bool? IsActive { get; set; } = true;
}

/// <summary>
/// DTO for Customer with Id - used for Response operations, inherits from CustomerDto
/// </summary>
public record CustomerResponseDto : CustomerDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? RetailChannelName { get; set; } // Tên kênh từ Order liên quan
}
