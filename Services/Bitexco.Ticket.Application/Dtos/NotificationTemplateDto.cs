﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Dtos;

public record NotificationTemplateDto
{
    public string Name { get; set; } = null!;
    public NotificationTemplateType Type { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = null!;
    public string? Description { get; set; }
    public string? AvailableVariables { get; set; }
    public bool IsActive { get; set; }
    public string? EventType { get; set; }
}

public record NotificationTemplateResponseDto : NotificationTemplateDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
