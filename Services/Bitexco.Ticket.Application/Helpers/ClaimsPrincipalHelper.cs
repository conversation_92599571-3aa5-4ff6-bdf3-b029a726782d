using System.Security.Claims;

namespace Bitexco.Ticket.Application.Helpers
{
    public static class ClaimsPrincipalHelper
    {
        public static Guid RetrieveUserIdFromPrincipal(this ClaimsPrincipal user)
        {
            var userId = user?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Guid.Empty;
            }

            return Guid.Parse(userId);
        }
    }
}