﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Helpers;

/// <summary>
/// Helper class for role permission management
/// Define available permissions and admin full access logic
/// </summary>
public static class RolePermissionHelper
{
    /// <summary>
    /// Get all available permissions in the system
    /// </summary>
    /// <returns>List of available permissions</returns>
    public static List<PermissionLabelDto> GetPermissions()
    {
        return
        [
            new PermissionLabelDto { Id = "R", Name = "Đọc" },
            new PermissionLabelDto { Id = "C", Name = "Thêm" },
            new PermissionLabelDto { Id = "U", Name = "Sửa" },
            new PermissionLabelDto { Id = "N", Name = "Hủy" },
            new PermissionLabelDto { Id = "D", Name = "Xóa" },
            new PermissionLabelDto { Id = "E", Name = "Xuất" },
            new PermissionLabelDto { Id = "I", Name = "Nhập" },
            new PermissionLabelDto { Id = "A", Name = "<PERSON><PERSON><PERSON><PERSON>" },
        ];
    }

    /// <summary>
    /// Check if user has admin role (type = admin) for full access
    /// </summary>
    /// <param name="userRoleType">User's role type</param>
    /// <returns>True if user is admin</returns>
    public static bool IsAdminRole(RoleType userRoleType)
    {
        return userRoleType == RoleType.admin;
    }
}
