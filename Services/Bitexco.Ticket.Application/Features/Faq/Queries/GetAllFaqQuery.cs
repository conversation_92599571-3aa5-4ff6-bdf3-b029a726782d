﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Faq.Dtos;

namespace Bitexco.Ticket.Application.Features.Faq.Queries
{
    public record GetAllFaqQuery() : IRequest<Response<List<FaqResponseDto>>>;

    public class GetAllFaqQueryHandler(IApplicationDbContext applicationDbContext) : IRequestHandler<GetAllFaqQuery, Response<List<FaqResponseDto>>>
    {
        private readonly IApplicationDbContext _dbContext = applicationDbContext;

        public async Task<Response<List<FaqResponseDto>>> Handle(GetAllFaqQuery request, CancellationToken cancellationToken)
        {
            // Retrieve all FAQs from the database
            var faqs = await _dbContext.Faqs
                .Select(f => new FaqResponseDto
                {
                    Answer = f.Answer,
                    Category = f.Category,
                    Id = f.Id,
                    IsActive = f.IsActive,
                    OrderDisplay = f.OrderDisplay,
                    Question = f.Question
                })
                .ToListAsync(cancellationToken);

            return new Response<List<FaqResponseDto>>(faqs);
        }
    }
}