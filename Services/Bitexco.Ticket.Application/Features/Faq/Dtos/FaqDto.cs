﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bitexco.Ticket.Application.Features.Faq.Dtos
{
    public class FaqDto
    {
        public Guid Id { get; set; }
        /// <summary>
        /// The question text
        /// </summary>
        public string Question { get; set; } = null!;

        /// <summary>
        /// The answer text to the question
        /// May contain HTML formatting for rich text display
        /// </summary>
        public string Answer { get; set; } = string.Empty;

        /// <summary>d
        /// Category grouping for the FAQ
        /// Used to organize FAQs by topic
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Display order within the category
        /// Lower numbers appear first
        /// </summary>
        public int? OrderDisplay { get; set; }

        /// <summary>
        /// Indicates whether this FAQ is currently visible to customers
        /// Default is true
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

   
}