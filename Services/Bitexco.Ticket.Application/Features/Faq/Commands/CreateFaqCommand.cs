﻿namespace Bitexco.Ticket.Application.Features.Faq.Commands
{
    public record CreateFaqCommand(CreateFaqDto Faq) : IRequest<Response<CreateFaqResult>>
    {
    }

    public record CreateFaqDto
    {
        /// <summary>
        /// The question text
        /// </summary>
        public string Question { get; set; } = null!;

        /// <summary>
        /// The answer text to the question
        /// May contain HTML formatting for rich text display
        /// </summary>
        public string Answer { get; set; } = string.Empty;

        /// <summary>d
        /// Category grouping for the FAQ
        /// Used to organize FAQs by topic
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Display order within the category
        /// Lower numbers appear first
        /// </summary>
        public int? OrderDisplay { get; set; }

        /// <summary>
        /// Indicates whether this FAQ is currently visible to customers
        /// Default is true
        /// </summary>
        public bool IsActive { get; set; } = true;


    }
    public record CreateFaqResult
    {
        /// <summary>
        /// ID of the created FAQ
        /// </summary>
        public Guid Id { get; init; }
    }

    public class CreateFaqCommandHandler(IApplicationDbContext _dbContext) : IRequestHandler<CreateFaqCommand, Response<CreateFaqResult>>
    {
        private readonly IApplicationDbContext dbContext = _dbContext;

        // Implementation of the command handler to create a new FAQ
        public async Task<Response<CreateFaqResult>> Handle(CreateFaqCommand request, CancellationToken cancellationToken)
        {
            var faq = new Domain.Models.Faq
            {
                Question = request.Faq.Question,
                Answer = request.Faq.Answer,
                Category = request.Faq.Category,
                OrderDisplay = request.Faq.OrderDisplay,
                IsActive = true, // Default to active
            };
            dbContext.Faqs.Add(faq);
            await dbContext.SaveChangesAsync(cancellationToken);
            var result = new CreateFaqResult
            {
                Id = faq.Id
            };
            return new Response<CreateFaqResult>(result);
        }
    }
}