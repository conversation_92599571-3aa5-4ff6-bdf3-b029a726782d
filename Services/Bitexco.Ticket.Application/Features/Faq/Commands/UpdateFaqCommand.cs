﻿namespace Bitexco.Ticket.Application.Features.Faq.Commands
{
    public record UpdateFaqCommand(UpdateFaqDto Faq) : IRequest<Response<UpdateFaqResult>>; // Command to update an existing FAQ

    public record UpdateFaqDto
    {
        /// <summary>
        /// Unique identifier for the FAQ to update
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// The question text
        /// </summary>
        public string Question { get; set; } = null!;
        /// <summary>
        /// The answer text to the question
        /// May contain HTML formatting for rich text display
        /// </summary>
        public string Answer { get; set; } = string.Empty;
        /// <summary>
        /// Category grouping for the FAQ
        /// Used to organize FAQs by topic
        /// </summary>
        public string Category { get; set; } = string.Empty;
        /// <summary>
        /// Display order within the category
        /// Lower numbers appear first
        /// </summary>
        public int? OrderDisplay { get; set; }
        /// <summary>
        /// Indicates whether this FAQ is currently visible to customers
        /// Default is true
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
    public record UpdateFaqResult(Guid Id);

    public class UpdateFaqCommandHandler(IApplicationDbContext _dbContext) : IRequestHandler<UpdateFaqCommand, Response<UpdateFaqResult>>
    {
        private readonly IApplicationDbContext dbContext = _dbContext;

        // Implementation of the command handler to update an existing FAQ
        public async Task<Response<UpdateFaqResult>> Handle(UpdateFaqCommand request, CancellationToken cancellationToken)
        {
            var faq = await dbContext.Faqs
                .FirstOrDefaultAsync(f => f.Id == request.Faq.Id, cancellationToken)
                ?? throw new FaqNotFoundException(request.Faq.Id);

            // Update the FAQ properties
            faq.Question = request.Faq.Question;
            faq.Answer = request.Faq.Answer;
            faq.Category = request.Faq.Category;
            faq.OrderDisplay = request.Faq.OrderDisplay;
            faq.IsActive = request.Faq.IsActive;
            dbContext.Faqs.Update(faq);
            var result = await dbContext.SaveChangesAsync(cancellationToken);

            if (result <= 0)
            {
                return new Response<UpdateFaqResult>(null!, "Failed to update FAQ");
            }

            return new Response<UpdateFaqResult>(new UpdateFaqResult(request.Faq.Id));
        }
    }
}