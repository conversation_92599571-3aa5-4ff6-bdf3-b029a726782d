namespace Bitexco.Ticket.Application.Features.DateManagement.GetDateRangeForPeriod;

/// <summary>
/// Response DTO containing calculated start and end dates
/// </summary>
public record DateRangeResponseDto
{
    /// <summary>
    /// Start date of the period
    /// </summary>
    public DateTime StartDate { get; set; }
    
    /// <summary>
    /// End date of the period
    /// </summary>
    public DateTime EndDate { get; set; }
    
    /// <summary>
    /// Period description for reference
    /// </summary>
    public string PeriodDescription { get; set; } = null!;
    
    /// <summary>
    /// Year used in calculation
    /// </summary>
    public int Year { get; set; }
}
