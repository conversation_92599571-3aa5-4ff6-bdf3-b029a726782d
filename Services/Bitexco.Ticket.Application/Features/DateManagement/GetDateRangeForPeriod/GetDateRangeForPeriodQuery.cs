namespace Bitexco.Ticket.Application.Features.DateManagement.GetDateRangeForPeriod;

/// <summary>
/// Query to get date range based on period type and year
/// </summary>
public class GetDateRangeForPeriodQuery : IRequest<Response<DateRangeResponseDto>>
{
    /// <summary>
    /// Period type for date range calculation
    /// </summary>
    public DateRangePeriod Period { get; set; }
    
    /// <summary>
    /// Year for calculation (optional, defaults to current year)
    /// </summary>
    public int? Year { get; set; }
}