using System.Globalization;

namespace Bitexco.Ticket.Application.Features.DateManagement.GetDateRangeForPeriod;

/// <summary>
/// Handler for GetDateRangeForPeriodQuery
/// Calculates start and end dates based on period type and year
/// </summary>
public class GetDateRangeForPeriodQueryHandler : IRequestHandler<GetDateRangeForPeriodQuery, Response<DateRangeResponseDto>>
{
    public async Task<Response<DateRangeResponseDto>> Handle(GetDateRangeForPeriodQuery request, CancellationToken cancellationToken)
    {
        var currentDate = DateTime.Now;
        var targetYear = request.Year ?? currentDate.Year;
        
        var result = CalculateDateRange(request.Period, targetYear, currentDate);
        
        return await Task.FromResult(new Response<DateRangeResponseDto>(result));
    }
    
    /// <summary>
    /// Calculate date range based on period type and year
    /// </summary>
    private static DateRangeResponseDto CalculateDateRange(DateRangePeriod period, int targetYear, DateTime currentDate)
    {
        DateTime startDate;
        DateTime endDate;
        string periodDescription;
        int actualYear;
        
        switch (period)
        {
            case DateRangePeriod.Today:
                // Hôm nay - always use current date, ignore year parameter
                startDate = currentDate.Date;
                endDate = currentDate.Date.AddDays(1).AddTicks(-1); // End of day
                periodDescription = "Hôm nay";
                actualYear = currentDate.Year;
                break;
                
            case DateRangePeriod.ThisWeek:
                // Tuần này - always use current week, ignore year parameter
                var startOfWeek = GetStartOfWeek(currentDate);
                startDate = startOfWeek.Date;
                endDate = startOfWeek.AddDays(6).Date.AddDays(1).AddTicks(-1); // End of Sunday
                periodDescription = "Tuần này";
                actualYear = currentDate.Year;
                break;
                
            case DateRangePeriod.ThisMonth:
                // Tháng này - always use current month, ignore year parameter
                startDate = new DateTime(currentDate.Year, currentDate.Month, 1);
                endDate = startDate.AddMonths(1).AddTicks(-1); // End of month
                periodDescription = "Tháng này";
                actualYear = currentDate.Year;
                break;
                
            case DateRangePeriod.QuarterI:
                // Quí I (Jan-Mar) - use target year
                startDate = new DateTime(targetYear, 1, 1);
                endDate = new DateTime(targetYear, 3, 31, 23, 59, 59, 999);
                periodDescription = $"Quí I năm {targetYear}";
                actualYear = targetYear;
                break;
                
            case DateRangePeriod.QuarterII:
                // Quí II (Apr-Jun) - use target year
                startDate = new DateTime(targetYear, 4, 1);
                endDate = new DateTime(targetYear, 6, 30, 23, 59, 59, 999);
                periodDescription = $"Quí II năm {targetYear}";
                actualYear = targetYear;
                break;
                
            case DateRangePeriod.QuarterIII:
                // Quý III (Jul-Sep) - use target year
                startDate = new DateTime(targetYear, 7, 1);
                endDate = new DateTime(targetYear, 9, 30, 23, 59, 59, 999);
                periodDescription = $"Quý III năm {targetYear}";
                actualYear = targetYear;
                break;
                
            case DateRangePeriod.QuarterIV:
                // Quý IV (Oct-Dec) - use target year
                startDate = new DateTime(targetYear, 10, 1);
                endDate = new DateTime(targetYear, 12, 31, 23, 59, 59, 999);
                periodDescription = $"Quý IV năm {targetYear}";
                actualYear = targetYear;
                break;
                
            case DateRangePeriod.ThisYear:
                // Năm này - use target year
                startDate = new DateTime(targetYear, 1, 1);
                endDate = new DateTime(targetYear, 12, 31, 23, 59, 59, 999);
                periodDescription = $"Năm {targetYear}";
                actualYear = targetYear;
                break;
                
            default:
                throw new ArgumentException($"Invalid period type: {period}");
        }
        
        return new DateRangeResponseDto
        {
            StartDate = startDate,
            EndDate = endDate,
            PeriodDescription = periodDescription,
            Year = actualYear
        };
    }
    
    /// <summary>
    /// Get start of week (Monday) for given date
    /// </summary>
    private static DateTime GetStartOfWeek(DateTime date)
    {
        var culture = CultureInfo.CurrentCulture;
        var firstDayOfWeek = culture.DateTimeFormat.FirstDayOfWeek;
        
        // Calculate days to subtract to get to start of week
        var daysFromFirstDay = (7 + (date.DayOfWeek - firstDayOfWeek)) % 7;
        return date.AddDays(-daysFromFirstDay);
    }
}
