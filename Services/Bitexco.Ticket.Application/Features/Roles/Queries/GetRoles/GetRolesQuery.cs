﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Roles.Queries.GetRoles;

/// <summary>
/// Query to get all roles with type = pos
/// </summary>
public record GetRolesQuery : IQuery<Response<List<RoleResponseDto>>>;

/// <summary>
/// Handler for getting roles (only POS type)
/// </summary>
public class GetRolesHandler(
    IApplicationDbContext context
) : IQueryHandler<GetRolesQuery, Response<List<RoleResponseDto>>>
{
    public async Task<Response<List<RoleResponseDto>>> Handle(
        GetRolesQuery request, 
        CancellationToken cancellationToken)
    {
        // Only get roles with type = pos
        var roles = await context.Roles
            .Where(r => r.Type == RoleType.pos)
            .ProjectToType<RoleResponseDto>()
            .OrderBy(r => r.Name)
            .ToListAsync(cancellationToken);

        return new Response<List<RoleResponseDto>>(roles);
    }
}
