namespace Bitexco.Ticket.Application.Features.Roles.Queries.GetRoleFunctions;

using Bitexco.Ticket.Application.Dtos;

/// <summary>
/// Role function with permission status
/// </summary>
public record RoleFunctionGrantedDto
{
    public Guid FunctionId { get; set; }
    public string PermissionCode { get; set; } = null!;
    public bool IsGranted { get; set; }
}

/// <summary>
/// Get role functions response
/// </summary>
public record GetRoleFunctionsResponseDto
{
    public IReadOnlyList<FunctionResponseDto> Functions { get; set; } = [];
    public IReadOnlyList<PermissionLabelDto> Permissions { get; set; } = [];
    public IReadOnlyList<RoleFunctionGrantedDto> RoleFunctions { get; set; } = [];
}