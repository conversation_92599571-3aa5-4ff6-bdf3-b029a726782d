﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Helpers;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Roles.Queries.GetRoleFunctions;

/// <summary>
/// Query to get functions and permissions of a specific role
/// </summary>
public record GetRoleFunctionsQuery(Guid RoleId) : IQuery<Response<GetRoleFunctionsResponseDto>>;

/// <summary>
/// Hand<PERSON> for getting role functions with permissions
/// </summary>
public class GetRoleFunctionsHandler(
    IApplicationDbContext context
) : IQueryHandler<GetRoleFunctionsQuery, Response<GetRoleFunctionsResponseDto>>
{
    public async Task<Response<GetRoleFunctionsResponseDto>> Handle(
        GetRoleFunctionsQuery request, 
        CancellationToken cancellationToken)
    {
        // Check if role exists and is POS type
        var role = await context.Roles
            .Where(r => r.Id == request.RoleId && r.Type == RoleType.pos)
            .FirstOrDefaultAsync(cancellationToken);
            
        if (role == null)
        {
            throw new RoleNotFoundException(request.RoleId);
        }

        // Get all functions
        var functions = await context.Functions
            .ProjectToType<FunctionResponseDto>()
            .ToListAsync(cancellationToken);

        // Get all available permissions
        var permissions = RolePermissionHelper.GetPermissions();

        // Get current role functions
        var roleFunctions = await context.RoleFunctions
            .Where(rf => rf.RoleId == request.RoleId)
            .ToListAsync(cancellationToken);

        // Create role function granted matrix
        var roleFunctionGrantedList = new List<RoleFunctionGrantedDto>();
        foreach (var function in functions)
        {
            foreach (var permission in permissions)
            {
                roleFunctionGrantedList.Add(new RoleFunctionGrantedDto
                {
                    FunctionId = function.Id,
                    PermissionCode = permission.Id,
                    IsGranted = roleFunctions.Any(rf => 
                        rf.FunctionId == function.Id && rf.PermissionType == permission.Id)
                });
            }
        }

        var response = new GetRoleFunctionsResponseDto
        {
            Functions = functions,
            Permissions = permissions,
            RoleFunctions = roleFunctionGrantedList
        };

        return new Response<GetRoleFunctionsResponseDto>(response);
    }
}
