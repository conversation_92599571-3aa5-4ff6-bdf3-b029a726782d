﻿using Bitexco.Ticket.Application.Helpers;
using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Http;

namespace Bitexco.Ticket.Application.Features.Roles.Queries.GetUserRoleFunctions;

/// <summary>
/// Query to get functions available to current user for FE menu
/// </summary>
public record GetUserRoleFunctionsQuery : IQuery<Response<List<UserRoleFunctionResponseDto>>>;

/// <summary>
/// Handler for getting user role functions
/// </summary>
public class GetUserRoleFunctionsHandler(
    IHttpContextAccessor httpContextAccessor,
    IApplicationDbContext context
) : IQueryHandler<GetUserRoleFunctionsQuery, Response<List<UserRoleFunctionResponseDto>>>
{
    public async Task<Response<List<UserRoleFunctionResponseDto>>> Handle(
        GetUserRoleFunctionsQuery request, 
        CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        
        if (userId == null)
        {
            throw new UserNotAuthenticatedException();
        }

        // Get user with role (simple approach using existing RoleId)
        var user = await context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.Id == userId.Value, cancellationToken);

        if (user?.Role == null)
        {
            throw new UserNotFoundException(userId.Value);
        }

        // Check if user is admin - if yes, return all functions
        if (RolePermissionHelper.IsAdminRole(user.Role.Type))
        {
            var allFunctions = await context.Functions
                .Select(f => new UserRoleFunctionResponseDto
                {
                    Name = f.Name,
                    Path = f.Path,
                    IconPath = f.IconPath
                })
                .ToListAsync(cancellationToken);

            return new Response<List<UserRoleFunctionResponseDto>>(allFunctions);
        }

        // For POS users, get functions based on role permissions
        var roleFunctions = await context.RoleFunctions
            .Where(rf => rf.RoleId == user.Role.Id && rf.PermissionType == "R") 
            .Include(rf => rf.Function)
            .Select(rf => new UserRoleFunctionResponseDto
            {
                Name = rf.Function.Name,
                Path = rf.Function.Path,
                IconPath = rf.Function.IconPath
            })
            .Distinct()
            .ToListAsync(cancellationToken);

        return new Response<List<UserRoleFunctionResponseDto>>(roleFunctions);
    }
}
