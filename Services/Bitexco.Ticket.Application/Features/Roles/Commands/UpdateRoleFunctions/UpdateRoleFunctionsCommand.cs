﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Roles.Commands.UpdateRoleFunctions;

/// <summary>
/// Command to update role functions and permissions
/// </summary>
public record UpdateRoleFunctionsCommand(
    Guid RoleId, 
    List<UpdateRoleFunctionRequestDto> RoleFunctionList
) : ICommand<Response<UpdateRoleFunctionsResponseDto>>;

/// <summary>
/// Hand<PERSON> for updating role functions
/// </summary>
public class UpdateRoleFunctionsHandler(
    IApplicationDbContext context
) : ICommandHandler<UpdateRoleFunctionsCommand, Response<UpdateRoleFunctionsResponseDto>>
{
    public async Task<Response<UpdateRoleFunctionsResponseDto>> Handle(
        UpdateRoleFunctionsCommand request, 
        CancellationToken cancellationToken)
    {
        // Check if role exists and is POS type
        var role = await context.Roles
            .Where(r => r.Id == request.RoleId && r.Type == RoleType.pos)
            .FirstOrDefaultAsync(cancellationToken);
            
        if (role == null)
        {
            throw new RoleNotFoundException(request.RoleId);
        }

        // Validate function IDs exist
        var functionIds = request.RoleFunctionList
            .Where(x => x.IsGranted)
            .Select(x => x.FunctionId)
            .Distinct()
            .ToList();

        var existingFunctions = await context.Functions
            .Where(f => functionIds.Contains(f.Id))
            .Select(f => f.Id)
            .ToListAsync(cancellationToken);

        var invalidFunctionIds = functionIds.Except(existingFunctions).ToList();
        if (invalidFunctionIds.Count > 0)
        {
            throw new FunctionNotFoundException(string.Join(", ", invalidFunctionIds));
        }

        // Remove existing role functions for this role
        var existingRoleFunctions = await context.RoleFunctions
            .Where(rf => rf.RoleId == request.RoleId)
            .ToListAsync(cancellationToken);

        context.RoleFunctions.RemoveRange(existingRoleFunctions);

        // Add new role functions
        var newRoleFunctions = request.RoleFunctionList
            .Where(x => x.IsGranted)
            .Select(item => new RoleFunction
            {
                RoleId = request.RoleId,
                FunctionId = item.FunctionId,
                PermissionType = item.PermissionType,
                CreatedAt = DateTime.UtcNow
            });

        await context.RoleFunctions.AddRangeAsync(newRoleFunctions, cancellationToken);
        
        var result = await context.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityUpdatedException("RoleFunction", request.RoleId);
        }

        var response = new UpdateRoleFunctionsResponseDto
        {
            IsSuccess = true
        };

        return new Response<UpdateRoleFunctionsResponseDto>(response);
    }
}
