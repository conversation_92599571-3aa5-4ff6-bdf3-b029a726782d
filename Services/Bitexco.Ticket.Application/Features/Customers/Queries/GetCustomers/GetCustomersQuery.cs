using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Customers.Queries.GetCustomers;

/// <summary>
/// Query to get paginated list of customers with optional filtering
/// </summary>
public class GetCustomersQuery : IRequest<PaginationResponse<CustomerResponseDto>>, IPaginationRequest
{
    // Pagination parameters
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 100;
    
    // Filter parameters
    public string? SearchTerm { get; set; } // Tên kh<PERSON>ch hàng, số điện thoại
    public Guid? RetailChannelId { get; set; }
}
