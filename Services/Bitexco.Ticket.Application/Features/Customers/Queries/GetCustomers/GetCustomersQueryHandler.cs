using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Customers.Queries.GetCustomers;

/// <summary>
/// Handler for getting paginated list of customers
/// </summary>
public class GetCustomersQueryHandler(IApplicationDbContext context) : IRequestHandler<GetCustomersQuery, PaginationResponse<CustomerResponseDto>>
{
    public async Task<PaginationResponse<CustomerResponseDto>> <PERSON><PERSON>(GetCustomersQuery request, CancellationToken cancellationToken)
    {
        var query = context.Customers
            .Include(c => c.RetailChannel)
            .AsNoTracking()
            .AsQueryable();

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(c => 
                c.FullName.ToLower().Contains(searchTerm) ||
                c.Code.ToLower().Contains(searchTerm) ||
                (c.PhoneNumber != null && c.PhoneNumber.ToLower().Contains(searchTerm)));
        }

        // Apply RetailChannelId filter through Orders
        if (request.RetailChannelId.HasValue)
        {
            query = query.Where(c => c.RetailChannelId == request.RetailChannelId.Value);
        }

        // Get total count for pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var customers = await query
            .OrderBy(c => c.FullName)
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);
        
        var customerDtos = customers.Select(customer => {
            var dto = customer.Adapt<CustomerResponseDto>();
            dto.RetailChannelName = customer.RetailChannel?.Name;
            return dto;
        }).ToList();

        var response = new PaginationResponse<CustomerResponseDto>
        (
            request.PageIndex,
            request.PageSize,
            totalCount,
            customerDtos
        );

        return response;
    }
}
