using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Customers.Queries.GetCustomerById;

/// <summary>
/// Query to get customer by ID
/// </summary>
public class GetCustomerByIdQuery : IRequest<Response<CustomerDetailResponseDto>>
{
    public Guid Id { get; set; }
}

public record CustomerDetailResponseDto : CustomerResponseDto
{
    public int TotalOrders { get; set; }
    public decimal TotalSpent { get; set; }
    public decimal AverageSpent => TotalOrders > 0 ? TotalSpent / TotalOrders : 0;
    public List<CustomerOrderResponseDto> TopOrders { get; set; } = [];
};

public record CustomerOrderResponseDto : OrderResponseDto
{
    public string? RetailChannelName { get; set; }
    public decimal TotalTickets { get; set; }
}

/// <summary>
/// Handler for getting customer by ID
/// </summary>
public class GetCustomerByIdQueryHandler(IApplicationDbContext context) : IRequestHandler<GetCustomerByIdQuery, Response<CustomerDetailResponseDto>>
{
    public async Task<Response<CustomerDetailResponseDto>> Handle(GetCustomerByIdQuery request, CancellationToken cancellationToken)
    {
        var customer = await context.Customers
            .Include(c => c.Orders!)
                .ThenInclude(o => o.OrderItems)
            .Include(c => c.Orders!)
                .ThenInclude(o => o.RetailChannel)
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken)
            ?? throw new CustomerNotFoundException(request.Id);

        // Calculate total orders and total spent
        var totalOrders = customer.Orders?.Count ?? 0;
        var totalSpent = customer.Orders?.Sum(o => o.TotalAmount) ?? 0;

        // Get top 5 orders by total amount
        var topOrders = customer.Orders?
            .OrderByDescending(o => o.CreatedAt)
            .Take(5)
            .Select(o =>
            {
                return o.Adapt<CustomerOrderResponseDto>() with
                {
                    RetailChannelName = o.RetailChannel?.Name,
                    TotalTickets = o.OrderItems?.Sum(oi => oi.Quantity) ?? 0
                };
            })
            .ToList() ?? [];

        // Map to response DTO
        var customerDetailDto = customer.Adapt<CustomerDetailResponseDto>();
        customerDetailDto.TotalOrders = totalOrders;
        customerDetailDto.TotalSpent = totalSpent;
        customerDetailDto.TopOrders = topOrders;
        customerDetailDto.RetailChannelName = customer.RetailChannel?.Name;

        return new Response<CustomerDetailResponseDto>(customerDetailDto);
    }
}
