using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Customers.Commands.UpdateCustomer;

/// <summary>
/// Command to update an existing customer
/// </summary>
public record UpdateCustomerCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
    public CustomerDto Customer { get; set; } = null!;
}

/// <summary>
/// Handler for updating an existing customer
/// </summary>
public class UpdateCustomerCommandHandler(IApplicationDbContext context) : IRequestHandler<UpdateCustomerCommand, Response<object>>
{
    public async Task<Response<object>> Handle(UpdateCustomerCommand request, CancellationToken cancellationToken)
    {
        var existingCustomer = await context.Customers
            .AsTracking()
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken)
        ?? throw new CustomerNotFoundException(request.Id);
        
        existingCustomer.FullName = request.Customer.FullName;
        existingCustomer.PhoneNumber = request.Customer.PhoneNumber;
        existingCustomer.Email = request.Customer.Email;
        existingCustomer.Type = request.Customer.Type;
        existingCustomer.RetailChannelId = request.Customer.RetailChannelId;
        existingCustomer.CompanyName = request.Customer.CompanyName;
        existingCustomer.TaxCode = request.Customer.TaxCode;
        existingCustomer.Country = request.Customer.Country;
        existingCustomer.Address = request.Customer.Address;
        existingCustomer.IsActive = request.Customer.IsActive;

        context.Customers.Update(existingCustomer);
        var result = await context.SaveChangesAsync(cancellationToken);
        
        if (result == 0)
        {
            throw new NoEntityUpdatedException("Customer", request.Id);
        }

        return new Response<object>(new { status = true });
    }
}
