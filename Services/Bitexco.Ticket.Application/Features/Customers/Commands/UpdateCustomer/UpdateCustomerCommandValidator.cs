using FluentValidation;

namespace Bitexco.Ticket.Application.Features.Customers.Commands.UpdateCustomer;

/// <summary>
/// Validator for UpdateCustomerCommand
/// </summary>
public class UpdateCustomerCommandValidator : AbstractValidator<UpdateCustomerCommand>
{
    public UpdateCustomerCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Customer ID is required");

        RuleFor(x => x.Customer.FullName)
            .NotEmpty().WithMessage("Customer name is required")
            .MaximumLength(200).WithMessage("Customer name cannot exceed 200 characters");

        RuleFor(x => x.Customer.PhoneNumber)
            .MaximumLength(20).WithMessage("Phone number cannot exceed 20 characters")
            .When(x => !string.IsNullOrEmpty(x.Customer.PhoneNumber));

        RuleFor(x => x.Customer.Email)
            .EmailAddress().WithMessage("Invalid email address format")
            .MaximumLength(100).WithMessage("Email cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.Customer.Email));

        RuleFor(x => x.Customer.Type)
            .IsInEnum().WithMessage("Invalid customer type");

        RuleFor(x => x.Customer.CompanyName)
            .MaximumLength(200).WithMessage("Company name cannot exceed 200 characters")
            .When(x => !string.IsNullOrEmpty(x.Customer.CompanyName));

        RuleFor(x => x.Customer.TaxCode)
            .MaximumLength(50).WithMessage("Tax code cannot exceed 50 characters")
            .When(x => !string.IsNullOrEmpty(x.Customer.TaxCode));

        RuleFor(x => x.Customer.Address)
            .MaximumLength(500).WithMessage("Address cannot exceed 500 characters")
            .When(x => !string.IsNullOrEmpty(x.Customer.Address));
    }
}
