﻿using BuildingBlocks.FileServices;
using System.ComponentModel.DataAnnotations;

namespace Bitexco.Ticket.Application.Features.Customers.Commands.ExportCustomer
{
    public class ExportCustomerCommand : ICommand<Response<ExportCustomerResponse>>
    {
        public string? SearchTerm { get; set; }
        public Guid? RetailChannelId { get; set; }
    }

    public class ExportCustomerResponse
    {
        public string? file { get; set; }
    }

    public class ExportCustomerData
    {
        [Display(Name = "STT")]
        public int STT { get; set; }

        [Display(Name = "Quốc gia")]
        public string? Country { get; set; }

        [Display(Name = "Tên khách hàng")]
        public string? FullName { get; set; }

        [Display(Name = "Số điện thoại")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "Địa chỉ")]
        public string? Address { get; set; }

        [Display(Name = "Mã số thuế cá nhân")]
        public string? TaxCode { get; set; }

        [Display(Name = "Mã khách hàng")]
        public string? Code { get; set; }
    }

    public class ExportCustomerCommandHandler(IApplicationDbContext context) : ICommandHandler<ExportCustomerCommand, Response<ExportCustomerResponse>>
    {
        private readonly IApplicationDbContext _context = context;

        public async Task<Response<ExportCustomerResponse>> Handle(ExportCustomerCommand request, CancellationToken cancellationToken)
        {
            int index = 1;
            var query = _context.Customers.AsQueryable();

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower();
                query = query.Where(c =>
                    c.FullName.ToLower().Contains(searchTerm) ||
                    (c.PhoneNumber != null && c.PhoneNumber.ToLower().Contains(searchTerm)));
            }
            // Apply RetailChannelId filter through Orders
            if (request.RetailChannelId.HasValue)
            {
                query = query.Where(c => c.Orders!.Any(o => o.RetailChannelId == request.RetailChannelId.Value));
            }

            var customers = await query.ToListAsync();

            var exportList = customers.Select(a => new ExportCustomerData
            {
                STT = index++,
                Address = a.Address,
                FullName = a.FullName,
                PhoneNumber = a.PhoneNumber,
                TaxCode = a.TaxCode,
                Country = a.Country,
                Code = a.Code,
            }).ToList();

            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "Customers", "CustomerExportFile.xlsx");

            string path = "exports/customers";
            string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);
            string fileName = $"CustomerExport_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
            string filefolder = Path.Combine(folder, fileName);

            ExcelServices.ExportData(exportList, templatePath, filefolder, 2);
            return new Response<ExportCustomerResponse>()
            {
                Data = new ExportCustomerResponse { file = path + "/" + fileName }
            };
        }
    }
}