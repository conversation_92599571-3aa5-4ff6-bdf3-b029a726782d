﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.FileServices;
using BuildingBlocks.Utils;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Bitexco.Ticket.Application.Features.Customers.Commands.ImportCustomer
{
    public class CustomerImportCommand : ICommand<Response<List<CustomerResponseDto>>>
    {
        public IFormFile FilePath { get; set; } = null!; // Required file for import
    }

    public class CustomerImportData
    {
        [Display(Name = "STT")]
        public string? STT { get; set; }

        [Display(Name = "Quốc gia")]
        public string? Nation { get; set; }

        [Display(Name = "Tên khách hàng")]
        public string? FullName { get; set; }

        [Display(Name = "Số điện thoại")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "Địa chỉ")]
        public string? Address { get; set; }

        [Display(Name = "Mã số thuế cá nhân")]
        public string? TaxCode { get; set; }
    }

    public class CustomerImportCommandValidator : AbstractValidator<CustomerImportCommand>
    {
        public CustomerImportCommandValidator()
        {
            RuleFor(x => x.FilePath)
                 .NotNull()
                 .WithMessage("File path cannot be null.")
                 .NotEmpty()
                 .WithMessage("File path cannot be empty.")
                 .Must(x => ExcelServices.IsValidFileExtension(x!.FileName))
                 .WithMessage("File must have a valid extension: .xls or .xlsx.");
        }
    }

    public class ImportCustomerDataValidation : AbstractValidator<CustomerImportData>
    {
        public ImportCustomerDataValidation()
        {
            RuleFor(x => x.STT)
                  .NotEmpty()
                  .WithMessage("STT cannot be empty.");

            RuleFor(x => x.FullName)
                .NotEmpty()
                .WithMessage("Full Name cannot be empty.");

            RuleFor(x => x.PhoneNumber)
                .NotEmpty()
                .NotEmpty().WithMessage("Phone number is required")
                .Matches(@"^\+?[1-9]\d{1,14}$").WithMessage("Phone number is not valid");

            RuleFor(x => x.Nation)
                .NotEmpty()
                .WithMessage("Nation cannot be empty.");

            RuleFor(x => x.Address)
                .NotEmpty()
                .WithMessage("Address cannot be empty.");
        }
    }

    public class CustomerImportCommandHandler(IApplicationDbContext context)
        : ICommandHandler<CustomerImportCommand, Response<List<CustomerResponseDto>>>
    {
        public async Task<Response<List<CustomerResponseDto>>> Handle(CustomerImportCommand request, CancellationToken cancellationToken)
        {
            var mappings = ExcelServices.GetPropertyMappings<CustomerImportData>();
            var result = await ExcelServices.ImportFile<CustomerImportData>(request.FilePath, mappings);

            var customers = new List<Customer>();

            foreach (var data in result)
            {
                var customer = new Customer
                {
                    FullName = data.FullName ?? string.Empty,
                    PhoneNumber = data.PhoneNumber,
                    Address = data.Address,
                    Type = CustomerType.individual, // Default type
                    IsActive = true, // Default active status,
                    Code = CommonUtils.GenerateNewCode(7),
                    TaxCode = data.TaxCode,
                    Country = data.Nation
                };

                customers.Add(customer);
            }

            context.Customers.AddRange(customers);
            var saveResult = await context.SaveChangesAsync(cancellationToken);
            if (saveResult > 0)
            {
                var response = customers.Select(c => new CustomerResponseDto
                {
                    Id = c.Id,
                    Code = c.Code,
                    FullName = c.FullName,
                    PhoneNumber = c.PhoneNumber,
                    Email = c.Email,
                    Type = c.Type,
                    CompanyName = c.CompanyName,
                    TaxCode = c.TaxCode,
                    Address = c.Address,
                    IsActive = c.IsActive,
                    Country = c.Country,
                }).ToList();
                return new Response<List<CustomerResponseDto>>(response);
            }

            throw new NoEntityCreatedException("Customer");
        }
    }
}