using Bitexco.Ticket.Application.Dtos;
using BuildingBlocks.Utils;

namespace Bitexco.Ticket.Application.Features.Customers.Commands.CreateCustomer;

/// <summary>
/// Command to create a new customer
/// </summary>
public record CreateCustomerCommand : IRequest<Response<CreateCustomerResult>>
{
    public CustomerDto Customer { get; set; } = null!;
}

public record CreateCustomerResult(Guid Id);

/// <summary>
/// Handler for creating a new customer
/// </summary>
public class CreateCustomerCommandHandler(IApplicationDbContext context) : IRequestHandler<CreateCustomerCommand, Response<CreateCustomerResult>>
{
    public async Task<Response<CreateCustomerResult>> Handle(CreateCustomerCommand request, CancellationToken cancellationToken)
    {
        var customer = request.Customer.Adapt<Customer>();
        customer.Code = CommonUtils.GenerateNewCode(8);
        context.Customers.Add(customer);
        var result = await context.SaveChangesAsync(cancellationToken);
        
        if (result == 0)
        {
            throw new NoEntityCreatedException("Customer");
        }

        return new Response<CreateCustomerResult>(new CreateCustomerResult(customer.Id));
    }
}
