namespace Bitexco.Ticket.Application.Features.Customers.Commands.DeleteCustomer;

/// <summary>
/// Command to delete a customer (soft delete)
/// </summary>
public class DeleteCustomerCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for deleting a customer
/// </summary>
public class DeleteCustomerCommandHandler(IApplicationDbContext context) : IRequestHandler<DeleteCustomerCommand, Response<object>>
{
    public async Task<Response<object>> Handle(DeleteCustomerCommand request, CancellationToken cancellationToken)
    {
        // Find existing customer
        var existingCustomer = await context.Customers
            .AsTracking()
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken)
            ?? throw new CustomerNotFoundException(request.Id); 
        
        existingCustomer.IsDeleted = true;
        
        context.Customers.Update(existingCustomer);
        var result = await context.SaveChangesAsync(cancellationToken);
        
        if (result == 0)
        {
            throw new NoEntityDeletedException("Customer", request.Id);
        }

        return new Response<object>(new { status = true });
    }
}
