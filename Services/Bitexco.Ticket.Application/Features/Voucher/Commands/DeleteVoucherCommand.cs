﻿namespace Bitexco.Ticket.Application.Features.Voucher.Commands
{
    public record DeleteVoucherCommand(Guid Id) : IRequest<Response<object>>;

    public class DeleteVoucherCommandHandler(IApplicationDbContext dbContext)
        : IRequestHandler<DeleteVoucherCommand, Response<object>>
    {
        public async Task<Response<object>> Handle(DeleteVoucherCommand request, CancellationToken cancellationToken)
        {
            var voucher = await dbContext.Vouchers.FindAsync([request.Id], cancellationToken: cancellationToken)
                ?? throw new VoucherNotFoundException("Voucher not found");
            voucher.IsDeleted = true;

            dbContext.Vouchers.Update(voucher);
            await dbContext.SaveChangesAsync(cancellationToken);
            return new Response<object>(true, "Voucher deleted successfully");
        }
    }
}