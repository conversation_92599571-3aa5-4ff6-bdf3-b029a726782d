﻿using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Voucher.Commands
{
    public record CreateVoucherCommand(VoucherDto Voucher) : IRequest<Response<CreateVoucherResult>>
    {
    }

    public record CreateVoucherResult(Guid Id);

    public class CreateVoucherCommandHandler(IApplicationDbContext _dbContext) 
        : IRequestHandler<CreateVoucherCommand, Response<CreateVoucherResult>>
    {
        private readonly IApplicationDbContext dbContext = _dbContext;
        // Implementation of the command handler to create a new voucher
        public async Task<Response<CreateVoucherResult>> Handle(CreateVoucherCommand request, CancellationToken cancellationToken)
        {
            var voucher = request.Voucher.Adapt<Domain.Models.Voucher>();
            dbContext.Vouchers.Add(voucher);
            var result = await dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new NoEntityCreatedException("Voucher");
            }

            return new Response<CreateVoucherResult>(new CreateVoucherResult(voucher.Id));
        }
    }
}