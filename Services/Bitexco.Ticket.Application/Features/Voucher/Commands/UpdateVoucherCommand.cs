﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Voucher.Commands
{
    public record UpdateVoucherCommand(Guid Id, VoucherDto Voucher) : IRequest<Response<UpdateVoucherResult>>; // Command to update an existing voucher

    public record UpdateVoucherResult(Guid Id); // Result of the update operation

    public class UpdateVoucherCommandHandler(IApplicationDbContext _dbContext) : IRequestHandler<UpdateVoucherCommand, Response<UpdateVoucherResult>>
    {
        private readonly IApplicationDbContext dbContext = _dbContext;

        // Implementation of the command handler to update the voucher
        public async Task<Response<UpdateVoucherResult>> Handle(UpdateVoucherCommand request, CancellationToken cancellationToken)
        {
            var voucher = await dbContext.Vouchers
                .AsNoTracking()
                .FirstOrDefaultAsync(v => v.Id == request.Id, cancellationToken)
                ?? throw new VoucherNotFoundException("Voucher not found");

            voucher.Code = request.Voucher.Code;
            voucher.Type = request.Voucher.Type;
            voucher.DiscountValue = request.Voucher.DiscountValue;
            voucher.UsageLimit = request.Voucher.UsageLimit;
            voucher.CurrentUsage = request.Voucher.CurrentUsage;
            voucher.ValidFrom = request.Voucher.ValidFrom;
            voucher.ValidTo = request.Voucher.ValidTo;
            voucher.IsActive = request.Voucher.IsActive;
            
            dbContext.Vouchers.Update(voucher);
            var result = await dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new NoEntityUpdatedException("Voucher", request.Id);
            }

            return new Response<UpdateVoucherResult>(new UpdateVoucherResult(voucher.Id));
        }
    }
}