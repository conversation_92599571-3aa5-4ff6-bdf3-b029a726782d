﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Voucher.Queries
{
    public record GetVoucherByCodeQuery : IQuery<Response<VoucherResponseDto>>
    {
        public string? Code { get; set; }
        public CustomerType CustomerType { get; set; }
        public Guid? AgentId { get; set; }
    }

    public class GetVoucherByCodeQueryHandler(IApplicationDbContext dbContext)
        : IQueryHandler<GetVoucherByCodeQuery, Response<VoucherResponseDto>>
    {
        public async Task<Response<VoucherResponseDto>> Handle(
            GetVoucherByCodeQuery request,
            CancellationToken cancellationToken)
        {
            var voucher = await dbContext.Vouchers
                .AsNoTracking()
                .FirstOrDefaultAsync(v => v.Code == request.Code, cancellationToken) ?? throw new VoucherNotFoundException("Voucher not found with the provided code");

            if (voucher.ValidFrom > DateTime.UtcNow || voucher.ValidTo < DateTime.UtcNow)
                throw new VoucherExpiredException(voucher.Code);

            if (voucher.UsageLimit.HasValue && voucher.CurrentUsage >= voucher.UsageLimit)
                throw new VoucherLimitReachException(voucher.Code);

            if (!voucher.IsActive)
                throw new VoucherInActiveException(voucher.Code);

            if (voucher.VoucherApplyType == VoucherApplyType.individual)
            {
                // check request cos phari laf khach le hay k
                if (request.CustomerType != CustomerType.individual)
                    throw new VoucherNotApplicableException(voucher.Code);
            }
            else if (voucher.VoucherApplyType == VoucherApplyType.include_corporate)
            {
                if (request.CustomerType != CustomerType.corporate && request.CustomerType != CustomerType.ship)
                    throw new VoucherNotApplicableException(voucher.Code);

                if (request.AgentId.HasValue)
                    if (voucher.AgentIds == null || !voucher.AgentIds.Contains(request.AgentId.Value))
                        throw new VoucherNotApplicableException(voucher.Code);
            }
            else if (voucher.VoucherApplyType == VoucherApplyType.exclude_corporate)
            {
                if (request.CustomerType != CustomerType.corporate && request.CustomerType != CustomerType.ship)
                    throw new VoucherNotApplicableException(voucher.Code);
                
                if (request.AgentId.HasValue)
                    if (voucher.AgentIds != null && voucher.AgentIds.Contains(request.AgentId.Value))
                        throw new VoucherNotApplicableException(voucher.Code);
            }

            var result = voucher.Adapt<VoucherResponseDto>();
            return new Response<VoucherResponseDto>(result);
        }
    }
}