﻿using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Voucher.Queries
{
    public record GetAllVoucherQuery(GetAllVoucherQueryDto Request) : IRequest<PaginationResponse<VoucherResponseDto>>;

    public record GetAllVoucherQueryDto : IPaginationRequest
    {
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 100;
        public string? Code { get; set; }
        public DateTime? ValidFrom { get; set; }
        public DateTime? ValidTo { get; set; }
    }

    public class GetAllVoucherHandler(IApplicationDbContext dbContext) : IRequestHandler<GetAllVoucherQuery, PaginationResponse<VoucherResponseDto>>
    {
        private readonly IApplicationDbContext _dbContext = dbContext;

        public async Task<PaginationResponse<VoucherResponseDto>> Handle(GetAllVoucherQuery request, CancellationToken cancellationToken)
        {
            var query = _dbContext.Vouchers.AsQueryable();
            if (!string.IsNullOrEmpty(request.Request.Code))
            {
                query = query.Where(v => v.Code.Contains(request.Request.Code));
            }
            if (request.Request.ValidFrom.HasValue)
            {
                query = query.Where(v => v.ValidFrom >= request.Request.ValidFrom.Value);
            }
            if (request.Request.ValidTo.HasValue)
            {
                query = query.Where(v => v.ValidTo <= request.Request.ValidTo.Value);
            }
            var totalCount = await query.CountAsync(cancellationToken);
            var vouchers = await query
                .Skip((request.Request.PageIndex - 1) * request.Request.PageSize)
                .Take(request.Request.PageSize)
                .ProjectToType<VoucherResponseDto>()
                .ToListAsync(cancellationToken);

            return new PaginationResponse<VoucherResponseDto>(request.Request.PageIndex,
                request.Request.PageSize, totalCount, vouchers.AsEnumerable());
        }
    }
}