namespace Bitexco.Ticket.Application.Features.Orders.ProcessRefund;

using Bitexco.Ticket.Domain.Enums;
using System;

/// <summary>
/// Response DTO for order refund processing
/// </summary>
public class ProcessRefundResponseDto
{
    public Guid Id { get; set; }
    public DateOnly OrderDate { get; set; }
    public OrderStatus Status { get; set; }
    public OrderRefundStatus? RefundStatus { get; set; }
    public string RefundReason { get; set; } = string.Empty;
    public DateTime? ProcessedAt { get; set; }
}
