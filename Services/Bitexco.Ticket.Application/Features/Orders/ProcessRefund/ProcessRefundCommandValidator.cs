namespace Bitexco.Ticket.Application.Features.Orders.ProcessRefund;

using FluentValidation;
using System;

/// <summary>
/// Validator for ProcessRefundCommand
/// </summary>
public class ProcessRefundCommandValidator : AbstractValidator<ProcessRefundCommand>
{
    public ProcessRefundCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("Order ID is required");

        RuleFor(x => x.Reason)
            .NotEmpty().WithMessage("Refund reason is required")
            .MaximumLength(500).WithMessage("Refund reason cannot exceed 500 characters");
    }
}
