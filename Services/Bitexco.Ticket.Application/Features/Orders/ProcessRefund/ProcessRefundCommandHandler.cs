namespace Bitexco.Ticket.Application.Features.Orders.ProcessRefund;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;

/// <summary>
/// Hand<PERSON> for processing an order refund
/// </summary>
public class ProcessRefundCommandHandler(
    IOrderRepository orderRepository) : IRequestHandler<ProcessRefundCommand, Response<ProcessRefundResponseDto>>
{
    private readonly IOrderRepository _orderRepository = orderRepository;

    public async Task<Response<ProcessRefundResponseDto>> Handle(ProcessRefundCommand request, CancellationToken cancellationToken)
    {
        // Get the order
        var order = await _orderRepository.GetOrderByIdAsync(request.OrderId, cancellationToken) ?? throw new OrderNotFoundException(request.OrderId);

        // Validate that the order can be refunded
        if (order.Status != OrderStatus.paid)
        {
            throw new BadRequestException("Only paid orders can be refunded");
        }

        // Process the refund
        var updatedOrder = await _orderRepository.ProcessRefundAsync(request.OrderId, request.Reason, cancellationToken) 
            ?? throw new OrderNotFoundException($"Order with ID {request.OrderId} not found during refund processing");

        // Map and return response
        var response = new ProcessRefundResponseDto
        {
            Id = updatedOrder.Id,
            OrderDate = updatedOrder.OrderDate,
            Status = updatedOrder.Status,
            RefundStatus = updatedOrder.RefundStatus,
            RefundReason = updatedOrder.RefundReason ?? string.Empty,
            ProcessedAt = updatedOrder.UpdatedAt
        };

        return new Response<ProcessRefundResponseDto>(response);
    }
}
