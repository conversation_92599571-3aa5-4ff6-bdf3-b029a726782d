namespace Bitexco.Ticket.Application.Features.Orders.ProcessRefund;

using BuildingBlocks.Abstractions;
using MediatR;
using System;

/// <summary>
/// Command to process a refund for an order
/// </summary>
public class ProcessRefundCommand : IRequest<Response<ProcessRefundResponseDto>>
{
    /// <summary>
    /// ID of the order to refund
    /// </summary>
    public Guid OrderId { get; set; }
    
    /// <summary>
    /// Reason for the refund
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}
