namespace Bitexco.Ticket.Application.Features.Orders.UpdateOrderStatus;

using Bitexco.Ticket.Domain.Enums;
using System;

/// <summary>
/// Response DTO for order status update
/// </summary>
public class UpdateOrderStatusResponseDto
{
    public Guid Id { get; set; }
    public DateOnly OrderDate { get; set; }
    public OrderStatus Status { get; set; }
    public string StatusDisplay => Status.ToString();
    public DateTime? UpdatedAt { get; set; }
}
