namespace Bitexco.Ticket.Application.Features.Orders.UpdateOrderStatus;

using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;
using MediatR;
using System;

/// <summary>
/// Command to update an order's status
/// </summary>
public class UpdateOrderStatusCommand : IRequest<Response<UpdateOrderStatusResponseDto>>
{
    /// <summary>
    /// ID of the order to update
    /// </summary>
    public Guid OrderId { get; set; }
    
    /// <summary>
    /// New status to set
    /// </summary>
    public OrderStatus Status { get; set; }
}
