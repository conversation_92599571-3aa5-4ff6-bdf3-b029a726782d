namespace Bitexco.Ticket.Application.Features.Orders.UpdateOrderStatus;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Events;
using Bitexco.Ticket.Domain.Models;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// Handler for updating an order's status
/// </summary>
public class UpdateOrderStatusCommandHandler : IRequestHandler<UpdateOrderStatusCommand, Response<UpdateOrderStatusResponseDto>>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IApplicationDbContext _dbContext;
    private readonly IPublisher _publisher;

    public UpdateOrderStatusCommandHandler(
        IOrderRepository orderRepository,
        IApplicationDbContext dbContext,
        IPublisher publisher)
    {
        _orderRepository = orderRepository;
        _dbContext = dbContext;
        _publisher = publisher;
    }

    public async Task<Response<UpdateOrderStatusResponseDto>> Handle(UpdateOrderStatusCommand request, CancellationToken cancellationToken)
    {
        // Get the existing order
        var order = await _orderRepository.GetOrderByIdAsync(request.OrderId, cancellationToken);
        if (order == null)
        {
            throw new OrderNotFoundException(request.OrderId);
        }

        // Check if the status transition is valid
        if (!IsValidStatusTransition(order.Status, request.Status))
        {
            throw new BadRequestException($"Invalid status transition from {order.Status} to {request.Status}");
        }

        // Update the order status
        var updatedOrder = await _orderRepository.UpdateOrderStatusAsync(request.OrderId, request.Status, cancellationToken) 
            ?? throw new NotFoundException($"Order with ID {request.OrderId} not found during update");
        
        if (order.Status == OrderStatus.pending_payment && request.Status == OrderStatus.paid)
        {
            await CreateCommissionOrderHistoryAsync(request.OrderId, cancellationToken);
        }

        // Map and return response
        var response = new UpdateOrderStatusResponseDto
        {
            Id = updatedOrder.Id,
            OrderDate = updatedOrder.OrderDate,
            Status = updatedOrder.Status,
            UpdatedAt = updatedOrder.UpdatedAt,
        };

        return new Response<UpdateOrderStatusResponseDto>(response);
    }

    /// <summary>
    /// Creates commission order history when order payment is successful
    /// </summary>
    private async Task CreateCommissionOrderHistoryAsync(Guid orderId, CancellationToken cancellationToken)
    {
        // Get order with agent information
        var order = await _dbContext.Orders
            .Include(o => o.Agent)
            .FirstOrDefaultAsync(o => o.Id == orderId, cancellationToken)
            ?? throw new OrderNotFoundException(orderId);

        // Only create commission if order has an agent
        if (order.AgentId == null || order.Agent == null)
        {
            return; 
        }
        
        var commissionRate = order.Agent.CommissionRatePercentage ?? 0;
        var commissionAmount = order.FinalAmount * (commissionRate / 100);

        // Create commission order history record
        var commissionOrderHistory = new CommissionOrderHistory
        {
            Id = Guid.NewGuid(),
            AgentId = order.AgentId.Value,
            OrderId = order.Id,
            CommissionRatePercentage = commissionRate,
            CommissionAmount = commissionAmount,
            PaidAmount = 0, 
            PaymentDate = null,
            PaymentMethod = CommissionPaymentMethod.bank_transfer, // Default
            PaymentProofUrl = null,
            Status = CommissionPaymentStatus.pending, 
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System", 
            UpdatedAt = null,
            UpdatedBy = null
        };
        
        _dbContext.CommissionOrderHistories.Add(commissionOrderHistory);
        var result = await _dbContext.SaveChangesAsync(cancellationToken);

        if (result == 0)
        {
            throw new NoEntityCreatedException("CommissionOrderHistory");
        }
    }

    /// <summary>
    /// Validates if the status transition is allowed
    /// </summary>
    private static bool IsValidStatusTransition(OrderStatus currentStatus, OrderStatus newStatus)
    {
        // Define valid transitions
        return currentStatus switch
        {
            OrderStatus.pending_payment => newStatus == OrderStatus.paid || newStatus == OrderStatus.cancelled,// From pending_payment, can move to paid or cancelled
            OrderStatus.paid => newStatus == OrderStatus.refunded || newStatus == OrderStatus.cancelled,// From paid, can move to refunded or cancelled
            OrderStatus.cancelled => false,// Cannot change status once cancelled
            OrderStatus.refunded => false,// Cannot change status once refunded
            _ => false,
        };
    }
}
