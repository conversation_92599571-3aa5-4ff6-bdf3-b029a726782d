namespace Bitexco.Ticket.Application.Features.Orders.UpdateOrderStatus;

using Bitexco.Ticket.Domain.Enums;
using FluentValidation;
using System;

/// <summary>
/// Validator for UpdateOrderStatusCommand
/// </summary>
public class UpdateOrderStatusCommandValidator : AbstractValidator<UpdateOrderStatusCommand>
{
    public UpdateOrderStatusCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("Order ID is required");

        RuleFor(x => x.Status)
            .IsInEnum().WithMessage("Invalid order status");

        // Additional validation: Cannot transition directly from pending to refunded
        RuleFor(x => x.Status)
            .Must((command, status) => IsValidStatusTransition(command, status))
            .WithMessage("Invalid status transition. Orders must be paid before they can be refunded.");
    }

    private bool IsValidStatusTransition(UpdateOrderStatusCommand command, OrderStatus newStatus)
    {
        // If trying to set status to refunded, we'll validate this in the handler
        // by checking the current status of the order
        return true;
    }
}
