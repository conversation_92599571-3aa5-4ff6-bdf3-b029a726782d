namespace Bitexco.Ticket.Application.Features.Orders.GetOrderById;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using BuildingBlocks.Abstractions;
using Bitexco.Ticket.Application.Features.Agents.Dtos;
using Bitexco.Ticket.Application.Dtos;

/// <summary>
/// Handler for getting order details by ID
/// </summary>
public class GetOrderByIdQueryHandler(IOrderRepository orderRepository) : IRequestHandler<GetOrderByIdQuery, Response<GetOrderByIdResponseDto>>
{
    private readonly IOrderRepository _orderRepository = orderRepository;

    public async Task<Response<GetOrderByIdResponseDto>> Handle(GetOrderByIdQuery request, CancellationToken cancellationToken)
    {
        // Retrieve order with all related data
        var order = await _orderRepository.GetOrderDetailByIdAsync(request.OrderId, cancellationToken)
            ?? throw new OrderNotFoundException(request.OrderId);

        // Map order to detailed response DTO
        var orderDto = new GetOrderByIdResponseDto
        {
            Id = order.Id,
            OrderDate = order.OrderDate,
            OrderTime = order.OrderTime,
            Code = order.Code,
            CustomerId = order.CustomerId,
            AgentId = order.AgentId,
            RetailChannelId = order.RetailChannelId,
            PosUserId = order.PosUserId,
            ShiftId = order.ShiftId,
            PosDeviceId = order.PosDeviceId,
            VoucherId = order.VoucherId,
            IsDebtPayment = order.IsDebtPayment,
            LastPaymentMethod = order.LastPaymentMethod,
            CustomerType = order.CustomerType,
            TotalAmount = order.TotalAmount,
            DiscountAmount = order.DiscountAmount,
            FinalAmount = order.FinalAmount,
            Status = order.Status,
            RefundStatus = order.RefundStatus,
            RefundReason = order.RefundReason
        };

        // Map customer if available
        if (order.Customer != null)
        {
            orderDto.Customer = order.Customer.Adapt<CustomerResponseDto>();
        }

        // Map agent if available
        if (order.Agent != null)
        {
            orderDto.Agent = order.Agent.Adapt<AgentResponseDetailDto>();
        }

        // Map retail channel
        if (order.RetailChannel != null)
        {
            orderDto.RetailChannel = new RetailChannelResponseDto
            {
                Id = order.RetailChannel.Id,
                Name = order.RetailChannel.Name
            };
        }

        // Map POS user if available
        if (order.PosUser != null)
        {
            orderDto.PosUser = new UserDetailDto
            {
                Id = order.PosUser.Id,
                FullName = order.PosUser.FullName ?? string.Empty,
                UserName = order.PosUser.UserName
            };
        }

        // Map order items and tickets
        if (order.OrderItems != null)
        {
            foreach (var item in order.OrderItems)
            {
                var itemDto = new OrderItemDetailDto
                {
                    Id = item.Id,
                    Quantity = item.Quantity,
                    UnitPrice = item.UnitPrice,
                    SubTotal = item.SubTotal,
                    VisitDate = item.VisitDate,
                    VisitTime = item.VisitTime,
                    TicketType = new TicketTypeDetailDto
                    {
                        Id = item.TicketType.Id,
                        Name = item.TicketType.Name,
                        Description = item.TicketType.Description
                    }
                };

                // Map tickets if available
                if (item.Tickets != null)
                {
                    foreach (var ticket in item.Tickets)
                    {
                        itemDto.Tickets.Add(ticket.Adapt<TicketResponseDto>());
                    }
                }
            }
        }

        // Map payment transactions if available
        if (order.PaymentTransactions != null)
        {
            foreach (var transaction in order.PaymentTransactions)
            {
                orderDto.PaymentTransactions.Add(transaction.Adapt<PaymentTransactionResponseDto>());
            }
        }

        // Map invoice if available
        if (order.Invoice != null)
        {
            orderDto.Invoice =  order.Invoice.Adapt<InvoiceResponseDto>();
        }

        return new Response<GetOrderByIdResponseDto>(orderDto);
    }
}
