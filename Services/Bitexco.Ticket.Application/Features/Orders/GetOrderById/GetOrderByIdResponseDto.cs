namespace Bitexco.Ticket.Application.Features.Orders.GetOrderById;

using Bitexco.Ticket.Application.Dtos;

/// <summary>
/// Detailed response DTO for a single order
/// </summary>
public record GetOrderByIdResponseDto : OrderResponseDto
{
    public CustomerResponseDto? Customer { get; set; }
    public AgentResponseDto? Agent { get; set; }
    public RetailChannelResponseDto RetailChannel { get; set; } = null!;
    public UserDetailDto? PosUser { get; set; }
    public string StatusDisplay => Status.ToString();
    public List<OrderItemResponseDto> Items { get; set; } = [];
    public List<PaymentTransactionResponseDto> PaymentTransactions { get; set; } = [];
    public InvoiceResponseDto? Invoice { get; set; }
    public List<TicketTypeConditionResponseDto>? TicketTypeConditions { get; set; }
}

/// <summary>
/// Detailed user (POS operator) information
/// </summary>
public class UserDetailDto
{
    public Guid Id { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
}

/// <summary>
/// Detailed order item information
/// </summary>
public class OrderItemDetailDto
{
    public Guid Id { get; set; }
    public TicketTypeDetailDto TicketType { get; set; } = null!;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal SubTotal { get; set; }
    public DateOnly VisitDate { get; set; }
    public TimeOnly VisitTime { get; set; }
    public List<TicketResponseDto> Tickets { get; set; } = [];
}

/// <summary>
/// Detailed ticket type information
/// </summary>
public class TicketTypeDetailDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}
