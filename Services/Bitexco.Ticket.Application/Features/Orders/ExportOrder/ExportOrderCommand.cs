﻿using BuildingBlocks.FileServices;
using System.ComponentModel.DataAnnotations;

namespace Bitexco.Ticket.Application.Features.Orders.ExportOrder
{
    public class ExportOrderCommand : ICommand<Response<ExportOrderResponse>>
    {
        public string? Search { get; set; }
        public Guid? RetailChannel { get; set; }
        public OrderStatus? Status { get; set; }
        public DateTime? CreatedAt { get; set; }
        public CustomerType CustomerType { get; set; } = CustomerType.individual;
    }

    public class ExportOrderData
    {
        [Display(Name = "STT")]
        public string? STT { get; set; }

        [Display(Name = "Mã đơn hàng")]
        public string? Code { get; set; }

        [Display(Name = "Kênh bán hàng")]
        public string? RetailChannel { get; set; }

        [Display(Name = "Trạng thái")]
        public string? Status { get; set; }

        [Display(Name = "Ngày tạo đơn")]
        public DateTime CreatedAt { get; set; }
    }

    public class ExportOrderResponse
    {
        public string? File { get; set; }
    }

    public class ExportOrderCommandValidator : AbstractValidator<ExportOrderCommand>
    {
        public ExportOrderCommandValidator()
        {
            RuleFor(x => x.Search).MaximumLength(100).WithMessage("Search term cannot exceed 100 characters.");
        }
    }

    public class ExportOrderCommandHandler(IApplicationDbContext applicationDbContext
        ) : ICommandHandler<ExportOrderCommand, Response<ExportOrderResponse>>
    {
        public async Task<Response<ExportOrderResponse>> Handle(ExportOrderCommand request, CancellationToken cancellationToken)
        {
            // Logic to export orders based on the command parameters
            var query = applicationDbContext.Orders.AsQueryable();
            query = query.Where(o => o.CustomerType == request.CustomerType);
            if (!string.IsNullOrEmpty(request.Search))
            {
                var searchTerm = request.Search.ToUpper();
                query = query.Where(o => o.Code.Contains(searchTerm));
            }
            if (request.RetailChannel.HasValue)
            {
                query = query.Where(o => o.RetailChannelId == request.RetailChannel.Value);
            }
            if (request.Status.HasValue)
            {
                query = query.Where(o => o.Status == request.Status.Value);
            }

            var orders = await query
                .Select(o => new ExportOrderData
                {
                    STT = o.Id.ToString(),
                    Code = o.Code,
                    RetailChannel = o.RetailChannel!.Name,
                    Status = o.Status.ToString(),
                    CreatedAt = o.CreatedAt!.Value,
                })
                .ToListAsync(cancellationToken);

            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "Orders", "OrderExportFile.xlsx");

            string path = "exports/orders";
            string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);
            string fileName = $"OrderExport_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
            string filefolder = Path.Combine(folder, fileName);

            ExcelServices.ExportData(orders, templatePath, filefolder, 2);
            return new Response<ExportOrderResponse>()
            {
                Data = new ExportOrderResponse { File = path + "/" + fileName }
            };
        }
    }
}