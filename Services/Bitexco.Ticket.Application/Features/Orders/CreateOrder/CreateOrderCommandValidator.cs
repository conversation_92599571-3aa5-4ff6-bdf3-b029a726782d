namespace Bitexco.Ticket.Application.Features.Orders.CreateOrder;

using FluentValidation;
using System;

/// <summary>
/// Validator for the CreateOrderCommand
/// </summary>
public class CreateOrderCommandValidator : AbstractValidator<CreateOrderCommand>
{
    public CreateOrderCommandValidator()
    {
        // Basic validation rules for the order
        RuleFor(x => x.RetailChannelId)
            .NotEmpty().WithMessage("Retail channel is required");

        RuleFor(x => x.VisitDate)
            .NotEmpty().WithMessage("Visit date is required")
            .Must(BeValidFutureDate).WithMessage("Visit date must be today or a future date");
        
        // Validate the order items
        RuleFor(x => x.Items)
            .NotEmpty().WithMessage("Order must contain at least one item");

        RuleForEach(x => x.Items).SetValidator(new OrderItemValidator());
    }

    private bool BeValidFutureDate(DateOnly date)
    {
        return date >= DateOnly.FromDateTime(DateTime.Today);
    }
}

/// <summary>
/// Validator for order items
/// </summary>
public class OrderItemValidator : AbstractValidator<CreateOrderItemDto>
{
    public OrderItemValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket type is required");

        RuleFor(x => x.Quantity)
            .GreaterThan(0).WithMessage("Quantity must be greater than zero");

        RuleFor(x => x.VisitDate)
            .NotEmpty().WithMessage("Visit date is required")
            .Must(BeValidFutureDate).WithMessage("Visit date must be today or a future date");
    }

    private bool BeValidFutureDate(DateOnly date)
    {
        return date >= DateOnly.FromDateTime(DateTime.Today);
    }
}
