namespace Bitexco.Ticket.Application.Features.Orders.CreateOrder;

using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Command to create a new order
/// </summary>
public class CreateOrderCommand : IRequest<Response<CreateOrderResponseDto>>
{
    // Basic order information
    public Guid? CustomerId { get; set; }
    public Guid? AgentId { get; set; }
    public Guid? ShiftId { get; set; }
    public Guid? RetailChannelId { get; set; }
    public Guid? GroupBookingId { get; set; }
    public CustomerType? CustomerType { get; set; }
    public Guid? PosDeviceId { get; set; }
    public Guid? VoucherId { get; set; }
    public DateOnly VisitDate { get; set; }
    public PaymentTransactionDto PaymentTransaction { get; set; } = null!;
    public List<CreateOrderItemDto> Items { get; set; } = [];
}
