namespace Bitexco.Ticket.Application.Features.Orders.CreateOrder;

using System.Security.Claims;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Utils;
using Microsoft.AspNetCore.Http;

/// <summary>
/// Handler for creating a new order
/// </summary>
public class CreateOrderCommandHandler(
    IHttpContextAccessor httpContextAccessor,
    IApplicationDbContext dbContext) : IRequestHandler<CreateOrderCommand, Response<CreateOrderResponseDto>>
{
    public async Task<Response<CreateOrderResponseDto>> Handle(CreateOrderCommand request, CancellationToken cancellationToken)
    {
        // Get userId from the current user context
        var userId = httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var guidUserId = Guid.Parse(userId);

        // Fetch the ticket types to get prices
        var ticketTypeIds = request.Items.Select(i => i.TicketTypeId).Distinct().ToList();

        var ticketTypes = await dbContext.TicketTypes
            .AsNoTracking()
            .Where(tt => tt.IsActive)
            .Include(tt => tt.TicketPrices)
            .Where(tt => ticketTypeIds.Contains(tt.Id))
            .ToListAsync(cancellationToken);

        if (ticketTypes.Count != ticketTypeIds.Count)
        {
            throw new TicketTypeNotFoundException();
        }

        // Create new order
        var order = new Order
        {
            Id = Guid.NewGuid(),
            Code = CommonUtils.GenerateNewCode(8),
            CustomerId = request.CustomerId,
            CustomerType = request.CustomerType ?? CustomerType.individual,
            AgentId = request.AgentId,
            RetailChannelId = request.RetailChannelId,
            IsDebtPayment = request.PaymentTransaction?.PaymentMethod == PaymentTransactionMethod.debt,
            LastPaymentMethod = request.PaymentTransaction?.PaymentMethod,
            PosUserId = guidUserId,
            ShiftId = request.ShiftId,
            PosDeviceId = request.PosDeviceId,
            VoucherId = request.VoucherId,
            OrderDate = DateOnly.FromDateTime(DateTime.Today),
            OrderTime = TimeOnly.FromDateTime(DateTime.Now),
            Status = request.PaymentTransaction?.TransactionStatus == PaymentTransactionStatus.success
                ? OrderStatus.paid : OrderStatus.pending_payment,
            OrderItems = []
        };

        order.PaymentTransactions = request.PaymentTransaction != null
            ? new List<PaymentTransaction>
            {
                new() {
                    Id = Guid.NewGuid(),
                    Code = CommonUtils.GenerateNewCode(8),
                    OrderId = order.Id,
                    PaymentMethod = request.PaymentTransaction.PaymentMethod,
                    Amount = request.PaymentTransaction.Amount,
                    TransactionType = request.PaymentTransaction.TransactionType ?? PaymentTransactionType.income,
                    PaidAmount = request.PaymentTransaction.PaidAmount,
                    TransactionCode = request.PaymentTransaction.TransactionCode,
                    TransactionStatus = request.PaymentTransaction.TransactionStatus ?? PaymentTransactionStatus.pending,
                    ReconciliationStatus = PaymentReconciliationStatus.pending,
                    TransactionAt = DateTime.UtcNow,
                    RetailChannelId = request.RetailChannelId,
                    PaidAt = request.PaymentTransaction.PaidAt ?? null
                }
            }
            : [];

        // Create order items and tickets
        decimal totalAmount = 0;

        foreach (var itemDto in request.Items)
        {
            var ticketType = ticketTypes.FirstOrDefault(tt => tt.Id == itemDto.TicketTypeId)
                ?? throw new TicketTypeNotFoundException(itemDto.TicketTypeId);

            // Find the appropriate price for this ticket type
            var ticketPrice = (ticketType.TicketPrices?
                .OrderByDescending(tp => tp.EffectiveDateFrom)
                .FirstOrDefault(tp => tp.EffectiveDateFrom <= DateOnly.FromDateTime(DateTime.Today)))
                ?? throw new TicketTypeNotFoundException(itemDto.TicketTypeId);

            var unitPrice = ticketPrice.Price;
            var subTotal = unitPrice * itemDto.Quantity;
            totalAmount += subTotal;

            // Create order item
            var orderItem = new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order.Id,
                TicketTypeId = itemDto.TicketTypeId,
                TicketTypeName = ticketType.Name,
                Quantity = itemDto.Quantity,
                UnitPrice = unitPrice,
                SubTotal = subTotal,
                VisitDate = itemDto.VisitDate,
                VisitTime = itemDto.VisitTime,
                Tickets = []
            };

            //get ticket type expiration
            DateTime? ticketExpiration = null;
            if (ticketType.TicketExpirationAfterDurations.HasValue)
            {
                if (ticketType.TicketExpirationDurationUnit == TicketExpirationDurationUnit.days)
                {
                    ticketExpiration = DateTime.SpecifyKind(
                        itemDto.VisitDate.AddDays(ticketType.TicketExpirationAfterDurations.Value)
                            .ToDateTime(itemDto.VisitTime),
                        DateTimeKind.Utc);
                }
                else if (ticketType.TicketExpirationDurationUnit == TicketExpirationDurationUnit.months)
                {
                    ticketExpiration = DateTime.SpecifyKind(
                        itemDto.VisitDate.AddMonths(ticketType.TicketExpirationAfterDurations.Value)
                            .ToDateTime(itemDto.VisitTime),
                        DateTimeKind.Utc);
                }
                else if (ticketType.TicketExpirationDurationUnit == TicketExpirationDurationUnit.years)
                {
                    ticketExpiration = DateTime.SpecifyKind(
                        itemDto.VisitDate.AddYears(ticketType.TicketExpirationAfterDurations.Value)
                            .ToDateTime(itemDto.VisitTime)
                        , DateTimeKind.Utc);
                }
            }

            // Generate individual tickets for this item
            for (int i = 0; i < itemDto.Quantity; i++)
            {
                var ticket = new Ticket
                {
                    Id = Guid.NewGuid(),
                    OrderItemId = orderItem.Id,
                    TicketTypeId = itemDto.TicketTypeId,
                    Code = $"TKT-{CommonUtils.GenerateNewCode(8)}",
                    Status = TicketStatus.sold,
                    SoldAt = DateTime.UtcNow,
                    PlannedVisitDate = itemDto.VisitDate,
                    PlannedVisitTime = itemDto.VisitTime,
                    CustomerId = request.CustomerId,
                    ExpiredAt = ticketExpiration,
                    ValidatedByUserId = guidUserId,
                    ValidatedAtDeviceId = request.PosDeviceId,
                    TicketHistories = [
                        new TicketHistory
                        {
                            Id = Guid.NewGuid(),
                            TicketId = Guid.NewGuid(), // This will be set later when the ticket is saved
                            FromStatus = null, // Initial status is null
                            ToStatus = TicketStatus.sold,
                            ChangedAt = DateTime.UtcNow,
                            ChangedByUserId = guidUserId,
                            ChangedAtDeviceId = request.PosDeviceId,
                            Location = "POS" // Assuming this is a POS order
                        }
                    ]
                };

                orderItem.Tickets.Add(ticket);
            }

            order.OrderItems.Add(orderItem);
        }

        // Update order totals
        order.TotalAmount = totalAmount;
        order.DiscountAmount = 0; // Discount calculation would be done here if applicable
        order.CouponAmount = 0; // Coupon amount would be applied if a voucher is used
        order.FinalAmount = totalAmount - order.DiscountAmount;

        // Create agent debt if order is debt payment
        if (order.IsDebtPayment && order.AgentId.HasValue)
        {
            await dbContext.AgentDebts.AddAsync(new AgentDebt
            {
                Id = Guid.NewGuid(),
                AgentId = order.AgentId.Value,
                OrderId = order.Id,
                OriginalAmount = order.FinalAmount,
                PaidAmount = 0,
                Status = AgentDebtStatus.outstanding,
            }, cancellationToken);
        }

        //create invoice if needed
        if(order.Status == OrderStatus.paid)
        {
            var invoice = new Invoice
            {
                Id = Guid.NewGuid(),
                Code = CommonUtils.GenerateNewCode(8),
                OrderId = order.Id,
                CustomerId = order.CustomerId,
                IssueDate = DateOnly.FromDateTime(DateTime.UtcNow),
                TotalAmount = order.FinalAmount,
                Status = InvoiceStatus.draft,
                SendMailStatus = SendMailStatus.unsent,
            };

            await dbContext.Invoices.AddAsync(invoice, cancellationToken);
        }

        // Save the order to the database
        await dbContext.Orders.AddAsync(order, cancellationToken);
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityCreatedException("Order");
        }

        // Map and return the response
        var response = order.Adapt<CreateOrderResponseDto>();
        response.OrderItems.ForEach(item =>
        {
            var ticketType = ticketTypes.FirstOrDefault(tt => tt.Id == item.TicketTypeId);
            
            item.Tickets?.ForEach(ticket =>
            {
                ticket.TicketName = ticketType?.Name;
            });
        });

        return new Response<CreateOrderResponseDto>(response);
    }
}
