namespace Bitexco.Ticket.Application.Features.Orders.CreateOrder;

using Bitexco.Ticket.Application.Dtos;

/// <summary>
/// Response DTO for order creation
/// </summary>
public record CreateOrderResponseDto : OrderResponseDto
{
    public CustomerDto? Customer { get; set; }
    public AgentDto? Agent { get; set; }
    public RetailChannelDto? RetailChannel { get; set; }
    public List<CreateOrderItemDto> OrderItems { get; set; } = [];
}

public record CreateOrderItemDto : OrderItemResponseDto
{
    public List<TicketResponseDto>? Tickets { get; set; } = [];
}