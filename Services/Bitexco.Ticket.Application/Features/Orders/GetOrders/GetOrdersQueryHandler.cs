namespace Bitexco.Ticket.Application.Features.Orders.GetOrders;

using Bitexco.Ticket.Application.Data;
using BuildingBlocks.Abstractions;
using MediatR;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// Handler for getting paginated list of orders
/// </summary>
public class GetOrdersQueryHandler : IRequestHandler<GetOrdersQuery, PaginationResponse<OrderSummaryDto>>
{
    private readonly IOrderRepository _orderRepository;

    public GetOrdersQueryHandler(IOrderRepository orderRepository)
    {
        _orderRepository = orderRepository;
    }

    public async Task<PaginationResponse<OrderSummaryDto>> Handle(GetOrdersQuery request, CancellationToken cancellationToken)
    {
        // Get paginated orders with applied filters
        var (orders, totalCount) = await _orderRepository.GetOrdersAsync(
            request.PageIndex,
            request.PageSize,
            request.StartDate,
            request.EndDate,
            request.CustomerId,
            request.AgentId,
            request.Status,
            cancellationToken);

        // Map orders to DTOs
        var orderDtos = orders.Select(order => new OrderSummaryDto
        {
            Id = order.Id,
            OrderDate = order.OrderDate,
            OrderTime = order.OrderTime,
            Code = order.Code,
            CustomerId = order.CustomerId,
            AgentId = order.AgentId,
            RetailChannelId = order.RetailChannelId,
            PosUserId = order.PosUserId,
            ShiftId = order.ShiftId,
            PosDeviceId = order.PosDeviceId,
            IsDebtPayment = order.IsDebtPayment,
            LastPaymentMethod = order.LastPaymentMethod,
            CustomerType = order.CustomerType,
            CustomerName = order.Customer?.FullName,
            AgentName = order.Agent?.Name,
            RetailChannel = order.RetailChannel?.Name,
            TotalItems = order.OrderItems?.Sum(oi => oi.Quantity) ?? 0,
            TotalAmount = order.TotalAmount,
            DiscountAmount = order.DiscountAmount,
            FinalAmount = order.FinalAmount,
            Status = order.Status
        }).ToList();

        // Create and return the response
        var response = new PaginationResponse<OrderSummaryDto>
        (
            request.PageIndex,
            request.PageSize,
            totalCount,
            orderDtos
        );

        return response;
    }
}
