namespace Bitexco.Ticket.Application.Features.Orders.GetOrders;

using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Query to get paginated list of orders with optional filtering
/// </summary>
public record GetOrdersQuery
    : IRequest<PaginationResponse<OrderSummaryDto>>, IPaginationRequest
{
    // Pagination parameters
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 10;

    // Filter parameters
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public Guid? CustomerId { get; set; }
    public Guid? AgentId { get; set; }
    public OrderStatus? Status { get; set; }
}
