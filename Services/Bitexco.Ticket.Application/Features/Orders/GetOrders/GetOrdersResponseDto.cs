namespace Bitexco.Ticket.Application.Features.Orders.GetOrders;

using Bitexco.Ticket.Application.Dtos;

public record OrderSummaryDto : OrderResponseDto
{
    public string? CustomerName { get; set; } = string.Empty;
    public string? AgentName { get; set; } = string.Empty;
    public string? RetailChannel { get; set; }
    public int TotalItems { get; set; }
    public string StatusDisplay => Status.ToString();
}
