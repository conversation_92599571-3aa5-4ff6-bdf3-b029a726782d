using BuildingBlocks.Exceptions;

namespace Bitexco.Ticket.Application.Features.Orders.CancelOrder;

/// <summary>
/// Handler for canceling an order
/// </summary>
public class CancelOrderCommandHandler(
    IOrderRepository orderRepository) : IRequestHandler<CancelOrderCommand, Response<CancelOrderResponseDto>>
{
    private readonly IOrderRepository _orderRepository = orderRepository;

    public async Task<Response<CancelOrderResponseDto>> Handle(CancelOrderCommand request, CancellationToken cancellationToken)
    {
        // Get the order
        var order = await _orderRepository.GetOrderByIdAsync(request.OrderId, cancellationToken)
        ?? throw new OrderNotFoundException(request.OrderId);

        // Check if the order can be cancelled (not already cancelled or refunded)
        if (order.Status == OrderStatus.cancelled
            || order.Status == OrderStatus.refunded)
        {
            throw new BadRequestException($"Cannot cancel order with status {order.Status}");
        }

        // Check if user has approval rights for this operation
        bool userHasApprovalRights = await _orderRepository.UserCanApproveOrderActionAsync(
            request.OrderId, 
            request.RequestingUserId, 
            cancellationToken);

        if (!userHasApprovalRights)
        {
            throw new ForbiddenException("You don't have permission to cancel this order");
        }

        // Cancel the order
        bool result = await _orderRepository.CancelOrderAsync(request.OrderId, cancellationToken);
        if(!result)
        {
            throw new DbUpdateException("Failed to cancel order. Please try again later.");
        }

        var response = new CancelOrderResponseDto
        {
            Succeeded = result,
            Message = result ? "Order cancelled successfully" : "Failed to cancel order",
            ProcessedAt = DateTime.UtcNow
        };
        
        return new Response<CancelOrderResponseDto>(response);
    }
}
