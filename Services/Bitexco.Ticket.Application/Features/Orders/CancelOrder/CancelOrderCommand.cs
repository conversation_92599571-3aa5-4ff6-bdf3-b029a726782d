namespace Bitexco.Ticket.Application.Features.Orders.CancelOrder;

/// <summary>
/// Command to cancel an order
/// </summary>
public class CancelOrderCommand : IRequest<Response<CancelOrderResponseDto>>
{
    /// <summary>
    /// ID of the order to cancel
    /// </summary>
    public Guid OrderId { get; set; }
    
    /// <summary>
    /// ID of the user requesting the cancellation (for approval check)
    /// </summary>
    public Guid RequestingUserId { get; set; }
}
