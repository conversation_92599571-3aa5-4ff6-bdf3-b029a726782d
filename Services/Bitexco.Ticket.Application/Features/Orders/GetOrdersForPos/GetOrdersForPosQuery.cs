using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Tickets.GetTicketsForPos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Orders.GetOrdersForPos;

/// <summary>
/// Query to retrieve tickets
/// </summary>
public record GetOrdersForPosQuery : IRequest<Response<List<OrdersForPosResponse>>>
{
    /// <summary>
    /// The sole date of the order to retrieve
    /// </summary>
    public DateOnly OrderDate { get; init; }
    public CustomerType? CustomerType { get; init; }
}

/// <summary>
/// Handler for retrieving tickets for POS
/// </summary>
public class GetOrdersForPosQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetOrdersForPosQuery, Response<List<OrdersForPosResponse>>>
{
    public async Task<Response<List<OrdersForPosResponse>>> Handle(GetOrdersForPosQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Orders
            .AsNoTracking()
            .Include(o => o.OrderItems!).ThenInclude(oi => oi.TicketType)
            .Include(o => o.Agent)
            .Include(o => o.PaymentTransactions)
            .Where(o => o.OrderDate == request.OrderDate)
            .AsQueryable();
        
        if (request.CustomerType.HasValue)
        {
            query = query.Where(o => o.CustomerType == request.CustomerType.Value);
        }

        var ordersDatas = await query.OrderBy(o => o.CreatedAt).ToListAsync(cancellationToken);

        var ordersForPosResponse = ordersDatas.Select(order => new OrdersForPosResponse
        {
            Id = order.Id,
            Code = order.Code,
            CustomerId = order.CustomerId,
            AgentId = order.AgentId,
            RetailChannelId = order.RetailChannelId,
            PosUserId = order.PosUserId,
            ShiftId = order.ShiftId,
            PosDeviceId = order.PosDeviceId,
            VoucherId = order.VoucherId,
            OrderDate = order.OrderDate,
            OrderTime = order.OrderTime,
            TotalAmount = order.TotalAmount,
            CouponAmount = order.CouponAmount,
            CustomerType = order.CustomerType,
            DiscountAmount = order.DiscountAmount,
            IsDebtPayment = order.IsDebtPayment,
            LastPaymentMethod = order.LastPaymentMethod,
            DepositAmount = order.DepositAmount,
            FinalAmount = order.FinalAmount,
            RefundStatus = order.RefundStatus,
            RefundReason = order.RefundReason,
            CreatedAt = order.CreatedAt,
            UpdatedAt = order.UpdatedAt,
            Status = order.Status,
            OrderItems = order.OrderItems?.Select(oi => new OrderItemForPosDto
            {
                Id = oi.Id,
                Quantity = oi.Quantity,
                UnitPrice = oi.UnitPrice,
                SubTotal = oi.SubTotal,
                VisitDate = oi.VisitDate,
                VisitTime = oi.VisitTime,
                TicketType = oi.TicketType.Adapt<TicketTypeForPosDto>()
            }).ToList(),
            Agent = order.Agent.Adapt<AgentResponseDto>()
        }).ToList();

        return new Response<List<OrdersForPosResponse>>(ordersForPosResponse);
    }
}
