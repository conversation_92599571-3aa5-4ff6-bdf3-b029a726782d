using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Tickets.GetTicketsForPos;

namespace Bitexco.Ticket.Application.Features.Orders.GetOrdersForPos;

public record OrdersForPosResponse : OrderResponseDto
{
    public DateTime? OrderAt => OrderDate?.ToDateTime(OrderTime ?? TimeOnly.MinValue, DateTimeKind.Utc);
    public List<OrderItemForPosDto>? OrderItems { get; set; }
    public AgentResponseDto? Agent { get; set; }
}

public record OrderItemForPosDto
{
    public Guid Id { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal SubTotal { get; set; }
    public DateOnly VisitDate { get; set; }
    public TimeOnly VisitTime { get; set; }

    public TicketTypeForPosDto? TicketType { get; set; }
}
