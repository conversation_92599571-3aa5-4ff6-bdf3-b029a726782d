using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketPrices.GetCurrentTicketPrices;

// This query is used to retrieve the current ticket prices from the system.
// It implements IRequest from MediatR, which allows it to be handled by a MediatR handler.
// The response is a list of TicketPriceResponseDto objects wrapped in a Response object.
public class GetCurrentTicketPricesQueryHandler(IApplicationDbContext context) : IRequestHandler<GetCurrentTicketPricesQuery, Response<List<TicketPriceResponseDto>>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<Response<List<TicketPriceResponseDto>>> Handle(GetCurrentTicketPricesQuery request, CancellationToken cancellationToken)
    {
        var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);
        var ticketPrices = await _context.TicketPrices
            .Where(tp => tp.EffectiveDateFrom <= currentDate && (tp.EffectiveDateTo == null || tp.EffectiveDateTo >= currentDate))
            .ProjectToType<TicketPriceResponseDto>()
            .ToListAsync(cancellationToken);

        return new Response<List<TicketPriceResponseDto>>(ticketPrices);
    }
}