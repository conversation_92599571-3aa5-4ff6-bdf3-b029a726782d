using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.CommissionOrderHistories.GetCommissionOrderHistories;

/// <summary>
/// Response DTO cho danh sách Commission Order History với các thông tin cần thiết
/// </summary>
public record CustomCommissionOrderHistoryResponseDto
{
    public Guid Id { get; set; } // Id của CommissionOrderHistory
    public CommissionPaymentStatus Status { get; set; } // Status
    public string StatusDisplay => Status.ToString();
    public string AgentCode { get; set; } = null!; // Mã agent
    public string AgentName { get; set; } = null!; // Tên agent
    public Guid OrderId { get; set; } // Id order
    public string OrderCode { get; set; } = null!; // Mã đơn hàng
    public decimal OrderFinalAmount { get; set; } // Gi<PERSON> đơn hàng (FinalAmount)
    public decimal CommissionRatePercentage { get; set; } // CommissionRatePercentage
    public decimal CommissionAmount { get; set; } // CommissionAmount
    public DateTime? CreatedAt { get; set; } // Ngày tạo
    public DateTime? PaymentDate { get; set; } // Ngày thanh toán
    public AgentType AgentType { get; set; } // Type từ bảng Agent
    public string AgentTypeDisplay => AgentType.ToString();
}
