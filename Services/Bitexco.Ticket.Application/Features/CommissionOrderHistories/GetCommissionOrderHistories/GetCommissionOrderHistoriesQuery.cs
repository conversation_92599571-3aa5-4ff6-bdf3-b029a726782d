using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.CommissionOrderHistories.GetCommissionOrderHistories;

/// <summary>
/// Query để lấy danh sách Commission Order History có phân trang và filter
/// </summary>
public record GetCommissionOrderHistoriesQuery
    : IRequest<PaginationResponse<CustomCommissionOrderHistoryResponseDto>>, IPaginationRequest, ISortRequest
{
    // Pagination parameters
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 100;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool? IsDescending { get; set; } = true;

    // Filter parameters
    public string? SearchTerm { get; set; } // Mã đơn hàng hoặc mã cộng tác viên
    public CommissionPaymentStatus? Status { get; set; } // Status filter
    public DateOnly? CreatedDate { get; set; } // Ngày tạo
    public AgentType? AgentType { get; set; } // Agent type filter
}

/// <summary>
/// Handler để lấy danh sách Commission Order History với phân trang
/// </summary>
public class GetCommissionOrderHistoriesQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetCommissionOrderHistoriesQuery, PaginationResponse<CustomCommissionOrderHistoryResponseDto>>
{
    public async Task<PaginationResponse<CustomCommissionOrderHistoryResponseDto>> Handle(
        GetCommissionOrderHistoriesQuery request, 
        CancellationToken cancellationToken)
    {
        // Build query with includes
        var query = context.CommissionOrderHistories
            .AsNoTracking()
            .Include(ch => ch.Agent)
            .Include(ch => ch.Order)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query.Where(ch => 
                ch.Order.Code.ToLower().Contains(searchTerm) || // Mã đơn hàng
                ch.Agent.Code.ToLower().Contains(searchTerm)); // Mã cộng tác viên
        }

        if (request.Status.HasValue)
        {
            query = query.Where(ch => ch.Status == request.Status.Value);
        }

        if (request.CreatedDate.HasValue)
        {
            var startOfDay = request.CreatedDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
            var endOfDay = request.CreatedDate.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);
            query = query.Where(ch => ch.CreatedAt >= startOfDay && ch.CreatedAt <= endOfDay);
        }

        if (request.AgentType.HasValue)
        {
            query = query.Where(ch => ch.Agent.Type == request.AgentType.Value);
        }

        // Get total count for pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = request.SortBy?.ToLower() switch
        {
            "agentname" => request.IsDescending == true ? 
                query.OrderByDescending(ch => ch.Agent.Name) : 
                query.OrderBy(ch => ch.Agent.Name),
            "status" => request.IsDescending == true ? 
                query.OrderByDescending(ch => ch.Status) : 
                query.OrderBy(ch => ch.Status),
            "orderfinalamount" => request.IsDescending == true ? 
                query.OrderByDescending(ch => ch.Order.FinalAmount) : 
                query.OrderBy(ch => ch.Order.FinalAmount),
            "commissionamount" => request.IsDescending == true ? 
                query.OrderByDescending(ch => ch.CommissionAmount) : 
                query.OrderBy(ch => ch.CommissionAmount),
            _ => request.IsDescending == true ? 
                query.OrderByDescending(ch => ch.CreatedAt) : 
                query.OrderBy(ch => ch.CreatedAt)
        };

        // Apply pagination and ordering
        var commissionHistories = await query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Map to DTOs
        var responseDtos = commissionHistories.Select(ch => new CustomCommissionOrderHistoryResponseDto
        {
            Id = ch.Id,
            Status = ch.Status,
            AgentCode = ch.Agent.Code,
            AgentName = ch.Agent.Name,
            OrderId = ch.OrderId,
            OrderCode = ch.Order.Code,
            OrderFinalAmount = ch.Order.FinalAmount,
            CommissionRatePercentage = ch.CommissionRatePercentage,
            CommissionAmount = ch.CommissionAmount,
            CreatedAt = ch.CreatedAt,
            PaymentDate = ch.PaymentDate,
            AgentType = ch.Agent.Type
        }).ToList();

        // Create and return pagination response
        var response = new PaginationResponse<CustomCommissionOrderHistoryResponseDto>
        (
            request.PageIndex,
            request.PageSize,
            totalCount,
            responseDtos
        );

        return response;
    }
}
