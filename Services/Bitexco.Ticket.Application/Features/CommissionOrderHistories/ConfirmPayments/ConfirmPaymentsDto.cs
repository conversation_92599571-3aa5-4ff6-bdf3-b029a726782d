using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.CommissionOrderHistories.ConfirmPayments;

/// <summary>
/// Request DTO cho việc xác nhận thanh toán cho nhiều commission order history cùng lúc
/// </summary>
public record ConfirmPaymentsRequestDto
{
    /// <summary>
    /// Danh sách ID của các CommissionOrderHistory cần xác nhận thanh toán
    /// </summary>
    public List<Guid> CommissionOrderHistoryIds { get; set; } = [];
    
    /// <summary>
    /// Ngày thanh toán
    /// </summary>
    public DateTime PaymentDate { get; set; }
    
    /// <summary>
    /// Phương thức thanh toán
    /// </summary>
    public CommissionPaymentMethod PaymentMethod { get; set; }
    
    /// <summary>
    /// URL file bằng chứng thanh toán (lưu string URL)
    /// </summary>
    public string? PaymentProofUrl { get; set; }
}

/// <summary>
/// Response DTO cho API xác nhận thanh toán
/// </summary>
public record ConfirmPaymentsResponseDto
{
    public bool Status { get; set; }
}
