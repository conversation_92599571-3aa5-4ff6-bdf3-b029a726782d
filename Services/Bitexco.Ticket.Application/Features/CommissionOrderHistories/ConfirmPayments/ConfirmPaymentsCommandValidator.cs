using FluentValidation;

namespace Bitexco.Ticket.Application.Features.CommissionOrderHistories.ConfirmPayments;

/// <summary>
/// Validator cho ConfirmPaymentsCommand
/// </summary>
public class ConfirmPaymentsCommandValidator : AbstractValidator<ConfirmPaymentsCommand>
{
    public ConfirmPaymentsCommandValidator()
    {
        RuleFor(x => x.Request.CommissionOrderHistoryIds)
            .NotEmpty().WithMessage("Commission Order History IDs list cannot be empty")
            .Must(list => list.Count > 0).WithMessage("At least one Commission Order History ID is required")
            .Must(list => list.All(id => id != Guid.Empty)).WithMessage("All Commission Order History IDs must be valid GUIDs");

        RuleFor(x => x.Request.PaymentDate)
            .NotEmpty().WithMessage("Payment date is required")
            .LessThanOrEqualTo(DateTime.Now.AddDays(1)).WithMessage("Payment date cannot be in the future");

        RuleFor(x => x.Request.PaymentMethod)
            .IsInEnum().WithMessage("Invalid payment method");

        RuleFor(x => x.Request.PaymentProofUrl)
            .MaximumLength(500).WithMessage("Payment proof URL cannot exceed 500 characters");
    }
}
