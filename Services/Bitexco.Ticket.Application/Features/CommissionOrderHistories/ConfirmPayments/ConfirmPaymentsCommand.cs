using Bitexco.Ticket.Domain.Enums;
using System.Text;

namespace Bitexco.Ticket.Application.Features.CommissionOrderHistories.ConfirmPayments;

/// <summary>
/// Command để xác nhận thanh toán cho nhiều commission order history cùng lúc
/// </summary>
public record ConfirmPaymentsCommand : IRequest<Response<ConfirmPaymentsResponseDto>>
{
    public ConfirmPaymentsRequestDto Request { get; set; } = null!;
}

/// <summary>
/// Handler để xử lý xác nhận thanh toán cho nhiều commission order history
/// </summary>
public class ConfirmPaymentsCommandHandler(IApplicationDbContext context) 
    : IRequestHandler<ConfirmPaymentsCommand, Response<ConfirmPaymentsResponseDto>>
{
    public async Task<Response<ConfirmPaymentsResponseDto>> Handle(ConfirmPaymentsCommand request, CancellationToken cancellationToken)
    {
        var confirmRequest = request.Request;
        
        // Validate that all CommissionOrderHistory records exist
        var allCommissionHistories = await context.CommissionOrderHistories
            .AsTracking()
            .Include(ch => ch.Agent)
            .Include(ch => ch.Order)
                .ThenInclude(o => o.Customer)
            .Where(ch => confirmRequest.CommissionOrderHistoryIds.Contains(ch.Id))
            .ToListAsync(cancellationToken);

        // Check if all requested records were found
        if (allCommissionHistories.Count != confirmRequest.CommissionOrderHistoryIds.Count)
        {
            var foundIds = allCommissionHistories.Select(ch => ch.Id).ToList();
            var missingIds = confirmRequest.CommissionOrderHistoryIds.Except(foundIds).ToList();
            throw new CommissionOrderHistoryNotFoundException(string.Join(", ", missingIds));
        }

        // Filter only pending commission histories (chưa thanh toán)
        var pendingCommissionHistories = allCommissionHistories
            .Where(ch => ch.Status == CommissionPaymentStatus.pending)
            .ToList();

        // If no pending records to process
        if (pendingCommissionHistories.Count == 0)
        {
            throw new NoPendingCommissionOrderHistoryException();
        }

        // Check if there are any non-pending records
        var nonPendingRecords = allCommissionHistories
            .Where(ch => ch.Status != CommissionPaymentStatus.pending)
            .ToList();

        if (nonPendingRecords.Count != 0)
        {
            var nonPendingDetails = nonPendingRecords.Select(ch => 
                $"ID: {ch.Id}, Agent: {ch.Agent.Code}, Status: {ch.Status}").ToList();
            throw new CommissionOrderHistoryAlreadyPaidException(string.Join("; ", nonPendingDetails));
        }

        // Update each pending commission history with payment information
        foreach (var commissionHistory in pendingCommissionHistories)
        {
            commissionHistory.PaidAmount = commissionHistory.CommissionAmount; 
            commissionHistory.PaymentDate = confirmRequest.PaymentDate;
            commissionHistory.PaymentMethod = confirmRequest.PaymentMethod;
            commissionHistory.Status = CommissionPaymentStatus.paid;
            commissionHistory.PaymentProofUrl = confirmRequest.PaymentProofUrl;
        }

        // Save changes
        context.CommissionOrderHistories.UpdateRange(pendingCommissionHistories);
        var result = await context.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new CommissionOrderHistoryPaymentSaveFailedException();
        }

        // Return success response
        return new Response<ConfirmPaymentsResponseDto>(new ConfirmPaymentsResponseDto { Status = true });
    }
}
