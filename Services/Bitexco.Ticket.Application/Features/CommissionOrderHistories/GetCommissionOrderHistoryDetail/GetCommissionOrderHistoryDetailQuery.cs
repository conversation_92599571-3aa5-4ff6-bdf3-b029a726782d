namespace Bitexco.Ticket.Application.Features.CommissionOrderHistories.GetCommissionOrderHistoryDetail;

/// <summary>
/// Query để lấy chi tiết Commission Order History
/// </summary>
public record GetCommissionOrderHistoryDetailQuery : IRequest<Response<CommissionOrderHistoryDetailResponseDto>>
{
    public Guid Id { get; set; }
}

/// <summary>
/// Handler để lấy chi tiết Commission Order History
/// </summary>
public class GetCommissionOrderHistoryDetailQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetCommissionOrderHistoryDetailQuery, Response<CommissionOrderHistoryDetailResponseDto>>
{
    public async Task<Response<CommissionOrderHistoryDetailResponseDto>> Handle(
        GetCommissionOrderHistoryDetailQuery request, 
        CancellationToken cancellationToken)
    {
        // Get commission order history with all related data
        var commissionHistory = await context.CommissionOrderHistories
            .AsNoTracking()
            .Include(ch => ch.Agent)
            .Include(ch => ch.Order)
                .ThenInclude(o => o.RetailChannel)
            .Include(ch => ch.Order)
                .ThenInclude(o => o.OrderItems)
            .Include(ch => ch.Order)
                .ThenInclude(o => o.Customer)
            .Include(ch => ch.Order)
                .ThenInclude(o => o.PosUser)
            .FirstOrDefaultAsync(ch => ch.Id == request.Id, cancellationToken)
        ?? throw new CommissionOrderHistoryNotFoundException(request.Id);

        return new Response<CommissionOrderHistoryDetailResponseDto>(commissionHistory.Adapt<CommissionOrderHistoryDetailResponseDto>());
    }
}
