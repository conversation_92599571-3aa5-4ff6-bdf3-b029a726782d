using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.CommissionOrderHistories.GetCommissionOrderHistoryDetail;

/// <summary>
/// Response DTO chi tiết cho Commission Order History
/// </summary>
public record CommissionOrderHistoryDetailResponseDto : CommissionOrderHistoryResponseDto
{
    // Thông tin đơn hàng
    public CommissionOrderInfo Order { get; set; } = null!;

    // Thông tin đại lý
    public CommissionAgentInfo Agent { get; set; } = null!;
}

/// <summary>
/// Thông tin đơn hàng trong commission order history
/// </summary>
public record CommissionOrderInfo : OrderResponseDto
{
    public CommissionRetailChannelInfo? RetailChannel { get; set; }
    public CommissionPosUserInfo? PosUser { get; set; }
    public List<OrderItemResponseDto> OrderItems { get; set; } = [];
    public CustomerResponseDto? Customer { get; set; }

}

/// <summary>
/// Thông tin đại lý trong commission order history
/// </summary>
public record CommissionAgentInfo
{
    public string? Code { get; set; }
    public string? Name { get; set; }
}

public record CommissionPosUserInfo
{
    public string? Code { get; set; }
    public string? Name { get; set; }
}

public record CommissionRetailChannelInfo
{
    public string? Code { get; set; }
    public string? Name { get; set; }
}
