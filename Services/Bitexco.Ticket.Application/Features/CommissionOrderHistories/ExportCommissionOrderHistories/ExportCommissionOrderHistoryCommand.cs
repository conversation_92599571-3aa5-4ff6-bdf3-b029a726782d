﻿using BuildingBlocks.FileServices;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bitexco.Ticket.Application.Features.CommissionOrderHistories.ExportCommissionOrderHistories
{
    public class ExportCommissionOrderHistoryCommand : ICommand<Response<ExportCommissionOrderHistoryResponse>>
    {
        // Filter parameters
        public string? SearchTerm { get; set; } // Mã đơn hàng hoặc mã cộng tác viên

        public CommissionPaymentStatus? Status { get; set; } // Status filter
        public DateOnly? CreatedDate { get; set; } // Ngày tạo
        public AgentType? AgentType { get; set; } // Agent type filter
    }

    public class ExportSellerCommissionOrderHistoriesData
    {
        [Display(Name = "STT")]
        public int STT { get; set; } // Số thứ tự
        [Display(Name = "Mã cộng tác viên")]
        public string? AgentCode { get; set; } // Mã cộng tác viên
        [Display(Name = "Tên cộng tác viên")]
        public string? AgentName { get; set; } // Tên cộng tác viên
        [Display(Name = "Mã đơn hàng")]
        public string? OrderCode { get; set; } // Mã đơn hàng
        [Display(Name = "Giá trị đơn hàng")]
        public decimal? OrderAmount { get; set; } // Gía trị đơn hàng
        [Display(Name = "Tỷ lệ hoa hồng (%)")]
        public decimal CommissionRatePercentage { get; set; } // Tỷ lệ hoa hồng (%)
        [Display(Name = "Giá trị hoa hồng")]
        public decimal CommissionAmount { get; set; } // Giá trị hoa hồng
        [Display(Name = "Trạng thái")]
        public string Status { get; set; } = null!; // Trạng thái
    }

    public class ExportAgentCommissionOrderHistoriesData
    {
        [Display(Name = "STT")]
        public int STT { get; set; } // Số thứ tự
        [Display(Name = "Mã đại lý")]
        public string? AgentCode { get; set; } // Mã đại lý
        [Display(Name = "Tên đại lý")]
        public string? AgentName { get; set; } // Tên đại lý
        [Display(Name = "Mã đơn hàng")]
        public string? OrderCode { get; set; } // Mã đơn hàng
        [Display(Name = "Giá trị đơn hàng")]
        public decimal? OrderAmount { get; set; } // Gía trị đơn hàng
        [Display(Name = "Tỷ lệ hoa hồng (%)")]
        public decimal CommissionRatePercentage { get; set; } // Tỷ lệ hoa hồng (%)
        [Display(Name = "Giá trị hoa hồng")]
        public decimal CommissionAmount { get; set; } // Giá trị hoa hồng
        [Display(Name = "Trạng thái")]
        public string Status { get; set; } = null!; // Trạng thái
    }

    public class ExportCommissionOrderHistoryResponse
    {
        public string? File { get; set; } // Đường dẫn đến file xuất
    }

    public class ExportCommissionOrderHistoryCommandValidator : AbstractValidator<ExportCommissionOrderHistoryCommand>
    {
        public ExportCommissionOrderHistoryCommandValidator()
        {
            RuleFor(x => x.SearchTerm).MaximumLength(100).WithMessage("Search term cannot exceed 100 characters.");
        }
    }

    public class ExportCommissionOrderHistoryCommandHandler(IApplicationDbContext applicationDbContext)
        : ICommandHandler<ExportCommissionOrderHistoryCommand, Response<ExportCommissionOrderHistoryResponse>>
    {
        private readonly IApplicationDbContext _applicationDbContext = applicationDbContext;

        public async Task<Response<ExportCommissionOrderHistoryResponse>> Handle(ExportCommissionOrderHistoryCommand request, CancellationToken cancellationToken)
        {
            // Build query with includes
            var query = _applicationDbContext.CommissionOrderHistories
                .AsNoTracking()
                .Include(ch => ch.Agent)
                .Include(ch => ch.Order)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.Trim().ToLower();
                query = query.Where(ch =>
                    ch.Order.Code.ToLower().Contains(searchTerm) || // Mã đơn hàng
                    ch.Agent.Code.ToLower().Contains(searchTerm)); // Mã cộng tác viên
            }

            if (request.Status.HasValue)
            {
                query = query.Where(ch => ch.Status == request.Status.Value);
            }

            if (request.CreatedDate.HasValue)
            {
                var startOfDay = request.CreatedDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
                var endOfDay = request.CreatedDate.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);
                query = query.Where(ch => ch.CreatedAt >= startOfDay && ch.CreatedAt <= endOfDay);
            }

            if (request.AgentType.HasValue)
            {
                query = query.Where(ch => ch.Agent.Type == request.AgentType.Value);
            }

            var commissionHistories = await query.ToListAsync(cancellationToken);
            string fileName = string.Empty;
            string path = "exports/commissions";
            if (request.AgentType == AgentType.collaborator)
            {
                var data = commissionHistories.Select((ch, index) => new ExportSellerCommissionOrderHistoriesData
                {
                    STT = index + 1,
                    AgentCode = ch.Agent.Code,
                    AgentName = ch.Agent.Name,
                    OrderCode = ch.Order.Code,
                    CommissionRatePercentage = ch.CommissionRatePercentage,
                    CommissionAmount = ch.CommissionAmount,
                    OrderAmount = ch.Order.FinalAmount,
                    Status = CommissionPaymentStatusDescription(ch.Status)
                }).ToList();
                string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "Customers", "SellerExportFile.xlsx");

                string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);
                if (!Directory.Exists(folder))
                    Directory.CreateDirectory(folder);
                fileName = $"SellerExport_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                string filefolder = Path.Combine(folder, fileName);

                ExcelServices.ExportData(data, templatePath, filefolder, 2);
            }
            else
            {
                var data = commissionHistories.Select((ch, index) => new ExportAgentCommissionOrderHistoriesData
                {
                    STT = index + 1,
                    AgentCode = ch.Agent.Code,
                    AgentName = ch.Agent.Name,
                    OrderCode = ch.Order.Code,
                    CommissionRatePercentage = ch.CommissionRatePercentage,
                    CommissionAmount = ch.CommissionAmount,
                    OrderAmount = ch.Order.FinalAmount,
                    Status = CommissionPaymentStatusDescription(ch.Status)
                }).ToList();
                string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "Customers", "AgentExportFile.xlsx");

                string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);
                if (!Directory.Exists(folder))
                    Directory.CreateDirectory(folder);
                fileName = $"AgentsExport_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                string filefolder = Path.Combine(folder, fileName);

                ExcelServices.ExportData(data, templatePath, filefolder, 2);
            }
            return new Response<ExportCommissionOrderHistoryResponse>
            {
                Data = new ExportCommissionOrderHistoryResponse { File = path + "/" + fileName }
            };
        }

        public string CommissionPaymentStatusDescription(CommissionPaymentStatus status)
        {
            return status switch
            {
                CommissionPaymentStatus.pending => "Chưa thanh toán",
                CommissionPaymentStatus.paid => "Đã thanh toán",
                CommissionPaymentStatus.cancelled => "Đã hủy",
                _ => "Không xác định"
            };
        }
    }
}