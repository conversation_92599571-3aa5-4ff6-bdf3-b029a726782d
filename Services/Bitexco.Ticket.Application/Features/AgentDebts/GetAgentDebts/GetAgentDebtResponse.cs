using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.AgentDebts.GetAgentDebts;

public record GetAgentDebtResponse
{
    public Guid Id { get; set; }
    public Guid AgentId { get; set; }
    public Guid OrderId { get; set; }
    public decimal OriginalAmount { get; set; }
    public decimal PaidAmount { get; set; }
    public AgentDebtStatus Status { get; set; }
    public AgentDebtPaymentMethod? PaymentMethod { get; set; }
    public DateTime? PaidAt { get; set; }
    public Guid? ApprovedByUserId { get; set; }
    public string? Notes { get; set; }
    public string? PaymentProofFilePath { get; set; }
    public string? InvoiceFilePath { get; set; }
    public DateTime? CreatedAt { get; set; }
    public AgentResponseDto? Agent { get; set; }
    public OrderResponseDto? Order { get; set; }
}