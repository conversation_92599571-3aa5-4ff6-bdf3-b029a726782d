namespace Bitexco.Ticket.Application.Features.AgentDebts.GetAgentDebts;

/// <summary>
/// Query to get paginated list of agent debts with filtering options
/// </summary>
public class GetAgentDebtsQuery
    : IRequest<PaginationResponse<GetAgentDebtResponse>>, IPaginationRequest, ISortRequest
{
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 100;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool? IsDescending { get; set; } = true;

    //filtering options
    public string? SearchTerm { get; set; }
    public AgentDebtStatus? Status { get; set; }
    public DateOnly? CreatedDate { get; set; }
}

/// <summary>
/// Handler for GetAgentDebtsQuery
/// </summary>
public class GetAgentDebtsQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetAgentDebtsQuery, PaginationResponse<GetAgentDebtResponse>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<PaginationResponse<GetAgentDebtResponse>> Handle(GetAgentDebtsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.AgentDebts
            .AsNoTracking()
            .Include(d => d.Agent)
            .Include(d => d.Order)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query.Where(d => d.Agent.Name.ToLower().Contains(searchTerm) ||
                                   d.Agent.Code.ToLower().Contains(searchTerm) ||
                                   d.Order.Code.ToLower().Contains(searchTerm));
        }

        if (request.Status.HasValue)
        {
            query = query.Where(d => d.Status == request.Status.Value);
        }

        if (request.CreatedDate.HasValue)
        {
            var startAt = request.CreatedDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
            var endAt = request.CreatedDate.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);
            query = query.Where(d => d.CreatedAt >= startAt && d.CreatedAt <= endAt);
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = request.SortBy?.ToLower() switch
        {
            "status" => request.IsDescending == true ? query.OrderByDescending(d => d.Status) : query.OrderBy(d => d.Status),
            "createdat" => request.IsDescending == true ? query.OrderByDescending(d => d.CreatedAt) : query.OrderBy(d => d.CreatedAt),
            "agenname" => request.IsDescending == true ? query.OrderByDescending(d => d.Agent.Name) : query.OrderBy(d => d.Agent.Name),
            "ordercode" => request.IsDescending == true ? query.OrderByDescending(d => d.Order.Code) : query.OrderBy(d => d.Order.Code),
            "originalamount" => request.IsDescending == true ? query.OrderByDescending(d => d.OriginalAmount) : query.OrderBy(d => d.OriginalAmount),
            _ => query.OrderByDescending(d => d.CreatedAt) // Default sorting
        };

        // Apply pagination and ordering
        var debts = await query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        var debtDtos = debts.Adapt<List<GetAgentDebtResponse>>();

        return new PaginationResponse<GetAgentDebtResponse>(
            request.PageIndex,
            request.PageSize,
            totalCount,
            debtDtos);
    }
}
