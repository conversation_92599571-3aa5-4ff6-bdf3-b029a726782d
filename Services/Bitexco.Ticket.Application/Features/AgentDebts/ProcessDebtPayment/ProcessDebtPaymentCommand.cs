using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace Bitexco.Ticket.Application.Features.AgentDebts.ProcessDebtPayment;

/// <summary>
/// Command to process a payment towards an agent debt
/// </summary>
public class ProcessDebtPaymentCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
    public DateTime? PaidAt { get; set; }
    public AgentDebtPaymentMethod? PaymentMethod { get; set; }
    public string? PaymentProofFilePath { get; set; }
}

/// <summary>
/// Handler for ProcessDebtPaymentCommand
/// </summary>
public class ProcessDebtPaymentCommandHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor) : IRequestHandler<ProcessDebtPaymentCommand, Response<object>>
{
    public async Task<Response<object>> Handle(ProcessDebtPaymentCommand request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;

        // Process the payment using the service
        var updatedDebt = await dbContext.AgentDebts
            .AsTracking()
            .Where(d => d.Id == request.Id)
            .FirstOrDefaultAsync(cancellationToken)
            ?? throw new AgentDebtNotFoundException(request.Id);

        // Update the debt with payment details
        updatedDebt.PaidAt = request.PaidAt ?? DateTime.UtcNow;
        updatedDebt.PaymentMethod = request.PaymentMethod;
        updatedDebt.PaymentProofFilePath = request.PaymentProofFilePath;
        updatedDebt.Status = AgentDebtStatus.paid;
        updatedDebt.ApprovedByUserId = userId != null ? Guid.Parse(userId) : null;
        updatedDebt.InvoiceFilePath = string.Empty; // TODO: call to invoice service to generate invoice

        // Save changes to the database
        dbContext.AgentDebts.Update(updatedDebt);
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("AgentDebt", request.Id);
        }
        
        return new Response<object>(true);
    }
}
