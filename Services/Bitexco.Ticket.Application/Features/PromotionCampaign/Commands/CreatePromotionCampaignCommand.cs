﻿using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;

namespace Bitexco.Ticket.Application.Features.PromotionCampaign.Commands
{
    public record CreatePromotionCampaignCommand : ICommand<Response<CreatePromotionCampaignResponse>>
    {
        public string? Name { get; set; }

        public PromotionType Type { get; set; }
        public decimal DiscountValue { get; set; }

        /// <summary>
        /// Maximum discount amount that can be applied
        /// This is used to limit the discount for fixed amount promotion
        /// Null means no maximum limit
        /// </summary>
        public decimal? MaxDiscountAmount { get; set; }
       
        /// <summary>
        ///  Ticket type ID to which this voucher applies
        /// </summary>
        public Guid? TicketTypeId { get; set; }

        public PromotionApplyType PromotionApplyType { get; set; }
        public List<Guid>? AgentIds { get; set; }

        /// <summary>
        /// Date and time when this promotion becomes valid
        /// </summary>
        public DateTime ValidFrom { get; set; }

        /// <summary>
        /// Date and time when this voucher expires
        /// </summary>
        public DateTime ValidTo { get; set; }
    }

    public record CreatePromotionCampaignResponse
    {
        public Guid Id { get; set; }
    }

    public class CreatePromotionCampaignValidator : AbstractValidator<CreatePromotionCampaignCommand>
    {
        public CreatePromotionCampaignValidator()
        {
            RuleFor(x => x.Name).NotEmpty().WithMessage("Name is required.");
            RuleFor(x => x.Type).IsInEnum().WithMessage("Invalid voucher type.");
            When(x => x.Type == PromotionType.percentage, () =>
            {
                RuleFor(x => x.DiscountValue).InclusiveBetween(0.01m, 100).WithMessage("Discount value must be between 0.01 and 100 for percentage promotion.");
            });
            When(x => x.Type == PromotionType.fixed_amount, () =>
            {
                RuleFor(x => x.DiscountValue).GreaterThan(0).WithMessage("Discount value must be greater than zero for fixed amount promotion.");
            });

            RuleFor(x => x.ValidFrom).LessThanOrEqualTo(x => x.ValidTo)
               .WithMessage("Valid From date must be before or equal to Valid To date.");
        }
    }

    public class CreatePromotionCampaignCommandHandler(IApplicationDbContext applicationDbContext)
        : ICommandHandler<CreatePromotionCampaignCommand, Response<CreatePromotionCampaignResponse>>
    {
        private readonly IApplicationDbContext _applicationDbContext = applicationDbContext;

        public async Task<Response<CreatePromotionCampaignResponse>> Handle(CreatePromotionCampaignCommand request, CancellationToken cancellationToken)
        {
            int result = 0;
            var promotionCampaign = new Domain.Models.PromotionCampaign
            {
                Name = request.Name!,
                Type = request.Type,
                DiscountValue = request.DiscountValue,
                MaxDiscountAmount = request.MaxDiscountAmount,
                PromotionApplyType = request.PromotionApplyType,
                AgentIds = request.AgentIds ?? new List<Guid>(),
                ValidFrom = request.ValidFrom,
                ValidTo = request.ValidTo
            };
            if (request.TicketTypeId.HasValue)
            {
                // Validate if the TicketTypeId exists in the database
                var ticketType = await _applicationDbContext.TicketTypes
                    .FindAsync(request.TicketTypeId.Value)
                    ?? throw new TicketTypeNotFoundException(request.TicketTypeId.Value);

                promotionCampaign.TicketTypeId = request.TicketTypeId.Value;
            }
            _applicationDbContext.PromotionCampaigns.Add(promotionCampaign);
            result = await _applicationDbContext.SaveChangesAsync(cancellationToken);
            // If no promotion were generated, throw an exception
            return result <= 0
                ? throw new NoEntityCreatedException("PromotionCampaign")
                : new Response<CreatePromotionCampaignResponse>(new CreatePromotionCampaignResponse
                {
                    Id = promotionCampaign.Id
                });
        }
    }
}