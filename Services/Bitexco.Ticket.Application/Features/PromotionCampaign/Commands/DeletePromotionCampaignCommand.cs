﻿namespace Bitexco.Ticket.Application.Features.PromotionCampaign.Commands
{
    public record DeletePromotionCampaignCommand : ICommand<Response<object>>
    {
        public Guid Id { get; init; }
    }

    public class DeleteVoucherCampaignCommandHandler(IApplicationDbContext dbContext)
        : ICommandHandler<DeletePromotionCampaignCommand, Response<object>>
    {
        public async Task<Response<object>> Handle(DeletePromotionCampaignCommand request, CancellationToken cancellationToken)
        {
            var campaign = await dbContext.PromotionCampaigns
                .AsTracking()
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken)
                ?? throw new PromotionCampaignNotFoundException(request.Id);

            
            campaign.IsDeleted = true;
            dbContext.PromotionCampaigns.Update(campaign);
            var result = await dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new NoEntityDeletedException("PromotionCampaign", campaign.Id);
            }
            return new Response<object>(true);
        }
    }
}