﻿using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.PromotionCampaign.Commands
{
    public record ActivePromotionCampaignCommand : ICommand<Response<PromotionCampaignResponseDto>>
    {
        public Guid Id { get; set; }
    }

    public class ActivePromotionCampaignCommandHandler(IApplicationDbContext dbContext)
        : ICommandHandler<ActivePromotionCampaignCommand, Response<PromotionCampaignResponseDto>>
    {
        public async Task<Response<PromotionCampaignResponseDto>> Handle(
            ActivePromotionCampaignCommand request,
            CancellationToken cancellationToken)
        {
            // Find the promotion campaign by Id
            var promotionCampaign = await dbContext.PromotionCampaigns
                .AsTracking()
                .FirstOrDefaultAsync(vc => vc.Id == request.Id, cancellationToken)
                ?? throw new PromotionCampaignNotFoundException(request.Id);
            
            // Update the IsActive status
            promotionCampaign.IsActive = !promotionCampaign.IsActive;
            dbContext.PromotionCampaigns.Update(promotionCampaign);

            // Save changes to the database
            var result = await dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new NoEntityUpdatedException("PromotionCampaign", request.Id);
            }

            // Map to response DTO
            var responseDto = promotionCampaign.Adapt<PromotionCampaignResponseDto>();

            return new Response<PromotionCampaignResponseDto>(responseDto);
        }
    }
}