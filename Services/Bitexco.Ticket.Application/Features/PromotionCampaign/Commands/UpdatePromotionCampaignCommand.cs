﻿using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using System.Text.Json.Serialization;

namespace Bitexco.Ticket.Application.Features.PromotionCampaign.Commands
{
    public class UpdatePromotionCampaignCommand() : ICommand<Response<PromotionCampaignResponseDto>>
    {
        [JsonIgnore]
        public Guid Id { get; set; } 

        public string? Name { get; set; }

        public PromotionType Type { get; set; }

        public decimal DiscountValue { get; set; }

        public decimal? MaxDiscountAmount { get; set; }

        /// <summary>
        ///  Ticket type ID to which this voucher applies
        /// </summary>
        public Guid? TicketTypeId { get; set; }

        public PromotionApplyType? PromotionApplyType { get; set; }

        public List<Guid>? AgentIds { get; set; }

        public DateTime ValidFrom { get; set; }

        public DateTime ValidTo { get; set; }
    }

    public class UpdatePromotionCampaginCommandValidator : AbstractValidator<UpdatePromotionCampaignCommand>
    {
        public UpdatePromotionCampaginCommandValidator()
        {
            RuleFor(x => x.Id).NotEmpty().WithMessage("Id is required.");
            RuleFor(x => x.Name).NotEmpty().WithMessage("Name is required.");
            RuleFor(x => x.Type).IsInEnum().WithMessage("Invalid promotion type.");
            When(x => x.Type == PromotionType.percentage, () =>
            {
                RuleFor(x => x.DiscountValue).InclusiveBetween(0.01m, 100).WithMessage("Discount value must be between 0.01 and 100 for percentage promotion.");
            });
            When(x => x.Type == PromotionType.fixed_amount, () =>
            {
                RuleFor(x => x.DiscountValue).GreaterThan(0).WithMessage("Discount value must be greater than zero for fixed amount promotion.");
            });
            RuleFor(x => x.ValidFrom).LessThanOrEqualTo(x => x.ValidTo)
                .WithMessage("Valid From date must be before or equal to Valid To date.");
        }
    }

    public class UpdatePromotionCampaignCommandHandler(IApplicationDbContext applicationDbContext)
        : ICommandHandler<UpdatePromotionCampaignCommand, Response<PromotionCampaignResponseDto>>
    {
        public async Task<Response<PromotionCampaignResponseDto>> Handle(UpdatePromotionCampaignCommand request, CancellationToken cancellationToken)
        {
            var promotion = await applicationDbContext.PromotionCampaigns.
                AsTracking()
                .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken)
                ?? throw new PromotionCampaignNotFoundException(request.Id);

            if (request.TicketTypeId.HasValue)
            {
                // Validate if the TicketTypeId exists in the database
                var ticketType = await applicationDbContext.TicketTypes
                    .FindAsync(request.TicketTypeId.Value)
                ?? throw new TicketTypeNotFoundException(request.TicketTypeId.Value);

                promotion.TicketTypeId = request.TicketTypeId.Value;
            }
            promotion.Name = request.Name!;
            promotion.Type = request.Type;
            promotion.DiscountValue = request.DiscountValue;
            promotion.MaxDiscountAmount = request.MaxDiscountAmount;
            promotion.PromotionApplyType = request.PromotionApplyType;
            promotion.AgentIds = request.AgentIds;
            promotion.ValidFrom = request.ValidFrom;
            promotion.ValidTo = request.ValidTo;

            applicationDbContext.PromotionCampaigns.Update(promotion);
            var result = await applicationDbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new NoEntityUpdatedException("PromotionCampaign", request.Id);
            }
            return new Response<PromotionCampaignResponseDto>(
                promotion.Adapt<PromotionCampaignResponseDto>());
        }
    }
}