﻿using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.PromotionCampaign.Queries
{
    public record PromotionCampaignQuery
        : PromotionCampaignQueryQueryDto, IQuery<Response<List<PromotionCampaignResponseDto>>>;

    public record PromotionCampaignQueryQueryDto
    {
        public string? Search { get; set; }
    }

    public class PromotionCampaignQueryHandler(IApplicationDbContext dbContext)
        : IQueryHandler<PromotionCampaignQuery, Response<List<PromotionCampaignResponseDto>>>
    {
        public async Task<Response<List<PromotionCampaignResponseDto>>> Handle(
            PromotionCampaignQuery request,
            CancellationToken cancellationToken)
        {
            var query = dbContext.PromotionCampaigns.
                Include(v => v.TicketType)
                .AsNoTracking()
                .AsQueryable();

            if (!string.IsNullOrEmpty(request.Search))
                query = query.Where(v => request.Search.Contains(v.Name));

            var items = await query
                .OrderByDescending(v => v.CreatedAt)

                .ToListAsync(cancellationToken);
            var result = new List<PromotionCampaignResponseDto>();
            items.ForEach(item =>
            {
                result.Add(new PromotionCampaignResponseDto
                {
                    Id = item.Id,
                    Name = item.Name,
                    CreatedAt = item.CreatedAt!.Value,
                    UpdatedAt = item.UpdatedAt,
                    IsActive = item.IsActive,
                    Type = item.Type,
                    DiscountValue = item.DiscountValue,
                    MaxDiscountAmount = item.MaxDiscountAmount,
                    PromotionApplyType = item.PromotionApplyType,
                    AgentIds = item.AgentIds,
                    ValidFrom = item.ValidFrom,
                    ValidTo = item.ValidTo,
                    TicketTypeId = item.TicketTypeId,
                    TicketTypeName = item.TicketType?.Name,
                });
            });

            return new Response<List<PromotionCampaignResponseDto>>(result);
        }
    }
}