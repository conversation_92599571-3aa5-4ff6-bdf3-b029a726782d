using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.PosDevices.GetPosDeviceBySerial;

/// <summary>
/// Query to get a POS device by serial number
/// </summary>
public class GetPosDeviceBySerialQuery : IRequest<Response<PosDeviceResponseDto>>
{
    /// <summary>
    /// POS device serial number
    /// </summary>
    public string SerialNumber { get; set; } = null!;
}

/// <summary>
/// Handler for GetPosDeviceBySerialQuery
/// </summary>
public class GetPosDeviceBySerialQueryHandler(IApplicationDbContext context) : IRequestHandler<GetPosDeviceBySerialQuery, Response<PosDeviceResponseDto>>
{
    public async Task<Response<PosDeviceResponseDto>> Handle(GetPosDeviceBySerialQuery request, CancellationToken cancellationToken)
    {
        var device = await context.PosDevices
            .AsNoTracking()
            .FirstOrDefaultAsync(d => d.SerialNumber == request.SerialNumber, cancellationToken)
            ?? throw new PosDeviceNotFoundException(request.SerialNumber);
        
        var deviceDto = device.Adapt<PosDeviceResponseDto>();

        return new Response<PosDeviceResponseDto>(deviceDto);
    }
}
