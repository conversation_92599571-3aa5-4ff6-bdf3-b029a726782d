using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.PosDevices.GetPosDevices;

/// <summary>
/// Query to get all POS devices
/// </summary>
public class GetPosDevicesQuery : IRequest<Response<List<PosDeviceResponseDto>>>
{
    // No parameters - just get all devices
}

/// <summary>
/// Handler for GetPosDevicesQuery
/// </summary>
public class GetPosDevicesQueryHandler(IApplicationDbContext context) : IRequestHandler<GetPosDevicesQuery, Response<List<PosDeviceResponseDto>>>
{
    public async Task<Response<List<PosDeviceResponseDto>>> Handle(GetPosDevicesQuery request, CancellationToken cancellationToken)
    {
        var devices = await context.PosDevices
            .AsNoTracking()
            .ProjectToType<PosDeviceResponseDto>()
            .ToListAsync(cancellationToken);

        return new Response<List<PosDeviceResponseDto>>(devices);
    }
}
