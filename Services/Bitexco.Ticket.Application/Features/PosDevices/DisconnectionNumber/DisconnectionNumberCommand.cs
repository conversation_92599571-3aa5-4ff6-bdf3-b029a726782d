namespace Bitexco.Ticket.Application.Features.PosDevices.DisconnectionNumber;

/// <summary>
/// Command to update the disconnection number for a POS device by ID
/// </summary>
public class DisconnectionNumberCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for DisconnectionNumberCommand
/// </summary>
public class DisconnectionNumberCommandHandler(IApplicationDbContext context) : IRequestHandler<DisconnectionNumberCommand, Response<object>>
{
    public async Task<Response<object>> Handle(DisconnectionNumberCommand request, CancellationToken cancellationToken)
    {
        // get pos device by id
        var posDevice = await context.PosDevices
            .AsTracking()
            .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken)
            ?? throw new PosDeviceNotFoundException(request.Id);

        // update the disconnection number
        posDevice.IsConnected = false;
        posDevice.SerialNumber = string.Empty;

        var result = await context.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("PosDevice", request.Id);
        }

        return new Response<object>(true);
    }
}