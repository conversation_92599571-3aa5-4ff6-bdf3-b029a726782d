namespace Bitexco.Ticket.Application.Features.PosDevices.DeletePosDevice;

/// <summary>
/// Command to delete a POS device
/// </summary>
public class DeletePosDeviceCommand : IRequest<Response<object>>
{
    /// <summary>
    /// POS device ID to delete
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for DeletePosDeviceCommand
/// </summary>
public class DeletePosDeviceCommandHandler(IApplicationDbContext context) : IRequestHandler<DeletePosDeviceCommand, Response<object>>
{
    public async Task<Response<object>> Handle(DeletePosDeviceCommand request, CancellationToken cancellationToken)
    {
        // Find existing device
        var existingDevice = await context.PosDevices
            .FindAsync([request.Id], cancellationToken)
            ?? throw new PosDeviceNotFoundException(request.Id);

        // Check if device has active orders or shifts
        var hasActiveOrders = await context.Orders
            .AsNoTracking()
            .AnyAsync(o => o.PosDeviceId == request.Id, cancellationToken);
            
        if (hasActiveOrders)
        {
            throw new PosDeviceHasActiveOrdersException(request.Id);
        }

        var hasActiveShifts = await context.Shifts
            .AsNoTracking()
            .AnyAsync(s => s.PosDeviceId == request.Id, cancellationToken);
            
        if (hasActiveShifts)
        {
            throw new PosDeviceHasActiveShiftsException(request.Id);
        }
        
        existingDevice.IsDeleted = true;
        context.PosDevices.Update(existingDevice);
        var result = await context.SaveChangesAsync(cancellationToken);
        
        if (result == 0)
        {
            throw new NoEntityDeletedException("PosDevice", request.Id);
        }

        return new Response<object>(new { status = true });
    }
}
