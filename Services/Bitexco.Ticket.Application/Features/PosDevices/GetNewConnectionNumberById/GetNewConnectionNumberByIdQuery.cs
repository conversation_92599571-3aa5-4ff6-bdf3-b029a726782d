using BuildingBlocks.CacheServices;
using BuildingBlocks.Utils;

namespace Bitexco.Ticket.Application.Features.PosDevices.GetNewConnectionNumberById;

/// <summary>
/// Query to get a new connection number for a POS device by ID
/// </summary>
public class GetNewConnectionNumberByIdQuery : IRequest<Response<string>>
{
    /// <summary>
    /// POS device ID
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for GetNewConnectionNumberByIdQuery
/// </summary>
public class GetNewConnectionNumberByIdQueryHandler(IApplicationDbContext context,
    ICachedService cachedService) : IRequestHandler<GetNewConnectionNumberByIdQuery, Response<string>>
{
    public async Task<Response<string>> Handle(GetNewConnectionNumberByIdQuery request, CancellationToken cancellationToken)
    {
        var device = await context.PosDevices
            .AsNoTracking()
            .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken)
            ?? throw new PosDeviceNotFoundException(request.Id);

        // Generate a new connection number
        var sessionCode = CommonUtils.GenerateNewNumber(6);

        //save to cache
        await cachedService.SetAsync(sessionCode, request.Id.ToString(), 600, cancellationToken);

        return new Response<string>(sessionCode);
    }
}