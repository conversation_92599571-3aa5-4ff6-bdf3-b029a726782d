using Bitexco.Ticket.Application.Dtos;
using BuildingBlocks.CacheServices;

namespace Bitexco.Ticket.Application.Features.PosDevices.UpdateNewConnectionNumber;

/// <summary>
/// Command to update the new connection number for a POS device by ID
/// </summary>
public class UpdateNewConnectionNumberCommand : IRequest<Response<PosDeviceResponseDto>>
{
    /// <summary>
    /// Session code of the POS device
    /// </summary>
    public string SessionCode { get; set; } = null!;

    /// <summary>
    /// Serial number of the POS device
    /// </summary>
    public string SerialNumber { get; set; } = null!;
}

/// <summary>
/// Handler for UpdateNewConnectionNumberCommand
/// </summary>
public class UpdateNewConnectionNumberCommandHandler(IApplicationDbContext context,
    ICachedService cachedService) : IRequestHandler<UpdateNewConnectionNumberCommand, Response<PosDeviceResponseDto>>
{
    public async Task<Response<PosDeviceResponseDto>> Handle(UpdateNewConnectionNumberCommand request, CancellationToken cancellationToken)
    {
        // get pos device by session code from cache
        var posDeviceId = await cachedService.GetAsync<string>(request.SessionCode, cancellationToken);
        if (string.IsNullOrEmpty(posDeviceId))
        {
            throw new SessionCodeNotFoundException(request.SessionCode);
        }

        // get pos device by serial number
        var posDevice = await context.PosDevices
            .AsTracking()
            .FirstOrDefaultAsync(d => d.Id == Guid.Parse(posDeviceId), cancellationToken)
            ?? throw new PosDeviceNotFoundException(request.SerialNumber);

        // update the new connection number
        posDevice.SerialNumber = request.SerialNumber;
        posDevice.IsConnected = true;

        var result = await context.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("PosDevice", request.SerialNumber);
        }

        //remove the cached session code
        await cachedService.RemoveAsync(request.SessionCode, cancellationToken);

        return new Response<PosDeviceResponseDto>(posDevice.Adapt<PosDeviceResponseDto>());
    }
}