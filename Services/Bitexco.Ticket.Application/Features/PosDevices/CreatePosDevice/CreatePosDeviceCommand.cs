using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Utils;

namespace Bitexco.Ticket.Application.Features.PosDevices.CreatePosDevice;

/// <summary>
/// Command to create a new POS device
/// </summary>
public record CreatePosDeviceCommand : IRequest<Response<CreatePosDeviceResult>>
{
    public PosDeviceDto PosDevice { get; set; } = null!;
}

public record CreatePosDeviceResult(Guid Id); 

/// <summary>
/// Handler for CreatePosDeviceCommand
/// </summary>
public class CreatePosDeviceCommandHandler(IApplicationDbContext context) : IRequestHandler<CreatePosDeviceCommand, Response<CreatePosDeviceResult>>
{
    public async Task<Response<CreatePosDeviceResult>> Handle(CreatePosDeviceCommand request, CancellationToken cancellationToken)
    {
        // Check if device with same serial number
        if (!string.IsNullOrEmpty(request.PosDevice.SerialNumber))
        {
            var existingBySerial = await context.PosDevices
                .AsNoTracking()
                .AnyAsync(d => d.SerialNumber == request.PosDevice.SerialNumber, cancellationToken);
            if (existingBySerial)
            {
                throw new DuplicatedDataException("SerialNumber");
            }
        }
        
        if (!string.IsNullOrEmpty(request.PosDevice.MacAddress))
        {
            var existingByMac = await context.PosDevices
                .AsNoTracking()
                .AnyAsync(d => d.MacAddress == request.PosDevice.MacAddress, cancellationToken);

            if (existingByMac)
            {
                throw new DuplicatedDataException("MacAddress");
            }
        }
        
        var posDevice = request.PosDevice.Adapt<PosDevice>(); 
        posDevice.Code = $"POS-{CommonUtils.GenerateNewCode(6)}";
        posDevice.Status = PosDeviceStatus.online;
        posDevice.Type = PosDeviceType.POS;
        
        context.PosDevices.Add(posDevice);
        var result = await context.SaveChangesAsync(cancellationToken);
        
        if (result == 0)
        {
            throw new NoEntityCreatedException("PosDevice");
        }

        return new Response<CreatePosDeviceResult>(new CreatePosDeviceResult(posDevice.Id));
    }
}
