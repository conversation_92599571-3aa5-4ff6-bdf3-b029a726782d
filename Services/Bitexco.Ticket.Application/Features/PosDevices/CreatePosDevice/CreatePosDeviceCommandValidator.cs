namespace Bitexco.Ticket.Application.Features.PosDevices.CreatePosDevice;

/// <summary>
/// Validator for CreatePosDeviceCommand
/// </summary>
public class CreatePosDeviceCommandValidator : AbstractValidator<CreatePosDeviceCommand>
{
    public CreatePosDeviceCommandValidator()
    {
        RuleFor(x => x.PosDevice)
            .NotNull().WithMessage("POS device data is required");

        RuleFor(x => x.PosDevice.Name)
            .NotNull().WithMessage("POS device name is required")
            .MaximumLength(200).WithMessage("POS device name cannot exceed 200 characters");
    }
}
