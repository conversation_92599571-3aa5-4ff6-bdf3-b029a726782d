using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.PosDevices.GetPosDeviceConnectionStatusById;

/// <summary>
/// Query to get a POS connection status by ID
/// </summary>
public class GetPosDeviceConnectionStatusByIdQuery : IRequest<Response<GetPosDeviceConnectionStatusByIdResult>>
{
    /// <summary>
    /// POS device ID
    /// </summary>
    public Guid Id { get; set; }
}

public record GetPosDeviceConnectionStatusByIdResult(bool IsConnected);

/// <summary>
/// Handler for GetPosDeviceByIdQuery
/// </summary>
public class GetPosDeviceByIdQueryHandler(IApplicationDbContext context) : IRequestHandler<GetPosDeviceConnectionStatusByIdQuery, Response<GetPosDeviceConnectionStatusByIdResult>>
{
    public async Task<Response<GetPosDeviceConnectionStatusByIdResult>> Handle(GetPosDeviceConnectionStatusByIdQuery request, CancellationToken cancellationToken)
    {
        var device = await context.PosDevices
            .AsNoTracking()
            .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken)
            ?? throw new PosDeviceNotFoundException(request.Id);

        return new Response<GetPosDeviceConnectionStatusByIdResult>(new GetPosDeviceConnectionStatusByIdResult(device.IsConnected));
    }
}
