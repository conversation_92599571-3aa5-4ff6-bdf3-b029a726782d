using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.PosDevices.GetPosDeviceById;

/// <summary>
/// Query to get a POS device by ID
/// </summary>
public class GetPosDeviceByIdQuery : IRequest<Response<PosDeviceResponseDto>>
{
    /// <summary>
    /// POS device ID
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for GetPosDeviceByIdQuery
/// </summary>
public class GetPosDeviceByIdQueryHandler(IApplicationDbContext context) : IRequestHandler<GetPosDeviceByIdQuery, Response<PosDeviceResponseDto>>
{
    public async Task<Response<PosDeviceResponseDto>> Handle(GetPosDeviceByIdQuery request, CancellationToken cancellationToken)
    {
        var device = await context.PosDevices
            .AsNoTracking()
            .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken)
            ?? throw new PosDeviceNotFoundException(request.Id);
        
        var deviceDto = device.Adapt<PosDeviceResponseDto>();

        return new Response<PosDeviceResponseDto>(deviceDto);
    }
}
