using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.PosDevices.UpdatePosDevice;

/// <summary>
/// Command to update an existing POS device
/// </summary>
public record UpdatePosDeviceCommand : IRequest<Response<UpdatePosDeviceResult>>
{
    /// <summary>
    /// POS device ID to update
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// POS device update data
    /// </summary>
    public PosDeviceDto PosDevice { get; set; } = null!;
}

public record UpdatePosDeviceResult(Guid Id);

/// <summary>
/// Handler for UpdatePosDeviceCommand
/// </summary>
public class UpdatePosDeviceCommandHandler(IApplicationDbContext context) : IRequestHandler<UpdatePosDeviceCommand, Response<UpdatePosDeviceResult>>
{
    public async Task<Response<UpdatePosDeviceResult>> Handle(UpdatePosDeviceCommand request, CancellationToken cancellationToken)
    {
        // Find existing device
        var existingDevice = await context.PosDevices
            .FindAsync([request.Id], cancellationToken)
            ?? throw new PosDeviceNotFoundException(request.Id);

        // Check for duplicate serial number or MAC address (excluding current device)
        if (!string.IsNullOrEmpty(request.PosDevice.SerialNumber))
        {
            var duplicateSerial = await context.PosDevices.AsNoTracking().AnyAsync(
                d => d.SerialNumber == request.PosDevice.SerialNumber && d.Id != request.Id, 
                cancellationToken);
            if (duplicateSerial)
            {
                throw new DuplicatedDataException("SerialNumber");
            }
        }

        if (!string.IsNullOrEmpty(request.PosDevice.MacAddress))
        {
            var duplicateMac = await context.PosDevices.AsNoTracking().AnyAsync(
                d => d.MacAddress == request.PosDevice.MacAddress && d.Id != request.Id, 
                cancellationToken);
            if (duplicateMac)
            {
                throw new DuplicatedDataException("MacAddress");
            }
        }
        
        //only update fields that are provided
        existingDevice.Name = request.PosDevice.Name ?? string.Empty;
        existingDevice.Location = request.PosDevice.Location ?? string.Empty;

        context.PosDevices.Update(existingDevice);

        var result = await context.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityUpdatedException("PosDevice", request.Id);
        }

        return new Response<UpdatePosDeviceResult>(new UpdatePosDeviceResult(existingDevice.Id));
    }
}