namespace Bitexco.Ticket.Application.Features.PosDevices.UpdatePosDevice;

/// <summary>
/// Validator for UpdatePosDeviceCommand
/// </summary>
public class UpdatePosDeviceCommandValidator : AbstractValidator<UpdatePosDeviceCommand>
{
    public UpdatePosDeviceCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotNull().WithMessage("POS device ID is required");

        RuleFor(x => x.PosDevice)
            .NotNull().WithMessage("POS device data is required");

        RuleFor(x => x.PosDevice.Name)
            .NotNull().WithMessage("POS device name is required")
            .MaximumLength(200).WithMessage("POS device name cannot exceed 200 characters");

        RuleFor(x => x.PosDevice.Type)
            .NotNull().WithMessage("POS device type is required");

        RuleFor(x => x.PosDevice.Status)
            .NotNull().WithMessage("POS device status is required");
    }
}
