namespace Bitexco.Ticket.Application.Features.Contracts;

public record DeleteContractCommand(
        Guid Id,
        Guid AgentId) : ICommand<Response<object>>;

public class DeleteContractCommandHandler(IApplicationDbContext dbContext) : ICommandHandler<DeleteContractCommand, Response<object>>
{
    public async Task<Response<object>> Handle(DeleteContractCommand request, CancellationToken cancellationToken)
    {
        // Get contract with agent validation
        var contract = await dbContext.Contracts
            .AsTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id && x.AgentId == request.AgentId, cancellationToken)
            ?? throw new ContractNotFoundException(request.Id);
        
        // Update contract properties
        contract.IsDeleted = true;

        var result = await dbContext.SaveChangesAsync(cancellationToken);

        if (result <= 0)
        {
            throw new NoEntityDeletedException("Contract", request.Id);
        }

        return new Response<object>(true);
    }
}
