namespace Bitexco.Ticket.Application.Features.Contracts;


public record UpdateContractCommand(
        Guid Id,
        Guid AgentId,
        string SapContractCode,
        string ContractName,
        DateOnly SignedDate,
        DateOnly ExpiryDate,
        string? ContractFileUrl,
        ContractStatus Status,
        string? Notes) : ICommand<Response<object>>;

public class UpdateContractCommandHandler(IApplicationDbContext dbContext) : ICommandHandler<UpdateContractCommand, Response<object>>
{
    public async Task<Response<object>> Handle(UpdateContractCommand request, CancellationToken cancellationToken)
    {
        // Get contract with agent validation
        var contract = await dbContext.Contracts
            .AsTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id && x.AgentId == request.AgentId, cancellationToken)
            ?? throw new ContractNotFoundException(request.Id);
        
        // Update contract properties
        contract.SapContractCode = request.SapContractCode;
        contract.ContractName = request.ContractName;
        contract.SignedDate = request.SignedDate;
        contract.ExpiryDate = request.ExpiryDate;
        contract.ContractFileUrl = request.ContractFileUrl;
        contract.Status = request.Status;
        contract.Notes = request.Notes;

        var result = await dbContext.SaveChangesAsync(cancellationToken);

        if (result <= 0)
        {
            throw new NoEntityUpdatedException("Contract", request.Id);
        }

        return new Response<object>(true);
    }
}
