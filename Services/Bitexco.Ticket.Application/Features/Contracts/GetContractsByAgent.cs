namespace Bitexco.Ticket.Application.Features.Contracts;

public record GetContractsByAgentQuery(Guid AgentId) : IQuery<Response<List<ContractResponse>>>;

public record ContractResponse(
        Guid Id,
        string? SapContractCode,
        string? ContractName,
        DateOnly? SignedDate,
        DateOnly? ExpiryDate,
        string? ContractFileUrl,
        ContractStatus Status,
        Guid AgentId,
        string? Notes,
        DateTime CreatedAt,
        DateTime UpdatedAt);
        
public class GetContractsByAgentHandler(IApplicationDbContext context)
        : IQueryHandler<GetContractsByAgentQuery, Response<List<ContractResponse>>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<Response<List<ContractResponse>>> Handle(GetContractsByAgentQuery request, CancellationToken cancellationToken)
    {
        // Validate agent exists
        var agentExists = await _context.Agents
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == request.AgentId, cancellationToken)
            ?? throw new AgentNotFoundException(request.AgentId);

        var contracts = await _context.Contracts
            .AsNoTracking()
            .Where(x => x.AgentId == request.AgentId)
            .OrderByDescending(x => x.CreatedAt)
            .ProjectToType<ContractResponse>()
            .ToListAsync(cancellationToken);

        return new Response<List<ContractResponse>>(contracts);
    }
}
