namespace Bitexco.Ticket.Application.Features.Contracts;

public record CreateContractCommand(
    Guid AgentId,
    string SapContractCode,
    string ContractName,
    DateOnly SignedDate,
    DateOnly ExpiryDate,
    string? ContractFileUrl,
    ContractStatus Status,
    string? Notes) : ICommand<Response<CreateContractResponse>>;

public record CreateContractResponse(Guid Id);

public class CreateContractCommandHandler(IApplicationDbContext dbcontext) : ICommandHandler<CreateContractCommand, Response<CreateContractResponse>>
{
    public async Task<Response<CreateContractResponse>> Handle(CreateContractCommand request, CancellationToken cancellationToken)
    {
        // Validate agent exists
        var agentExists = await dbcontext.Agents
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == request.AgentId, cancellationToken)
            ?? throw new AgentNotFoundException(request.AgentId);

        var contract = request.Adapt<Contract>();

        dbcontext.Contracts.Add(contract);
        var result = await dbcontext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityCreatedException("Contract");        }

        return new Response<CreateContractResponse>(new CreateContractResponse(contract.Id));
    }
}