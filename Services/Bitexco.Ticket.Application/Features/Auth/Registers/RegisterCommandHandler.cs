namespace Bitexco.Ticket.Application.Features.Auth.Login;

using System.Threading;
using System.Threading.Tasks;
using BCrypt.Net;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Application.Features.Auth.Registers;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Utils;
using MediatR;

public class RegisterCommandHandler(IApplicationDbContext dbContext, IUserRepository userRepository) : IRequestHandler<RegisterCommand, Response<RegisterResponseDto>>
{

    public async Task<Response<RegisterResponseDto>> Handle(RegisterCommand request, CancellationToken cancellationToken)
    {
        // Check if user already exists by email or username
        var existingUserByEmail = await userRepository.GetByEmailAsync(request.User.Email, cancellationToken);
        var existingUserByUsername = await userRepository.GetByUsernameAsync(request.User.UserName, cancellationToken);

        if (existingUserByEmail != null || existingUserByUsername != null)
        {
            throw new UserAlreadyExistsException();
        }

        // Validate role exist in db
        var roles = await dbContext.Roles
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        if (roles.All(r => r.Type != request.User.RoleType))
        {
            throw new RoleNotFoundException();
        }

        if (request.User.RoleType == RoleType.pos)
        {
            if (request.User.PosRole == null)
            {
                throw new PosRoleNotFoundException();
            }

            var posRoles = roles.Where(r => r.Type == request.User.RoleType).ToList();

            if (posRoles.All(r => r.Name != request.User.PosRole.ToString()))
            {
                throw new PosRoleNotFoundException(request.User.PosRole.ToString() ?? "Unknown");
            }
        }

        // Find the appropriate role
        Guid? roleId = null;
        if (request.User is { RoleType: RoleType.pos, PosRole: not null })
        {
            roleId = roles.FirstOrDefault(r => r.Name == request.User.PosRole.ToString() && r.Type == RoleType.pos)?.Id;
        }
        else if (request.User.RoleType == RoleType.admin)
        {
            roleId = roles.FirstOrDefault(r => r.Type == RoleType.admin)?.Id;
        }

        if (roleId == null)
        {
            throw new RoleNotFoundException();
        }

        // Create new user
        var newUser = new User
        {
            Code = CommonUtils.GenerateNewCode(8),
            UserName = request.User.UserName,
            PasswordHash = BCrypt.HashPassword(request.User.Password),
            Email = request.User.Email,
            FullName = request.User.FullName,
            PhoneNumber = request.User.PhoneNumber,
            RoleId = roleId,
            IsActive = true 
        };

        // Save the new user to the repository
        if(!await userRepository.CreateAsync(newUser, cancellationToken))
        {
            throw new NoEntityCreatedException("User");
        }

        // Return success response
        return new Response<RegisterResponseDto>(new RegisterResponseDto(true));
    }
}
