using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Auth.Registers;

public record RegisterRequestDto
{
    public string Email { get; set; } = null!;
    public RoleType RoleType { get; set; } = RoleType.pos;
    public PosRoles? PosRole { get; set; }
    public string UserName { get; set; } = null!;
    public string Password { get; set; } = null!;
    public string? FullName { get; set; }
    public string? PhoneNumber { get; set; }
}

public record RegisterResponseDto(bool IsSuccess);

public record RegisterCommand(RegisterRequestDto User) : IRequest<Response<RegisterResponseDto>>;