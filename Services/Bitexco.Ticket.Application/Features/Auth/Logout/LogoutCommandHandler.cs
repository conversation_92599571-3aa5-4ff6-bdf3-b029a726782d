namespace Bitexco.Ticket.Application.Features.Auth.Logout;

using System.Threading;
using System.Threading.Tasks;
using Bitexco.Ticket.Application.Data;
using BuildingBlocks.Abstractions;
using MediatR;

public class LogoutCommandHandler(IUserRepository userRepository) : IRequestHandler<LogoutCommand, Response<object>>
{
    private readonly IUserRepository _userRepository = userRepository;

    public async Task<Response<object>> Handle(LogoutCommand request, CancellationToken cancellationToken)
    {
        // Remove the refresh token from the database
        var result = await _userRepository.RemoveRefreshTokenAsync(request.RefreshToken, cancellationToken);
        
        // Return success if the token was successfully removed
        return new Response<object>(result);
    }
}
