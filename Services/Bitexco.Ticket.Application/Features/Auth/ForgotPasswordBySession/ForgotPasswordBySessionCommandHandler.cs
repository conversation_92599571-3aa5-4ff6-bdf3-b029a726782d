﻿using Bitexco.Ticket.Application.Services;
using BuildingBlocks.Exceptions;
using BuildingBlocks.MailServices;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;

namespace Bitexco.Ticket.Application.Features.Auth.ForgotPasswordBySession;

using System.Threading;
using System.Threading.Tasks;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Domain.Models;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.Extensions.Logging;

public class ForgotPasswordBySessionCommandHandler(IUserRepository userRepository,
    IApplicationDbContext dbContext,
    IConfiguration configuration,
    IWebHostEnvironment env,
    ILogger<ForgotPasswordBySessionCommandHandler> logger)
    : IRequestHandler<ForgotPasswordBySessionCommand, Response<ForgotPasswordResponseDto>>
{
    public async Task<Response<ForgotPasswordResponseDto>> Handle(ForgotPasswordBySessionCommand request, CancellationToken cancellationToken)
    {
        var user = await userRepository.GetByUsernameAsync(request.Account, cancellationToken)
            ?? throw new UserNotFoundException(); 
        
        if (!user.IsActive)
        {
            throw new UserNotFoundException(); 
        }
        
        var expiresAt = DateTime.UtcNow.AddMinutes(int.TryParse(configuration["UpdatePasswordExpireInMinutes"], out var minutes) ? minutes : 5);
        
        var session = new ForgotPasswordSession
        {
            UserId = user.Id,
            ExpiresAt = expiresAt,
        };

        dbContext.ForgotPasswordSessions.Add(session);
        var createdSessionResult = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (createdSessionResult == 0)
        {
            throw new ForgotPasswordSessionCreateFailedException();
        }
        
        var resetLink = $"{configuration["ClientUrl"]}/new-password?session={session.Id}&user={user.UserName}";
        logger.LogInformation("Password reset requested for user {UserName}. Reset link: {ResetLink}", user.UserName, resetLink);

        // get email template path from wwwroot path
        var templatePath = Path.Combine(env.WebRootPath, "Templates", "Mails", "TICKETForgotPassword.html");
        var template = await File.ReadAllTextAsync(templatePath, cancellationToken);

        // replace otp code in template
        template = template.Replace("{{AccountName}}", user.FullName ?? user.UserName);
        template = template.Replace("{{CreatePassword}}", resetLink);
        
        // Log SMTP configuration for debugging (without sensitive data)
        var port = int.TryParse(configuration["EmailPort"], out int parsedPort) ? parsedPort : 587;
        var host = configuration["EmailHost"];
        var fromMail = configuration["EmailFromMail"];
        var displayName = configuration["EmailDisplayName"];
        var subject = configuration["UpdatePasswordEmailSubject"];
        
        logger.LogInformation("Attempting to send email - Host: {Host}, Port: {Port}, From: {FromMail}, To: {ToMail}, Subject: {Subject}", 
            host, port, fromMail, request.Account, subject);
        
        var sendAsyncResult = await MailServices.SendAsync(
            port,
            host!,
            configuration["EmailPassword"]!,
            fromMail!,
            request.Account,
            displayName!,
            subject!,
            template
        );
        
        if (!string.IsNullOrEmpty(sendAsyncResult))
        {
            throw new EmailSendFailedException(sendAsyncResult);
        }
        
        var response = new ForgotPasswordResponseDto
        {
            Account = request.Account,
            ExpiresAt = expiresAt
        };

        return new Response<ForgotPasswordResponseDto>(response);
    }
}
