﻿namespace Bitexco.Ticket.Application.Features.Auth.ForgotPasswordBySession;

/// <summary>
/// Validator for ForgotPasswordBySessionCommand
/// </summary>
public class ForgotPasswordBySessionCommandValidator : AbstractValidator<ForgotPasswordBySessionCommand>
{
    public ForgotPasswordBySessionCommandValidator()
    {
        RuleFor(x => x.Account)
            .NotEmpty().WithMessage("Account is required")
            .EmailAddress().WithMessage("Account must be a valid email address");
    }
}
