using Bitexco.Ticket.Application.Features.Auth.Login;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Features.Auth.RefreshToken;

public class RefreshTokenCommandHandler(
    IUserRepository userRepository,
    IJwtTokenService jwtTokenService) : IRequestHandler<RefreshTokenCommand, Response<LoginResponseDto>>
{
    private readonly IUserRepository _userRepository = userRepository;
    private readonly IJwtTokenService _jwtTokenService = jwtTokenService;

    public async Task<Response<LoginResponseDto>> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        // Validate the refresh token (find user with the given token)
        var user = await _userRepository.GetByRefreshTokenAsync(request.RefreshToken, cancellationToken);
        
        // Check if user exists and token is not expired
        if (user == null || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
        {
            throw new InvalidRefreshTokenException();
        }

        // Generate new tokens
        string newAccessToken = await _jwtTokenService.GenerateAccessToken(user);
        string newRefreshToken = _jwtTokenService.GenerateRefreshToken();
        DateTime refreshTokenExpiryTime = _jwtTokenService.GetRefreshTokenExpiryTime();

        // Update the refresh token in the database
        await _userRepository.UpdateRefreshTokenAsync(
            user.Id,
            newRefreshToken,
            refreshTokenExpiryTime,
            cancellationToken);

        // Create response
        var response = new LoginResponseDto
        {
            AccessToken = newAccessToken,
            RefreshToken = newRefreshToken,
            RefreshTokenExpiryTime = refreshTokenExpiryTime,
            User = new UserDto
            {
                Id = user.Id,
                UserName = user.UserName,
                Email = user.Email ?? string.Empty,
                FullName = user.FullName ?? string.Empty,
                PhoneNumber = user.PhoneNumber ?? string.Empty,
                IsActive = user.IsActive,
                LastLoginAt = user.LastLoginAt,
                RoleName = user.Role?.Name ?? string.Empty
            }
        };

        return new Response<LoginResponseDto>(response);
    }
}
