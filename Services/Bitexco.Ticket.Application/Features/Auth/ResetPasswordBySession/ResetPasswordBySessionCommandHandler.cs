﻿using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Features.Auth.ResetPasswordBySession;

using System.Threading;
using System.Threading.Tasks;
using BCrypt.Net;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using BuildingBlocks.Abstractions;
using MediatR;

public class ResetPasswordBySessionCommandHandler(
    IUserRepository userRepository,
    IApplicationDbContext dbContext) : IRequestHandler<ResetPasswordBySessionCommand, Response<object>>
{
    public async Task<Response<object>> Handle(ResetPasswordBySessionCommand request, CancellationToken cancellationToken)
    {
        var user = await userRepository.GetByUsernameAsync(request.Account, cancellationToken) 
            ?? throw new UserNotFoundException();
        
        var session = await dbContext.ForgotPasswordSessions
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.Id == request.Session && s.UserId == user.Id, cancellationToken)
            ?? throw new ForgotPasswordSessionNotFoundException();

        // Check if session has expired
        if (session.ExpiresAt < DateTime.UtcNow)
        {
            throw new ForgotPasswordSessionExpiredException();
        }

        // Check if session has already been used
        if (session.VerifiedAt.HasValue)
        {
            throw new ForgotPasswordSessionUsedException();
        }
        
        session.VerifiedAt = DateTime.UtcNow;
        session.UpdatedAt = DateTime.UtcNow;
        
        dbContext.ForgotPasswordSessions.Update(session);
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (result == 0)
        {
            throw new ForgotPasswordSessionUpdateFailedException();
        }
        
        string passwordHash = BCrypt.HashPassword(request.Password);
        
        bool updateResult = await userRepository.UpdatePasswordAsync(user.Id, passwordHash, cancellationToken);
        if (!updateResult)
        {
            throw new PasswordUpdateFailedException();
        }

        return new Response<object>(new { success = true });
    }
}
