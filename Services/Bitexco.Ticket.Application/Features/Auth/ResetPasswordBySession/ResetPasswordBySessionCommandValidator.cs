﻿namespace Bitexco.Ticket.Application.Features.Auth.ResetPasswordBySession;

/// <summary>
/// Validator for ResetPasswordBySessionCommand
/// </summary>
public class ResetPasswordBySessionCommandValidator : AbstractValidator<ResetPasswordBySessionCommand>
{
    public ResetPasswordBySessionCommandValidator()
    {
        RuleFor(x => x.Account)
            .NotEmpty().WithMessage("Account is required");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(6).WithMessage("Password must be at least 6 characters long");

        RuleFor(x => x.Session)
            .NotEmpty().WithMessage("Session is required");
    }
}
