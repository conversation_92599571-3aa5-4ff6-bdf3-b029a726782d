﻿namespace Bitexco.Ticket.Application.Features.Auth.ResetPasswordBySession;

/// <summary>
/// Command to reset password using forgot password session
/// </summary>
public record ResetPasswordBySessionCommand : IRequest<Response<object>>
{
    /// <summary>
    /// User account (email, username, or phone number)
    /// </summary>
    public string Account { get; init; } = string.Empty;

    /// <summary>
    /// New password
    /// </summary>
    public string Password { get; init; } = string.Empty;

    /// <summary>
    /// Forgot password session ID
    /// </summary>
    public Guid Session { get; init; }
}
