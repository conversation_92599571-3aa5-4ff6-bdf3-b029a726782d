namespace Bitexco.Ticket.Application.Features.Auth.Login;

using System.Threading;
using System.Threading.Tasks;
using BCrypt.Net;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Application.Services;
using BuildingBlocks.Abstractions;
using MediatR;

public class LoginCommandHandler : IRequestHandler<LoginCommand, Response<LoginResponseDto>>
{
    private readonly IUserRepository _userRepository;
    private readonly IJwtTokenService _jwtTokenService;

    public LoginCommandHandler(IUserRepository userRepository, IJwtTokenService jwtTokenService)
    {
        _userRepository = userRepository;
        _jwtTokenService = jwtTokenService;
    }

    public async Task<Response<LoginResponseDto>> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        // First try to find user by email
        var user = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);

        // If user not found by email, try by username
        user ??= await _userRepository.GetByUsernameAsync(request.Email, cancellationToken);

        // If user still not found, throw exception
        if (user == null)
        {
            throw new InvalidCredentialsException();
        }

        // Check if user is active
        if (!user.IsActive)
        {
            throw new InvalidCredentialsException(); // We could have a separate exception here
        }

        // Verify password using BCrypt
        bool isPasswordValid = BCrypt.Verify(request.Password, user.PasswordHash);
        if (!isPasswordValid)
        {
            throw new InvalidCredentialsException();
        }

        // Generate tokens
        string accessToken = await _jwtTokenService.GenerateAccessToken(user);
        string refreshToken = _jwtTokenService.GenerateRefreshToken();
        DateTime refreshTokenExpiryTime = _jwtTokenService.GetRefreshTokenExpiryTime();

        // Update user with refresh token
        await _userRepository.UpdateRefreshTokenAsync(
            user.Id,
            refreshToken,
            refreshTokenExpiryTime,
            cancellationToken);

        // Update last login timestamp
        await _userRepository.UpdateLastLoginAsync(user.Id, cancellationToken);

        // Create response
        var response = new LoginResponseDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            RefreshTokenExpiryTime = refreshTokenExpiryTime,
            User = new UserDto
            {
                Id = user.Id,
                UserName = user.UserName,
                Email = user.Email ?? string.Empty,
                FullName = user.FullName ?? string.Empty,
                PhoneNumber = user.PhoneNumber ?? string.Empty,
                IsActive = user.IsActive,
                LastLoginAt = user.LastLoginAt,
                RoleName = user.Role?.Name ?? string.Empty
            }
        };

        return new Response<LoginResponseDto>(response);
    }
}
