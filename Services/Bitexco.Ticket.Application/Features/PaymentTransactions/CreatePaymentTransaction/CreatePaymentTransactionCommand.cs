namespace Bitexco.Ticket.Application.Features.PaymentTransactions.CreatePaymentTransaction;

using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Command to create a payment transaction for an order
/// </summary>
public class CreatePaymentTransactionCommand : IRequest<Response<PaymentTransactionResponseDto>>
{
    /// <summary>
    /// Order ID to create payment transaction for
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Payment method for the transaction
    /// </summary>
    public PaymentTransactionMethod PaymentMethod { get; set; }

    /// <summary>
    /// Amount to be paid
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// External transaction code (optional)
    /// </summary>
    public string? TransactionCode { get; set; }
}
