namespace Bitexco.Ticket.Application.Features.PaymentTransactions.CreatePaymentTransaction;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using Bitexco.Ticket.Domain.Models;
using BuildingBlocks.Abstractions;

/// <summary>
/// Handler for creating payment transactions
/// </summary>
public class CreatePaymentTransactionCommandHandler(
    IApplicationDbContext dbContext) : IRequestHandler<CreatePaymentTransactionCommand, Response<PaymentTransactionResponseDto>>
{
    public async Task<Response<PaymentTransactionResponseDto>> Handle(CreatePaymentTransactionCommand request, CancellationToken cancellationToken)
    {
        // Verify order exists and is valid for payment
        var order = await dbContext.Orders
            .Include(o => o.PaymentTransactions)
            .FirstOrDefaultAsync(o => o.Id == request.OrderId, cancellationToken)
            ?? throw new OrderNotFoundException(request.OrderId);

        // Check if order already has a successful payment transaction
        var existingSuccessfulPayment = order.PaymentTransactions?
            .FirstOrDefault(pt => pt.TransactionStatus == PaymentTransactionStatus.success);
        
        if (existingSuccessfulPayment != null)
        {
            throw new DuplicatedDataException();
        }
        
        var paymentTransaction = new PaymentTransaction
        {
            Id = Guid.NewGuid(),
            OrderId = request.OrderId,
            PaymentMethod = request.PaymentMethod,
            Amount = request.Amount,
            PaidAmount = 0,
            TransactionCode = request.TransactionCode,
            TransactionStatus = PaymentTransactionStatus.pending,
            ReconciliationStatus = PaymentReconciliationStatus.pending,
            TransactionAt = DateTime.UtcNow,
            PaidAt = null 
        };
        
        dbContext.PaymentTransactions.Add(paymentTransaction);
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (result == 0)
        {
            throw new NoEntityCreatedException("PaymentTransaction");
        }
        
        var response = paymentTransaction.Adapt<PaymentTransactionResponseDto>();
        
        return new Response<PaymentTransactionResponseDto>(response);
    }
}
