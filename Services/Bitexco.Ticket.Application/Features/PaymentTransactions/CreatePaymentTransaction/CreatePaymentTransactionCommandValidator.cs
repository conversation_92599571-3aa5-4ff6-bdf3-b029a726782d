namespace Bitexco.Ticket.Application.Features.PaymentTransactions.CreatePaymentTransaction;

using FluentValidation;

/// <summary>
/// Validator for CreatePaymentTransactionCommand
/// </summary>
public class CreatePaymentTransactionCommandValidator : AbstractValidator<CreatePaymentTransactionCommand>
{
    public CreatePaymentTransactionCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty()
            .WithMessage("Order ID is required");

        RuleFor(x => x.PaymentMethod)
            .IsInEnum()
            .WithMessage("Invalid payment method");

        RuleFor(x => x.Amount)
            .GreaterThan(0)
            .WithMessage("Amount must be greater than 0");

        RuleFor(x => x.TransactionCode)
            .MaximumLength(255)
            .When(x => !string.IsNullOrWhiteSpace(x.TransactionCode))
            .WithMessage("Transaction code cannot exceed 255 characters");
    }
}
