﻿using Bitexco.Ticket.Application.Features.Orders.ExportOrder;
using BuildingBlocks.FileServices;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bitexco.Ticket.Application.Features.PaymentTransactions.ExportPaymentTransactions
{
    public class ExportPaymentTransactionCommand : ICommand<Response<ExportPaymentTransactionResponse>>
    {
        public string? Search { get; set; }
        public PaymentTransactionType? PaymentTransactionType { get; set; }
        public DateOnly? TransactionDate { get; set; }
    }

    public class ExportPaymentTransactionData
    {
        [Display(Name = "STT")]
        public string? STT { get; set; }

        [Display(Name = "Mã giao dịch")]
        public string? Code { get; set; }

        [Display(Name = "Mã giao dịch tham chiếu")]
        public string? TransactionCode { get; set; }

        [Display(Name = "<PERSON><PERSON>y giao dịch")]
        public DateTime TransactionAt { get; set; }

        [Display(Name = "<PERSON>ênh bán hàng")]
        public string? RetailChannel { get; set; }

        [Display(Name = "Hình thức thanh toán")]
        public string? PaymentMethod { get; set; }

        [Display(Name = "Giá trị")]
        public decimal Amount { get; set; }
    }

    public class ExportPaymentTransactionResponse
    {
        public string? File { get; set; }
    }

    public class ExportPaymentTransactionCommandValidator : AbstractValidator<ExportPaymentTransactionCommand>
    {
        public ExportPaymentTransactionCommandValidator()
        {
            RuleFor(x => x.Search).MaximumLength(100).WithMessage("Search term cannot exceed 100 characters.");
        }
    }

    public class ExportPaymentTransactionCommandHandler(IApplicationDbContext applicationDbContext
      ) : ICommandHandler<ExportPaymentTransactionCommand, Response<ExportPaymentTransactionResponse>>
    {
        private readonly IApplicationDbContext _applicationDbContext = applicationDbContext;

        public async Task<Response<ExportPaymentTransactionResponse>> Handle(ExportPaymentTransactionCommand request, CancellationToken cancellationToken)
        {
            // Logic to export payment transactions based on the command parameters
            var query = _applicationDbContext.PaymentTransactions.AsQueryable();
            if (!string.IsNullOrEmpty(request.Search))
            {
                var searchTerm = request.Search.ToUpper();
                query = query.Where(pt => pt.Code.Contains(searchTerm) || (pt.TransactionCode != null && pt.TransactionCode.Contains(searchTerm)));
            }
            if (request.TransactionDate.HasValue)
            {
                var startDate = request.TransactionDate.Value.ToDateTime(new TimeOnly(0, 0), DateTimeKind.Utc);
                var endDate = request.TransactionDate.Value.ToDateTime(new TimeOnly(23, 59, 59), DateTimeKind.Utc);
                query = query.Where(pt => pt.TransactionAt >= startDate && pt.TransactionAt <= endDate);
            }
            if (request.PaymentTransactionType.HasValue)
            {
                query = query.Where(pt => pt.TransactionType == request.PaymentTransactionType.Value);
            }
            var paymentTransactions = await query.ToListAsync(cancellationToken);
            // Prepare data for export
            var exportData = paymentTransactions.Select((pt, index) => new ExportPaymentTransactionData
            {
                STT = (index + 1).ToString(),
                Code = pt.Code,
                TransactionCode = pt.TransactionCode,
                TransactionAt = pt.TransactionAt,
                RetailChannel = pt.RetailChannel?.Name,
                PaymentMethod = PaymentMethodDescription(pt.PaymentMethod),
                Amount = pt.Amount
            }).ToList();
            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "PaymentTransaction", "PaymentTransactionExportFile.xlsx");

            string path = "exports/payments";
            string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);
            string fileName = $"PaymentExport_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
            string filefolder = Path.Combine(folder, fileName);

            ExcelServices.ExportData(exportData, templatePath, filefolder, 2);
            return new Response<ExportPaymentTransactionResponse>()
            {
                Data = new ExportPaymentTransactionResponse { File = path + "/" + fileName }
            };


        }
        public string PaymentMethodDescription(PaymentTransactionMethod paymentMethod)

        {
            return paymentMethod switch
            {
                PaymentTransactionMethod.cash => "Tiền mặt",
                PaymentTransactionMethod.pos => "POS",
                PaymentTransactionMethod.bank_transfer => "Chuyển khoản ngân hàng",
                PaymentTransactionMethod.vnpay => "VNPay",
                PaymentTransactionMethod.zalopay => "ZaloPay",
                PaymentTransactionMethod.momo => "Momo",
                _ => "Không xác định"
            };
        }
    }

    
    
}