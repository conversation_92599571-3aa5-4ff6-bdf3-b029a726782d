namespace Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactions;

using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Response DTO for payment transactions
/// </summary>
public record GetPaymentTransactionResponse : PaymentTransactionResponseDto
{
    public OrderResponseDto? Order { get; set; }
    public RetailChannelResponseDto? RetailChannel { get; set; }
}

/// <summary>
/// Query to get payment transactions
/// </summary>
public record GetPaymentTransactionsQuery : IRequest<PaginationResponse<GetPaymentTransactionResponse>>, IPaginationRequest, ISortRequest
{
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 100;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool? IsDescending { get; set; } = false;

    public string? Search { get; set; }
    public PaymentTransactionType? PaymentTransactionType { get; set; }
    public DateOnly? TransactionDate { get; set; }
}
