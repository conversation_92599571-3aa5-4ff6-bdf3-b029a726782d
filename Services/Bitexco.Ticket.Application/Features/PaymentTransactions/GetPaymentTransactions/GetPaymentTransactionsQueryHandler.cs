namespace Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactions;

using Bitexco.Ticket.Application.Data;
using BuildingBlocks.Abstractions;

/// <summary>
/// Handler for getting payment transaction by order ID
/// </summary>
public class GetPaymentTransactionsQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetPaymentTransactionsQuery, PaginationResponse<GetPaymentTransactionResponse>>
{
    public async Task<PaginationResponse<GetPaymentTransactionResponse>> Handle(GetPaymentTransactionsQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.PaymentTransactions
            .AsNoTracking()
            .Include(pt => pt.RetailChannel)
            .Include(pt => pt.Order)
            .AsQueryable();

        if (!string.IsNullOrEmpty(request.Search))
        {
            request.Search = request.Search.Trim().ToUpper();
            query = query.Where(pt =>
                (pt.Order != null && pt.Order.Code.Contains(request.Search)) ||
                pt.Code.Contains(request.Search) ||
                (pt.TransactionCode != null && pt.TransactionCode.Contains(request.Search)));
        }

        if (request.TransactionDate.HasValue)
        {
            var startDate = request.TransactionDate.Value.ToDateTime(new TimeOnly(0, 0), DateTimeKind.Utc);
            var endDate = request.TransactionDate.Value.ToDateTime(new TimeOnly(23, 59, 59), DateTimeKind.Utc);
            query = query.Where(pt => pt.TransactionAt >= startDate && pt.TransactionAt <= endDate);
        }

        if (request.PaymentTransactionType.HasValue)
        {
            query = query.Where(pt => pt.TransactionType == request.PaymentTransactionType.Value);
        }

        // total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // apply sorting
        query = request.SortBy?.ToLower() switch
        {
            "transactionat" => request.IsDescending == true
                ? query.OrderByDescending(pt => pt.TransactionAt)
                : query.OrderBy(pt => pt.TransactionAt),
            "retailchannel" => request.IsDescending == true
                ? query.OrderByDescending(pt => pt.RetailChannel!.Name)
                : query.OrderBy(pt => pt.RetailChannel!.Name),
            "paymentmethod" => request.IsDescending == true
                ? query.OrderByDescending(pt => pt.PaymentMethod)
                : query.OrderBy(pt => pt.PaymentMethod),
            "amount" => request.IsDescending == true
                ? query.OrderByDescending(pt => pt.Amount)
                : query.OrderBy(pt => pt.Amount),
            _ => query.OrderByDescending(pt => pt.TransactionAt) // default sort by TransactionAt descending
        };

        // apply pagination
        var paymentTransactions = await query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ProjectToType<GetPaymentTransactionResponse>()
            .ToListAsync(cancellationToken);

        return new PaginationResponse<GetPaymentTransactionResponse>(request.PageIndex, request.PageSize, totalCount, paymentTransactions);
    }
}
