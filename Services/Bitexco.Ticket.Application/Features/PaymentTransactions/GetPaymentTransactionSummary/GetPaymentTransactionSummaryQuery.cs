namespace Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactionSummary;

using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Response DTO for payment transactions summary
/// </summary>
public record GetPaymentTransactionSummaryResponse
{
    public int TotalIncomingTransactions { get; set; }
    public int TotalOutgoingTransactions { get; set; }
    public decimal TotalIncomingAmount { get; set; }
    public decimal TotalOutgoingAmount { get; set; }
}

/// <summary>
/// Query to get payment transactions
/// </summary>
public record GetPaymentTransactionSummaryQuery : IRequest<Response<GetPaymentTransactionSummaryResponse>>;
