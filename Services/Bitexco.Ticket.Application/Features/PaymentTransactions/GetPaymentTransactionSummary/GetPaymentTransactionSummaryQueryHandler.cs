namespace Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactionSummary;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Handler for getting payment transaction by order ID
/// </summary>
public class GetPaymentTransactionSummaryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetPaymentTransactionSummaryQuery, Response<GetPaymentTransactionSummaryResponse>>
{
    public async Task<Response<GetPaymentTransactionSummaryResponse>> Handle(GetPaymentTransactionSummaryQuery request, CancellationToken cancellationToken)
    {
        //TotalIncomingTransactions, TotalOutgoingTransactions, TotalIncomingAmount, TotalOutgoingAmount
        var summary = await dbContext.PaymentTransactions
            .AsNoTracking()
            .Where(pt => pt.TransactionStatus == PaymentTransactionStatus.success)
            .GroupBy(pt => pt.TransactionType)
            .Select(g => new
            {
                TransactionType = g.Key,
                Count = g.Count(),
                Amount = g.Sum(pt => pt.Amount)
            })
            .ToListAsync(cancellationToken);

        var summaryResponse = new GetPaymentTransactionSummaryResponse
        {
            TotalIncomingTransactions = summary.Where(s => s.TransactionType == PaymentTransactionType.income).Select(s => s.Count).FirstOrDefault(),
            TotalOutgoingTransactions = summary.Where(s => s.TransactionType == PaymentTransactionType.expense).Select(s => s.Count).FirstOrDefault(),
            TotalIncomingAmount = summary.Where(s => s.TransactionType == PaymentTransactionType.income).Select(s => s.Amount).FirstOrDefault(),
            TotalOutgoingAmount = summary.Where(s => s.TransactionType == PaymentTransactionType.expense).Select(s => s.Amount).FirstOrDefault()
        };

        return new Response<GetPaymentTransactionSummaryResponse>(summaryResponse);
    }
}
