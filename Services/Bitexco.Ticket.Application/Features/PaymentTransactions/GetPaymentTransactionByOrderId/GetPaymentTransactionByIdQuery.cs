namespace Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactionByOrderId;

using Bitexco.Ticket.Application.Dtos;
using BuildingBlocks.Abstractions;

/// <summary>
/// Response DTO for payment transaction query
/// </summary>
public record PaymentTransactionStatusResponse : PaymentTransactionResponseDto
{
    
}

/// <summary>
/// Query to get payment transaction status by order ID
/// </summary>
public record GetPaymentTransactionByIdQuery(Guid Id) : IRequest<Response<PaymentTransactionStatusResponse>>;
