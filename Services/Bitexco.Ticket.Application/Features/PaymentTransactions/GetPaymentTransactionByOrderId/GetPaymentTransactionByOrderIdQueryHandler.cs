namespace Bitexco.Ticket.Application.Features.PaymentTransactions.GetPaymentTransactionByOrderId;

using Bitexco.Ticket.Application.Data;
using BuildingBlocks.Abstractions;

/// <summary>
/// Handler for getting payment transaction by order ID
/// </summary>
public class GetPaymentTransactionByOrderIdQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetPaymentTransactionByIdQuery, Response<PaymentTransactionStatusResponse>>
{
    public async Task<Response<PaymentTransactionStatusResponse>> Handle(GetPaymentTransactionByIdQuery request, CancellationToken cancellationToken)
    {
        var paymentTransaction = await dbContext.PaymentTransactions
            .AsNoTracking()
            .FirstOrDefaultAsync(pt => pt.Id == request.Id, cancellationToken)
        ?? throw new PaymentTransactionNotFoundException(request.Id);

        return new Response<PaymentTransactionStatusResponse>(
            paymentTransaction.Adapt<PaymentTransactionStatusResponse>());
    }
}
