namespace Bitexco.Ticket.Application.Features.Agents.Queries.GetFilteredAgents;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Query to get paginated list of agents with optional filtering
/// </summary>
public class GetFilteredAgentsQuery : IRequest<PaginationResponse<AgentResponseDto>>, IPaginationRequest, ISortRequest
{
    /// <summary>
    /// Filter by agent name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by tax code
    /// </summary>
    public string? TaxCode { get; set; }

    /// <summary>
    /// Filter by approval status
    /// </summary>
    public AgentApproveStatus? Status { get; set; }

    /// <summary>
    /// Filter by creation date range - start date
    /// </summary>
    public DateOnly? CreatedFromDate { get; set; }

    /// <summary>
    /// Filter by creation date range - end date
    /// </summary>
    public DateOnly? CreatedToDate { get; set; }

    /// <summary>
    /// Agent type filter
    /// </summary>
    public AgentType? AgentType { get; set; }

    /// <summary>
    /// The page index for pagination
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// The page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 100;

    /// <summary>
    /// The sort order for the results
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// The sort direction (asc or desc)
    /// </summary>
    public bool? IsDescending { get; set; }
}

/// <summary>
/// Handler for GetAgentsQuery
/// </summary>
public class GetFilteredAgentsQueryHandler(IApplicationDbContext context) : IRequestHandler<GetFilteredAgentsQuery, PaginationResponse<AgentResponseDto>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<PaginationResponse<AgentResponseDto>> Handle(GetFilteredAgentsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Agents
            .Include(a => a.ParentAgent)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(request.Name))
        {
            var requestName = request.Name.Trim().ToLower();
            query = query.Where(a => a.Name.ToLower().Contains(requestName)
                || a.Code.ToLower().Contains(requestName)
                || (!string.IsNullOrWhiteSpace(a.PhoneNumber) && a.PhoneNumber.ToLower().Contains(requestName)));
        }

        if (!string.IsNullOrWhiteSpace(request.TaxCode))
        {
            query = query.Where(a => a.TaxCode != null && a.TaxCode.Contains(request.TaxCode));
        }

        if (request.Status.HasValue)
        {
            query = query.Where(a => a.ApprovalStatus == request.Status.Value);
        }

        if (request.CreatedFromDate.HasValue)
        {
            var fromDate = request.CreatedFromDate.Value.ToDateTime(TimeOnly.MinValue);
            query = query.Where(a => a.CreatedAt >= fromDate);
        }

        if (request.CreatedToDate.HasValue)
        {
            var toDate = request.CreatedToDate.Value.ToDateTime(TimeOnly.MaxValue);
            query = query.Where(a => a.CreatedAt <= toDate);
        }

        if (request.AgentType.HasValue)
        {
            query = query.Where(a => a.Type == request.AgentType.Value);
        }

        //total count for pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        //order by
        query = (request.SortBy?.ToLower()) switch
        {
            "address" => request.IsDescending == true ? query.OrderByDescending(a => a.Address) : query.OrderBy(a => a.Address),
            "taxcode" => request.IsDescending == true ? query.OrderByDescending(a => a.TaxCode) : query.OrderBy(a => a.TaxCode),
            _ => request.IsDescending == true ? query.OrderByDescending(a => a.Name) : query.OrderBy(a => a.Name),
        };

        // Apply pagination
        query = query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize);


        // Apply pagination and project to DTO
        var agents = await query
            .AsNoTracking()
            .ProjectToType<AgentResponseDto>()
            .ToListAsync(cancellationToken);

        return new PaginationResponse<AgentResponseDto>(request.PageIndex, request.PageSize, totalCount, agents);
    }
}
