namespace Bitexco.Ticket.Application.Features.Agents.Queries.GetAgentById;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Features.Agents.Dtos;
using BuildingBlocks.Abstractions;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// Query to get detailed information for a specific agent
/// </summary>
public class GetAgentByIdQuery : IRequest<Response<AgentResponseDetailDto>>
{
    /// <summary>
    /// ID of the agent to retrieve
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for GetAgentByIdQuery
/// </summary>
public class GetAgentByIdQueryHandler(IApplicationDbContext context) : IRequestHandler<GetAgentByIdQuery, Response<AgentResponseDetailDto>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<Response<AgentResponseDetailDto>> Handle(GetAgentByIdQuery request, CancellationToken cancellationToken)
    {
        var agent = await _context.Agents
            .Include(a => a.ParentAgent)
            .Include(a => a.ChildAgents)
            .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken) ?? throw new AgentFoundException(request.Id);
        var agentDto = agent.Adapt<AgentResponseDetailDto>();

        // Map parent agent if exists
        if (agent.ParentAgent != null)
        {
            agentDto.ParentAgent = new AgentParentDto
            {
                Id = agent.ParentAgent.Id,
                Name = agent.ParentAgent.Name
            };
        }

        // Map child agents if exist
        if (agent.ChildAgents != null && agent.ChildAgents.Count != 0)
        {
            agentDto.ChildAgents = [.. agent.ChildAgents
                .Select(c => new AgentChildDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    ApprovalStatus = c.ApprovalStatus,
                    IsActive = c.IsActive
                })];
        }

        return new Response<AgentResponseDetailDto>(agentDto);
    }
}
