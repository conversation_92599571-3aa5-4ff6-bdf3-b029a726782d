namespace Bitexco.Ticket.Application.Features.Agents.Queries.GetAgents;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Query to get paginated list of agents with optional filtering
/// </summary>
public class GetAgentsQuery : IRequest<Response<List<AgentResponseDto>>>
{
    /// <summary>
    /// Filter by agent name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by tax code
    /// </summary>
    public string? TaxCode { get; set; }

    /// <summary>
    /// Filter by approval status
    /// </summary>
    public AgentApproveStatus? Status { get; set; }

    /// <summary>
    /// Filter by creation date range - start date
    /// </summary>
    public DateOnly? CreatedFromDate { get; set; }

    /// <summary>
    /// Filter by creation date range - end date
    /// </summary>
    public DateOnly? CreatedToDate { get; set; }

    /// <summary>
    /// Agent type filter
    /// </summary>
    public AgentType? AgentType { get; set; }
}

/// <summary>
/// Handler for GetAgentsQuery
/// </summary>
public class GetAgentsQueryHandler(IApplicationDbContext context) : IRequestHandler<GetAgentsQuery, Response<List<AgentResponseDto>>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<Response<List<AgentResponseDto>>> Handle(GetAgentsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Agents.AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(request.Name))
        {
            query = query.Where(a => a.Name.Contains(request.Name));
        }

        if (!string.IsNullOrWhiteSpace(request.TaxCode))
        {
            query = query.Where(a => a.TaxCode != null && a.TaxCode.Contains(request.TaxCode));
        }

        if (request.Status.HasValue)
        {
            query = query.Where(a => a.ApprovalStatus == request.Status.Value);
        }

        if (request.CreatedFromDate.HasValue)
        {
            var fromDate = request.CreatedFromDate.Value.ToDateTime(TimeOnly.MinValue);
            query = query.Where(a => a.CreatedAt >= fromDate);
        }

        if (request.CreatedToDate.HasValue)
        {
            var toDate = request.CreatedToDate.Value.ToDateTime(TimeOnly.MaxValue);
            query = query.Where(a => a.CreatedAt <= toDate);
        }

        if (request.AgentType.HasValue)
        {
            query = query.Where(a => a.Type == request.AgentType.Value);
        }

        // Include parent agent for display
        query = query.Include(a => a.ParentAgent);

        // Apply pagination and project to DTO
        var agents = await query
            .AsNoTracking()
            .OrderBy(a => a.Name)
            .ProjectToType<AgentResponseDto>()
            .ToListAsync(cancellationToken);

        return new Response<List<AgentResponseDto>>(agents);
    }
}
