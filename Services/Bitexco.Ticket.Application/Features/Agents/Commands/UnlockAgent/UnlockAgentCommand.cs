namespace Bitexco.Ticket.Application.Features.Agents.Commands.UnlockAgent;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;
using System;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// Command to unlock (reactivate) a suspended agent account
/// </summary>
public class UnlockAgentCommand : IRequest<Response<object>>
{
    /// <summary>
    /// ID of the agent to unlock
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for UnlockAgentCommand
/// </summary>
public class UnlockAgentCommandHandler(IApplicationDbContext context) : IRequestHandler<UnlockAgentCommand, Response<object>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<Response<object>> Handle(UnlockAgentCommand request, CancellationToken cancellationToken)
    {
        var agent = await _context.Agents
            .AsTracking()
            .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken)
            ?? throw new AgentFoundException(request.Id);

        // Validate agent is currently suspended
        if (agent.ApprovalStatus != AgentApproveStatus.suspended)
        {
            throw new AccountLockedException(request.Id);
        }

        // Reactivate the agent account
        agent.ApprovalStatus = AgentApproveStatus.approved;
        agent.SuspensionReason = null;
        agent.UpdatedAt = DateTime.UtcNow;

        var result = await _context.SaveChangesAsync(cancellationToken);

        return new Response<object>(true);
    }
}
