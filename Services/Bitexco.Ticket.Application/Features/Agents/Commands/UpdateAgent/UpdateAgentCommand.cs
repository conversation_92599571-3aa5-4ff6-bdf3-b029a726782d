namespace Bitexco.Ticket.Application.Features.Agents.Commands.UpdateAgent;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Agents.Dtos;
using BuildingBlocks.Abstractions;

/// <summary>
/// Command to update an existing agent
/// </summary>
public class UpdateAgentCommand : IRequest<Response<AgentResponseDetailDto>>
{
    /// <summary>
    /// ID of the agent to update
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Updated agent data
    /// </summary>
    public AgentDto Agent { get; set; } = null!;
}

/// <summary>
/// Handler for UpdateAgentCommand
/// </summary>
public class UpdateAgentCommandHandler(IApplicationDbContext context) : IRequestHandler<UpdateAgentCommand, Response<AgentResponseDetailDto>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<Response<AgentResponseDetailDto>> Handle(UpdateAgentCommand request, CancellationToken cancellationToken)
    {
        // Find the agent
        var agent = await _context.Agents.FindAsync([request.Id], cancellationToken)
            ?? throw new AgentFoundException(request.Id);

        // Validate parent agent exists if provided
        if (request.Agent.ParentAgentId.HasValue)
        {
            // Prevent circular reference (agent can't be its own parent)
            if (request.Agent.ParentAgentId.Value == agent.Id)
            {
                throw new AgentFoundException("An agent cannot be its own parent.");
            }

            var parentExists = await _context.Agents.AnyAsync(a => a.Id == request.Agent.ParentAgentId.Value, cancellationToken);
            if (!parentExists)
            {
                throw new AgentFoundException(request.Agent.ParentAgentId.Value);
            }
        }

        // Update properties
        agent.Name = request.Agent.Name;
        agent.ContactPerson = request.Agent.ContactPerson;
        agent.TaxCode = request.Agent.TaxCode;
        agent.PhoneNumber = request.Agent.PhoneNumber;
        agent.Email = request.Agent.Email;
        agent.Address = request.Agent.Address;
        agent.TicketSaleLimit = request.Agent.TicketSaleLimit;
        agent.MonthlyTicketLimit = request.Agent.MonthlyTicketLimit;
        agent.ParentAgentId = request.Agent.ParentAgentId;
        agent.BankName = request.Agent.BankName;
        agent.BankAccountName = request.Agent.BankAccountName;
        agent.BankAccountNumber = request.Agent.BankAccountNumber;

        // Save changes
        var result = await _context.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new DbUpdateException("Failed to update agent.");
        }

        // Map to DTO for response
        var agentDto = agent.Adapt<AgentResponseDetailDto>();
        
        // Map parent agent if exists
        if (agent.ParentAgent != null)
        {
            agentDto.ParentAgent = new AgentParentDto
            {
                Id = agent.ParentAgent.Id,
                Name = agent.ParentAgent.Name
            };
        }

        // Map child agents if exist
        if (agent.ChildAgents != null && agent.ChildAgents.Count > 0)
        {
            foreach (var childAgent in agent.ChildAgents)
            {
                agentDto.ChildAgents.Add(new AgentChildDto
                {
                    Id = childAgent.Id,
                    Name = childAgent.Name,
                    ApprovalStatus = childAgent.ApprovalStatus,
                    IsActive = childAgent.IsActive
                });
            }
        }

        return new Response<AgentResponseDetailDto>(agentDto);
    }
}
