namespace Bitexco.Ticket.Application.Features.Agents.Commands.CreateAgent;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Agents.Dtos;
using Bitexco.Ticket.Domain.Models;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Utils;

/// <summary>
/// Command to create a new agent
/// </summary>
public class CreateAgentCommand : IRequest<Response<AgentResponseDetailDto>>
{
    /// <summary>
    /// Agent creation data
    /// </summary>
    public AgentDto Agent { get; set; } = null!;
}

/// <summary>
/// Handler for CreateAgentCommand
/// </summary>
public class CreateAgentCommandHandler(IApplicationDbContext context) : IRequestHandler<CreateAgentCommand, Response<AgentResponseDetailDto>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<Response<AgentResponseDetailDto>> Handle(CreateAgentCommand request, CancellationToken cancellationToken)
    {
        // Validate parent agent exists if provided
        if (request.Agent.ParentAgentId.HasValue)
        {
            var parentExists = await _context.Agents.AnyAsync(a => a.Id == request.Agent.ParentAgentId.Value, cancellationToken);
            if (!parentExists)
            {
                throw new AgentFoundException(request.Agent.ParentAgentId.Value);
            }
        }

        // Map to domain entity
        var agent = new Agent
        {
            Code = request.Agent.Code ?? $"AG-{CommonUtils.GenerateNewCode(7)}", // Generate code if not provided
            Name = request.Agent.Name,
            ContactPerson = request.Agent.ContactPerson,
            TaxCode = request.Agent.TaxCode,
            PhoneNumber = request.Agent.PhoneNumber,
            Email = request.Agent.Email,
            Address = request.Agent.Address,
            TicketSaleLimit = request.Agent.TicketSaleLimit,
            MonthlyTicketLimit = request.Agent.MonthlyTicketLimit,
            ParentAgentId = request.Agent.ParentAgentId,
            ApprovalStatus = Domain.Enums.AgentApproveStatus.pending, // New agents start with pending status
            IsActive = true, // New agents are active by default
        };

        // Add to database
        await _context.Agents.AddAsync(agent, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        // Map to DTO for response
        var agentDto = agent.Adapt<AgentResponseDetailDto>();
        
        // Map parent agent if exists
        if (agent.ParentAgent != null)
        {
            agentDto.ParentAgent = new AgentParentDto
            {
                Id = agent.ParentAgent.Id,
                Name = agent.ParentAgent.Name
            };
        }

        return new Response<AgentResponseDetailDto>(agentDto);
    }
}
