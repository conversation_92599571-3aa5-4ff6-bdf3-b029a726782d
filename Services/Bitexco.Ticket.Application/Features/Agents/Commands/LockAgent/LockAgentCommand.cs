namespace Bitexco.Ticket.Application.Features.Agents.Commands.LockAgent;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// Command to lock (suspend) an agent account
/// </summary>
public class LockAgentCommand : IRequest<Response<object>>
{
    /// <summary>
    /// ID of the agent to lock
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Reason for locking the agent account
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// Handler for LockAgentCommand
/// </summary>
public class LockAgentCommandHandler : IRequestHandler<LockAgentCommand, Response<object>>
{
    private readonly IApplicationDbContext _context;

    public LockAgentCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Response<object>> Handle(LockAgentCommand request, CancellationToken cancellationToken)
    {
        var agent = await _context.Agents
            .AsTracking()
            .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken)
            ?? throw new AgentFoundException(request.Id);

        // Validate not already suspended
        if (agent.ApprovalStatus == AgentApproveStatus.suspended)
        {
            throw new AccountLockedException(request.Id);
        }

        // Lock the agent account
        agent.ApprovalStatus = AgentApproveStatus.suspended;
        agent.SuspensionReason = request.Reason;
        agent.UpdatedAt = DateTime.UtcNow;

        // Update any agent API keys to inactive
        var apiKeys = await _context.AgentApiKeys.Where(k => k.AgentId == agent.Id).ToListAsync(cancellationToken);
        foreach (var key in apiKeys)
        {
            key.IsActive = false;
            key.UpdatedAt = DateTime.UtcNow;
        }

        var result = await _context.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new DbUpdateException("Failed to lock agent account.");
        }

        return new Response<object>(true);
    }
}
