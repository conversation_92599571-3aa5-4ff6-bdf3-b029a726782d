namespace Bitexco.Ticket.Application.Features.Agents.Commands.DeleteAgent;

using Bitexco.Ticket.Application.Data;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// Command to delete (soft delete) an agent
/// </summary>
public class DeleteAgentCommand : IRequest<Response<object>>
{
    /// <summary>
    /// ID of the agent to delete
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for DeleteAgentCommand
/// </summary>
public class DeleteAgentCommandHandler(IApplicationDbContext context) : IRequestHandler<DeleteAgentCommand, Response<object>>
{
    private readonly IApplicationDbContext _context = context;

    public async Task<Response<object>> Handle(DeleteAgentCommand request, CancellationToken cancellationToken)
    {
        var agent = await _context.Agents
            .AsTracking()
            .Include(a => a.ChildAgents)
            .Include(a => a.Orders)
            .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken)
        ?? throw new AgentFoundException(request.Id);

        // Check if agent has any child agents
        if (agent.ChildAgents != null && agent.ChildAgents.Count > 0)
        {
            throw new BadRequestException("Cannot delete agent with child agents. Please delete child agents first.");
        }

        // Check if agent has any associated orders
        if (agent.Orders != null && agent.Orders.Count > 0)
        {
            throw new BadRequestException("Cannot delete agent with associated orders. Please contact administrator for assistance.");
        }

        // Soft delete the agent
        agent.IsDeleted = true;

        var result = await _context.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityDeletedException("Agent", request.Id);
        }

        return new Response<object>(true);
    }
}
