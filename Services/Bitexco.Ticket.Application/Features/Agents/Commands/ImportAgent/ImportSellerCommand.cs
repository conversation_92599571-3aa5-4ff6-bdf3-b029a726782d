﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.FileServices;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Bitexco.Ticket.Application.Features.Agents.Commands.ImportAgent
{
    public class ImportSellerCommand : ICommand<Response<List<AgentResponseDto>>>
    {
        public IFormFile FilePath { get; set; } = null!;
    }

    public class ImportSellerValidation : AbstractValidator<ImportSellerCommand>
    {
        public ImportSellerValidation()
        {
            RuleFor(x => x.FilePath)
                .NotNull()
                .WithMessage("File path cannot be null.")
                .NotEmpty()
                .WithMessage("File path cannot be empty.")
                  .Must(x => ExcelServices.IsValidFileExtension(x.FileName))
            .WithMessage("File must have a valid extension: .xls or .xlsx.");
        }
    }

    public class ImportSellerData
    {
        [Display(Name = "STT")]
        public int STT { get; set; }

        [Display(Name = "Tên cộng tác viên")]
        public string? SellerName { get; set; }

        [Display(Name = "Mã cộng tác viên")]
        public string? SellerCode { get; set; }

        [Display(Name = "Hoa hồng (%)")]
        public decimal CommissionRate { get; set; }

        [Display(Name = "Số điện thoại")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "Địa chỉ")]
        public string? Address { get; set; }

        [Display(Name = "Tên ngân hàng")]
        public string? BankName { get; set; }
        [Display(Name = "Tên tài khoản")]
        public string? BankAccountName { get; set; }
        [Display(Name = "Số tài khoản")]
        public string? BankAccountNumber { get; set; }
    }

    public class ImportSellerCommandHandler(IApplicationDbContext context)
        : ICommandHandler<ImportSellerCommand, Response<List<AgentResponseDto>>>
    {
        private readonly IApplicationDbContext _context = context;

        public async Task<Response<List<AgentResponseDto>>> Handle(ImportSellerCommand request, CancellationToken cancellationToken)
        {
            if (request.FilePath == null || request.FilePath.Length == 0)
            {
                throw new ArgumentException("File path cannot be null or empty.");
            }
            var mappings = ExcelServices.GetPropertyMappings<ImportSellerData>();

            var agents = await ExcelServices.ImportFile<ImportSellerData>(request.FilePath, mappings,"data");

            var existingAgent = _context.Agents
                   .Where(a => agents.Select(x => x.SellerCode).Contains(a.Code)
                   && a.ApprovalStatus == AgentApproveStatus.approved).AsEnumerable();

            if (existingAgent.Count() > 0)
                throw new AgentExitstingException(string.Join(";", existingAgent.Select(x => x.Code)));

            var newAgents = new List<Agent>();
            foreach (var agent in agents)
            {
                // Validate and add each agent

                var newAgent = new Agent
                {
                    Address = agent.Address,
                    Code = agent.SellerCode!,
                    Name = agent.SellerName!,
                    PhoneNumber = agent.PhoneNumber,
                    CommissionRatePercentage = agent.CommissionRate,
                    Type = AgentType.collaborator,
                    IsActive = true,
                    ApprovalStatus = AgentApproveStatus.approved,
                    BankName = agent.BankName,
                    BankAccountName = agent.BankAccountName,
                    BankAccountNumber = agent.BankAccountNumber,


                };
                newAgents.Add(newAgent);
            }
            _context.Agents.AddRange(newAgents);

            var result = await _context.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new NoEntityCreatedException("Agents");
            }

            // return data agents
            var dataResult = newAgents.Select(a => new AgentResponseDto
            {
                Id = a.Id,
                Name = a.Name,
                Code = a.Code,
                IsActive = a.IsActive,
                ApprovalStatus = a.ApprovalStatus,
                Type = a.Type,
                CreatedAt = a.CreatedAt,
                ParentAgentName = a.ParentAgent?.Name,
                ContactPerson = a.ContactPerson,
                ContactPosition = a.ContactPosition,
                PhoneNumber = a.PhoneNumber,
                Email = a.Email,
                TaxCode = a.TaxCode,
                Address = a.Address,
                CommissionRatePercentage = a.CommissionRatePercentage,
                FixedCommissionAmount = a.FixedCommissionAmount,
                SapCode = a.SapCode,
                IsDebtAllowed = a.IsDebtAllowed,
                BankName = a.BankName,
                BankAccountName = a.BankAccountName,
                BankAccountNumber = a.BankAccountNumber,


            }).ToList();

            return new Response<List<AgentResponseDto>>(dataResult);
        }
    }
}