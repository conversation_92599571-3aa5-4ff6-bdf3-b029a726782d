﻿using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.FileServices;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Bitexco.Ticket.Application.Features.Agents.Commands.ImportAgent
{
    public class ImportAgentCommand : ICommand<Response<List<AgentResponseDto>>>
    {
        public IFormFile FilePath { get; set; } = null!; // Required file for import
    }

    public class ImportAgentData
    {
        [Display(Name = "STT")]
        public int STT { get; set; }

        [Display(Name = "Mã đại lý")]
        public string? AgentCode { get; set; }

        [Display(Name = "Mã số thuế")]
        public string? TaxCode { get; set; }

        [Display(Name = "Mã SAP")]
        public string? SapCode { get; set; }

        [Display(Name = "Tên đại lý")]
        public string? AgentName { get; set; }

        [Display(Name = "Địa chỉ")]
        public string? Address { get; set; }

        [Display(Name = "Số điện thoại")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "Vị trí")]
        public string? ContactPosition { get; set; }

        [Display(Name = "Người liên hệ")]
        public string? ContactPerson { get; set; }

        [Display(Name = "Hoa hồng")]
        public decimal CommissionRate { get; set; }

        [Display(Name = "Cho phép ghi nhận bằng hình thức công nợ trên máy POS")]
        public string? AllowDebtRecognitionOnPOS { get; set; }

        [Display(Name = "Chiết khấu thẳng giá vé thay vì ghi nhận hoa hồng")]
        public string? IsDirectTicketDiscountInsteadOfCommission { get; set; }

        [Display(Name = "Tên ngân hàng")]
        public string? BankName { get; set; }
        [Display(Name = "Tên tài khoản")]
        public string? BankAccountName { get; set; }
        [Display(Name = "Số tài khoản")]
        public string? BankAccountNumber { get; set; }
    }

    public class ImportAgentValidation : AbstractValidator<ImportAgentCommand>
    {
        public ImportAgentValidation()
        {
            RuleFor(x => x.FilePath)
                .NotNull()
                .WithMessage("File path cannot be null.")
                .NotEmpty()
                .WithMessage("File path cannot be empty.")
                .Must(x => ExcelServices.IsValidFileExtension(x.FileName))
                .WithMessage("File must have a valid extension: .xls or .xlsx.");
        }
    }

    public class ImportAgentCommandHandler(IApplicationDbContext context)
        : ICommandHandler<ImportAgentCommand, Response<List<AgentResponseDto>>>
    {
        private readonly IApplicationDbContext _context = context;

        public async Task<Response<List<AgentResponseDto>>> Handle(ImportAgentCommand request, CancellationToken cancellationToken)
        {
            var mappings = ExcelServices.GetPropertyMappings<ImportAgentData>();
            var readDatas = await ExcelServices.ImportFile<ImportAgentData>(request.FilePath!, mappings, "data");

            var existingAgent = _context.Agents
                   .Where(a => readDatas.Select(x => x.AgentCode).Contains(a.Code)
                   && a.ApprovalStatus == AgentApproveStatus.approved).AsEnumerable();

            if (existingAgent.Count() > 0)
                throw new AgentExitstingException(string.Join(";", existingAgent.Select(x => x.Code)));

            var agents = new List<Agent>();
            foreach (var data in readDatas)
            {
                agents.Add(new Agent
                {
                    Address = data.Address,
                    Code = data.AgentCode!,
                    Name = data.AgentName!,
                    PhoneNumber = data.PhoneNumber,
                    ContactPosition = data.ContactPosition,
                    ContactPerson = data.ContactPerson,
                    CommissionRatePercentage = data.CommissionRate,
                    TaxCode = data.TaxCode,
                    SapCode = data.SapCode,
                    Type = AgentType.agent,
                    IsActive = true,
                    ApprovalStatus = AgentApproveStatus.approved,
                    IsDebtAllowed = data.AllowDebtRecognitionOnPOS == "X" ? true : false,
                    IsDirectDiscount = data.IsDirectTicketDiscountInsteadOfCommission == "X" ? true : false,
                    BankName = data.BankName,
                    BankAccountName = data.BankAccountName,
                    BankAccountNumber = data.BankAccountNumber,


                });
            }
            _context.Agents.AddRange(agents);
            var result = await _context.SaveChangesAsync(cancellationToken);

            // Process the imported data and convert to AgentResponseDto
            var resultData = agents.Select(data => new AgentResponseDto
            {
                IsDebtAllowed = data.IsDebtAllowed,
                IsDirectDiscount = data.IsDirectDiscount,
                Address = data.Address,
                Code = data.Code,
                ContactPerson = data.ContactPerson,
                ContactPosition = data.ContactPosition,
                CommissionRatePercentage = data.CommissionRatePercentage,
                Id = data.Id,
                Name = data.Name,
                PhoneNumber = data.PhoneNumber,
                SapCode = data.SapCode,
                TaxCode = data.TaxCode,
                IsActive = data.IsActive,
                ApprovalStatus = data.ApprovalStatus,
                BankName = data.BankName,
                BankAccountName = data.BankAccountName,
                BankAccountNumber = data.BankAccountNumber,

            }).ToList();

            return result > 0
                ? new Response<List<AgentResponseDto>>(resultData)
                : throw new NoEntityCreatedException("Agents");
        }
    }
}