﻿using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.FileServices;
using System.ComponentModel.DataAnnotations;

namespace Bitexco.Ticket.Application.Features.Agents.Commands.ExportAgent
{
    public class ExportAgentCommand : ICommand<Response<ExportAgentCommandResponse>>
    {
    }

    public class ExportAgentCommandResponse
    {
        public string? file { get; set; }
    }

    public class ExportAgentData
    {
        [Display(Name = "STT")]
        public int STT { get; set; }

        [Display(Name = "Mã đại lý")]
        public string? AgentCode { get; set; }

        [Display(Name = "Mã số thuế")]
        public string? TaxCode { get; set; }

        [Display(Name = "Mã SAP")]
        public string? SapCode { get; set; }

        [Display(Name = "Tên đại lý")]
        public string? AgentName { get; set; }

        [Display(Name = "Địa chỉ")]
        public string? Address { get; set; }

        [Display(Name = "Số điện thoại")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "Vị trí")]
        public string? ContactPosition { get; set; }

        [Display(Name = "Người liên hệ")]
        public string? ContactPerson { get; set; }

        [Display(Name = "Hoa hồng")]
        public decimal? CommissionRate { get; set; }

        [Display(Name = "Tên ngân hàng")]
        public string? BankName { get; set; }

        [Display(Name = "Tên tài khoản")]
        public string? BankAccountName { get; set; }

        [Display(Name = "Số tài khoản")]
        public string? BankAccountNumber { get; set; }
    }

    public class ExportAgentCommandHandler(IApplicationDbContext context)
        : ICommandHandler<ExportAgentCommand, Response<ExportAgentCommandResponse>>
    {
        private readonly IApplicationDbContext _context = context;

        public async Task<Response<ExportAgentCommandResponse>> Handle(ExportAgentCommand request, CancellationToken cancellationToken)
        {
            int index = 1;
            var exportData = await _context.Agents
                .Where(a => a.IsActive && a.Type == AgentType.agent
                && a.ApprovalStatus == AgentApproveStatus.approved)
                .ToListAsync(cancellationToken);
            var exportList = exportData.Select(a => new ExportAgentData
            {
                STT = index++,
                AgentCode = a.Code,
                TaxCode = a.TaxCode,
                SapCode = a.SapCode,
                AgentName = a.Name,
                Address = a.Address,
                PhoneNumber = a.PhoneNumber,
                ContactPosition = a.ContactPosition,
                ContactPerson = a.ContactPerson,
                CommissionRate = a.CommissionRatePercentage,
                BankName = a.BankName,
                BankAccountName = a.BankAccountName,
                BankAccountNumber = a.BankAccountNumber
            }).ToList();

            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "Agents", "AgentExportFile.xlsx");
            string path = "exports/agents";
            string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);
            string fileName = $"agent_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx";
            string filefolder = Path.Combine(folder, fileName);

            ExcelServices.ExportData(exportList, templatePath, filefolder, 2);
            return new Response<ExportAgentCommandResponse>
            {
                Data = new ExportAgentCommandResponse { file = path+"/"+fileName }
            };
        }
    }
}