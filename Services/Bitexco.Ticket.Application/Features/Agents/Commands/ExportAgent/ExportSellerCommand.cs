﻿using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.FileServices;
using System.ComponentModel.DataAnnotations;

namespace Bitexco.Ticket.Application.Features.Agents.Commands.ExportAgent
{
    public class ExportSellerCommand : ICommand<Response<ExportSellerCommandResponse>>;

    public class ExportSellerCommandResponse
    {
        public string? file { get; set; }
    }

    public class ExportSellerData
    {
        [Display(Name = "STT")]
        public int STT { get; set; }

        [Display(Name = "Tên cộng tác viên")]
        public string? SellerName { get; set; }

        [Display(Name = "Mã cộng tác viên")]
        public string? SellerCode { get; set; }

        [Display(Name = "Hoa hồng(%)")]
        public decimal? CommissionRate { get; set; }

        [Display(Name = "Số điện thoại")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "Địa chỉ")]
        public string? Address { get; set; }

        [Display(Name = "Tên ngân hàng")]
        public string? BankName { get; set; }

        [Display(Name = "Tên tài khoản")]
        public string? BankAccountName { get; set; }

        [Display(Name = "Số tài khoản")]
        public string? BankAccountNumber { get; set; }
    }

    public class ExportSellerCommandHandler(IApplicationDbContext context)
        : ICommandHandler<ExportSellerCommand, Response<ExportSellerCommandResponse>>
    {
        private readonly IApplicationDbContext _context = context;

        public async Task<Response<ExportSellerCommandResponse>> Handle(ExportSellerCommand request, CancellationToken cancellationToken)
        {
            int index = 1;
            var data = await _context.Agents
               .Where(a => a.IsActive && a.Type == AgentType.collaborator
               && a.ApprovalStatus == AgentApproveStatus.approved)
               .ToListAsync();
            var dataExport = new List<ExportSellerData>();
            data.ForEach(a => dataExport.Add(new ExportSellerData
            {
                STT = index++,
                SellerName = a.Name,
                SellerCode = a.Code,
                CommissionRate = a.CommissionRatePercentage,
                PhoneNumber = a.PhoneNumber,
                BankAccountName = a.BankAccountName,
                BankAccountNumber = a.BankAccountNumber,
                BankName = a.BankName,
            }));

            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "Agents", "SellerExportFile.xlsx");
            string path = "exports/agents";
            string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);

            string fileName = $"seller_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx";
            string filefolder = Path.Combine(folder, $"seller_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx");

            ExcelServices.ExportData(dataExport, templatePath, filefolder, startRow: 2, startCol: 1, headerRow: 1);

            // Logic to export seller data
            var response = new ExportSellerCommandResponse
            {
                file = path + "/" + fileName // Example file path
            };
            return new Response<ExportSellerCommandResponse>(response);
        }
    }
}