namespace Bitexco.Ticket.Application.Features.Agents.Dtos;

using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

public record AgentResponseDetailDto : AgentResponseDto
{
    public AgentParentDto? ParentAgent { get; set; }

    public List<AgentChildDto> ChildAgents { get; set; } = [];
}

public record AgentParentDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public AgentApproveStatus ApprovalStatus { get; set; }
    public bool IsActive { get; set; }
}

public class AgentChildDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public AgentApproveStatus ApprovalStatus { get; set; }
    public bool IsActive { get; set; }
}
