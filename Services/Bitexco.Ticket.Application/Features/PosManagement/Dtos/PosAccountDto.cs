namespace Bitexco.Ticket.Application.Features.PosManagement.Dtos;

/// <summary>
/// Base DTO for POS account - used for update operations (no Id)
/// </summary>
public record PosAccountDto
{
    public PosRoles? PosRole { get; set; }
    public string? Password { get; set; }
    public string? FullName { get; set; }
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
}

/// <summary>
/// Response DTO for POS account - inherits base DTO and adds display fields
/// </summary>
public record PosAccountResponseDto : PosAccountDto
{
    public Guid Id { get; set; }
    public string? RoleName { get; set; }
    public string? AccountCode { get; set; }
    public string? UserName { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}