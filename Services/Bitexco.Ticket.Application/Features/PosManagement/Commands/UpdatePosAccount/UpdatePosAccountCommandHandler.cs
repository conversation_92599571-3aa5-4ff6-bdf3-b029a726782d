namespace Bitexco.Ticket.Application.Features.PosManagement.Commands.UpdatePosAccount;

using BCrypt.Net;
using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Handler for UpdatePosAccountCommand
/// </summary>
public class UpdatePosAccountCommandHandler(IApplicationDbContext context) 
    : IRequestHandler<UpdatePosAccountCommand, Response<object>>
{

    public async Task<Response<object>> Handle(
        UpdatePosAccountCommand request, 
        CancellationToken cancellationToken)
    {
        // Find the POS user
        var user = await context.Users
            .AsTracking()
            .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken)
        ?? throw new UserNotFoundException(request.Id);
        
        // Validate role exist in db
        var role = await context.Roles
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.Type == RoleType.pos && r.Name == request.PosAccount.PosRole.ToString(), cancellationToken)
        ?? throw new PosRoleNotFoundException(request.PosAccount.PosRole.ToString() ?? "Unknown");

        // Update user information
        user.RoleId = role.Id;
        user.FullName = request.PosAccount.FullName;
        user.Email = request.PosAccount.Email;
        user.PhoneNumber = request.PosAccount.PhoneNumber;

        // Update password if provided
        if (!string.IsNullOrWhiteSpace(request.PosAccount.Password))
        {
            user.PasswordHash = BCrypt.HashPassword(request.PosAccount.Password);
        }

        context.Users.Update(user);

        var result = await context.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityUpdatedException("User", request.Id);
        }

        return new Response<object>(new UpdatePosAccountResponseDto { Status = true });
    }
}