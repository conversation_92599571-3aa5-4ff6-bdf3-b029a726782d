namespace Bitexco.Ticket.Application.Features.PosManagement.Commands.UpdatePosAccount;

using Bitexco.Ticket.Application.Features.PosManagement.Dtos;
using BuildingBlocks.Abstractions;

/// <summary>
/// Command to update POS account information
/// </summary>
public record UpdatePosAccountCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
    public PosAccountDto PosAccount { get; set; } = null!;
}

/// <summary>
/// Response DTO for update POS account operation
/// </summary>
public record UpdatePosAccountResponseDto
{
    public bool Status { get; set; } = true;
}