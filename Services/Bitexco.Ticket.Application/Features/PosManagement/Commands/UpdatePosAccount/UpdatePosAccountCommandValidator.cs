namespace Bitexco.Ticket.Application.Features.PosManagement.Commands.UpdatePosAccount;

using FluentValidation;

/// <summary>
/// Validator for UpdatePosAccountCommand
/// </summary>
public class UpdatePosAccountCommandValidator : AbstractValidator<UpdatePosAccountCommand>
{
    public UpdatePosAccountCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("User ID is required");

        When(x => !string.IsNullOrEmpty(x.PosAccount.Password), () =>
        {
            RuleFor(x => x.PosAccount.Password)
                .MinimumLength(6).WithMessage("Password must be at least 6 characters");
        });

        When(x => !string.IsNullOrEmpty(x.PosAccount.Email), () =>
        {
            RuleFor(x => x.PosAccount.Email)
                .EmailAddress().WithMessage("Invalid email format")
                .MaximumLength(255).WithMessage("Email cannot exceed 255 characters");
        });

        When(x => !string.IsNullOrEmpty(x.PosAccount.PhoneNumber), () =>
        {
            RuleFor(x => x.PosAccount.PhoneNumber)
                .MaximumLength(20).WithMessage("Phone number cannot exceed 20 characters");
        });

        When(x => !string.IsNullOrEmpty(x.PosAccount.FullName), () =>
        {
            RuleFor(x => x.PosAccount.FullName)
                .MaximumLength(200).WithMessage("Full name cannot exceed 200 characters");
        });
    }
}