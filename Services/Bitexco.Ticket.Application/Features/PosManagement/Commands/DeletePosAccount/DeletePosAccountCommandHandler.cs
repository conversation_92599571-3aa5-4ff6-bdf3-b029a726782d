namespace Bitexco.Ticket.Application.Features.PosManagement.Commands.DeletePosAccount;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Handler for DeletePosAccountCommand
/// </summary>
public class DeletePosAccountCommandHandler(IApplicationDbContext context) 
    : IRequestHandler<DeletePosAccountCommand, Response<object>>
{

    public async Task<Response<object>> Handle(
        DeletePosAccountCommand request, 
        CancellationToken cancellationToken)
    {
        // Find the POS user
        var user = await context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

        if (user == null || user.Role?.Type != RoleType.pos)
        {
            throw new UserNotFoundException(request.Id);
        }

        user.IsDeleted = true;
        context.Users.Update(user);
        
        var result = await context.SaveChangesAsync(cancellationToken);

        if (result == 0)
        {
            throw new NoEntityDeletedException("User", request.Id);
        }

        return new Response<object>(new DeletePosAccountResponseDto { Status = true });
    }
}