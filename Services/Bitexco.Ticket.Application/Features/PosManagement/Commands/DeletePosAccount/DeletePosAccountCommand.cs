namespace Bitexco.Ticket.Application.Features.PosManagement.Commands.DeletePosAccount;

using BuildingBlocks.Abstractions;

/// <summary>
/// Command to soft delete a POS account
/// </summary>
public record DeletePosAccountCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
}

/// <summary>
/// Response DTO for delete POS account operation
/// </summary>
public record DeletePosAccountResponseDto
{
    public bool Status { get; set; } = true;
}