namespace Bitexco.Ticket.Application.Features.PosManagement.Queries.GetPosAccounts;

using Bitexco.Ticket.Application.Features.PosManagement.Dtos;
using BuildingBlocks.Abstractions;

/// <summary>
/// Query to get paginated list of POS accounts with role-based sorting
/// </summary>
public record GetPosAccountsQuery : IRequest<PaginationResponse<PosAccountResponseDto>>, IPaginationRequest, ISortRequest
{
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 100;
    public string? SortBy { get; set; } = "posRole";
    public bool? IsDescending { get; set; } = false;
}