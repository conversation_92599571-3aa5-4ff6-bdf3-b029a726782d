namespace Bitexco.Ticket.Application.Features.PosManagement.Queries.GetPosAccounts;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Features.PosManagement.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Handler for GetPosAccountsQuery
/// </summary>
public class GetPosAccountsQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetPosAccountsQuery, PaginationResponse<PosAccountResponseDto>>
{
    public async Task<PaginationResponse<PosAccountResponseDto>> Handle(
        GetPosAccountsQuery request, 
        CancellationToken cancellationToken)
    {
        // Query POS users (users with roles that have Type = pos)
        var query = context.Users
            .Where(u => u.Role != null && u.Role.Type == RoleType.pos)
            .Include(u => u.Role)
            .AsNoTracking();

        // Get total count for pagination
        var totalCount = await query.CountAsync(cancellationToken);

        query = request.SortBy?.ToLower() switch
        {
            "posrole" => request.IsDescending == true
                ? query.OrderByDescending(u => u.Role != null ? u.Role.Name : string.Empty)
                : query.OrderBy(u => u.Role != null ? u.Role.Name : string.Empty),
            "username" => request.IsDescending == true
                ? query.OrderByDescending(u => u.UserName)
                : query.OrderBy(u => u.UserName),
            "email" => request.IsDescending == true
                ? query.OrderByDescending(u => u.Email)
                : query.OrderBy(u => u.Email),
            "createdat" => request.IsDescending == true
                ? query.OrderByDescending(u => u.CreatedAt)
                : query.OrderBy(u => u.CreatedAt),
            _ => query // Default case, no sorting applied
        };

        // Apply pagination
        var users = await query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Map to DTOs with AccountCode generation
        var posAccounts = users.Select((user, index) => new PosAccountResponseDto
        {
            Id = user.Id,
            AccountCode = $"POS-{(request.PageIndex - 1) * request.PageSize + index + 1:D3}",
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            RoleName = user.Role?.Name ?? string.Empty,
            PosRole = Enum.TryParse<PosRoles>(user.Role?.Name ?? string.Empty, out var posRole) ? posRole : null,
            FullName = user.FullName,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        }).ToList();

        return new PaginationResponse<PosAccountResponseDto>(
            request.PageIndex,
            request.PageSize,
            totalCount,
            posAccounts
        );
    }
}