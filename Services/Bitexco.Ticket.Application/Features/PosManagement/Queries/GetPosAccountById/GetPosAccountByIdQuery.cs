namespace Bitexco.Ticket.Application.Features.PosManagement.Queries.GetPosAccountById;

using Bitexco.Ticket.Application.Data;
using Bitexco.Ticket.Application.Features.PosManagement.Dtos;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Query to get detailed information for a specific POS account
/// </summary>
public class GetPosAccountByIdQuery : IRequest<Response<PosAccountResponseDto>>
{
    /// <summary>
    /// ID of the POS account to retrieve
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for GetPosAccountByIdQuery
/// </summary>
public class GetPosAccountByIdQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetPosAccountByIdQuery, Response<PosAccountResponseDto>>
{

    public async Task<Response<PosAccountResponseDto>> Handle(
        GetPosAccountByIdQuery request, 
        CancellationToken cancellationToken)
    {
        // Find POS user with role information
        var user = await context.Users
            .Include(u => u.Role)
            .AsNoTracking()
            .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

        if (user == null || user.Role?.Type != RoleType.pos)
        {
            throw new UserNotFoundException(request.Id);
        }

        // Map to DTO with AccountCode generation
        var posAccountDto = new PosAccountResponseDto
        {
            Id = user.Id,
            AccountCode = $"SDK-POS-{user.Id.ToString("N")[..8].ToUpper()}", // Use part of GUID for unique code
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            RoleName = user.Role?.Name ?? string.Empty,
            FullName = user.FullName,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };

        return new Response<PosAccountResponseDto>(posAccountDto);
    }
}