﻿using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.VoucherCampaign.Queries
{
    public record VoucherCampaignQuery
        : VoucherCampaignQueryDto, IQuery<Response<List<VoucherCampaignResponseDto>>>;

    public record VoucherCampaignQueryDto
    {
        //public DateTime? ValidFrom { get; set; }
        //public DateTime? ValidTo { get; set; }
        public bool? IsActive { get; set; }
        public VoucherType? VoucherType { get; set; } // Optional filter by voucher code
    }

    public class VoucherCampaignQueryHandler(IApplicationDbContext dbContext)
        : IQueryHandler<VoucherCampaignQuery, Response<List<VoucherCampaignResponseDto>>>
    {
        public async Task<Response<List<VoucherCampaignResponseDto>>> Handle(
            VoucherCampaignQuery request,
            CancellationToken cancellationToken)
        {
            var query = dbContext.VoucherCampaigns.
                Include(v => v.TicketType).
                AsNoTracking().AsQueryable();
            if (request.IsActive.HasValue)
            {
                query = query.Where(v => v.IsActive == request.IsActive.Value);
            }
            if (request.VoucherType.HasValue)
            {
                query = query.Where(v => v.Type == request.VoucherType.Value);
            }

            var items = await query
                .OrderByDescending(v => v.CreatedAt)
                .ToListAsync(cancellationToken);

            var result = new List<VoucherCampaignResponseDto>();
            items.ForEach(item => result.Add
            (
                new VoucherCampaignResponseDto
                {
                    Id = item.Id,
                    Name = item.Name,
                    Type = item.Type,
                    DiscountValue = item.DiscountValue,
                    MaxDiscountAmount = item.MaxDiscountAmount,
                    UsageLimit = item.UsageLimit,
                    TicketTypeId = item.TicketType?.Id,
                    TicketTypeName = item.TicketType?.Name,
                    VoucherApplyType = item.VoucherApplyType,
                    AgentIds = item.AgentIds,
                    CurrentUsage = item.CurrentUsage,
                    ValidFrom = item.ValidFrom,
                    ValidTo = item.ValidTo,
                    IsActive = item.IsActive,
                    CreatedAt = item.CreatedAt!.Value,
                    UpdatedAt = item.UpdatedAt,
                }));

            return new Response<List<VoucherCampaignResponseDto>>(result);
        }
    }
}