﻿using System.Text;

namespace Bitexco.Ticket.Application.Features.VoucherCampaign.Commands
{
    public record CreateVoucherCampaignCommand : ICommand<Response<CreateVoucherCampaignResponse>>
    {
        public string? Name { get; set; }

        public VoucherType Type { get; set; }
        public decimal DiscountValue { get; set; }

        /// <summary>
        /// Maximum discount amount that can be applied
        /// This is used to limit the discount for fixed amount vouchers
        /// Null means no maximum limit
        /// </summary>
        public decimal? MaxDiscountAmount { get; set; }

        /// <summary>
        ///  1 Gen voucher code by prefix code
        ///  2 Gen voucher code by voucher code
        /// </summary>
        public int VoucherGenType { get; set; }

        public int VoucherQuantity { get; set; } = 1;
        /// <summary>
        /// Prefix voucher code
        /// </summary>
        public string? PrefixVoucher { get; set; }
        /// <summary>
        /// character to prefix the voucher code
        /// </summary>
        public int? PrefixChar { get; set; }

        /// <summary>
        /// Voucher code
        /// </summary>
        public string? VoucherCode { get; set; }

        /// <summary>
        ///  Ticket type ID to which this voucher applies
        /// </summary>
        public Guid? TicketTypeId { get; set; }

        public VoucherApplyType VoucherApplyType { get; set; }
        public List<Guid>? AgentIds { get; set; }

        /// <summary>
        /// Date and time when this voucher becomes valid
        /// </summary>
        public DateTime ValidFrom { get; set; }

        /// <summary>
        /// Date and time when this voucher expires
        /// </summary>
        public DateTime ValidTo { get; set; }
    }

    public record CreateVoucherCampaignResponse
    {
        public Guid Id { get; set; }
    }

    public class CreateVoucherCampaignValidator : AbstractValidator<CreateVoucherCampaignCommand>
    {
        public CreateVoucherCampaignValidator()
        {
            RuleFor(x => x.Name).NotEmpty().WithMessage("Name is required.");
            RuleFor(x => x.Type).IsInEnum().WithMessage("Invalid voucher type.");
            When(x => x.Type == VoucherType.percentage, () =>
            {
                RuleFor(x => x.DiscountValue).InclusiveBetween(0.01m, 100).WithMessage("Discount value must be between 0.01 and 100 for percentage vouchers.");
            });
            When(x => x.Type == VoucherType.fixed_amount, () =>
            {
                RuleFor(x => x.DiscountValue).GreaterThan(0).WithMessage("Discount value must be greater than zero for fixed amount vouchers.");
            });

            RuleFor(x => x.VoucherQuantity)
              .GreaterThan(0).WithMessage("Voucher quantity must be greater than zero.");

            When(x => x.VoucherGenType == 1, () =>
            {
                RuleFor(x => x.PrefixVoucher)
                    .NotEmpty().WithMessage("Prefix voucher is required when generating by prefix code.")
                    .MaximumLength(10).WithMessage("Prefix voucher cannot exceed 10 characters.");
                RuleFor(x => x.PrefixChar)
                    .GreaterThan(0).WithMessage("Prefix character length must be greater than zero.");

                RuleFor(x => x)

                .Custom((x, context) =>
                {
                    int charsetLength = 36; // 0-9, A-Z
                    int safeRatio = 10;     // tổ hợp nên lớn gấp 10 lần số lượng voucher
                    int prefixLength = x.PrefixChar!.Value + (x.PrefixVoucher?.Length ?? 0);
                    if (prefixLength <= 0)
                    {
                        context.AddFailure("RandomCodeLength", "Random code length must be greater than zero.");
                    }
                    else
                    {
                        double totalCombinations = Math.Pow(charsetLength, prefixLength);
                        if (totalCombinations < x.VoucherQuantity * safeRatio)
                        {
                            context.AddFailure("RandomCodeLength", $"The number of random code combinations ({totalCombinations:N0}) should be at least {safeRatio} times greater than the number of vouchers ({x.VoucherQuantity}). Please increase the random code length or decrease the voucher quantity.");
                        }
                    }
                });
            });
            When(x => x.VoucherGenType == 2, () =>
            {
                RuleFor(x => x.VoucherCode)
                    .NotEmpty().WithMessage("Voucher code is required when generating by voucher code.")
                    .MaximumLength(20).WithMessage("Voucher code cannot exceed 20 characters.");
            });

            When(x => x.VoucherApplyType == VoucherApplyType.include_corporate
            || x.VoucherApplyType == VoucherApplyType.exclude_corporate, () =>
            {
                RuleFor(x => x.AgentIds)
                    .NotEmpty().WithMessage("Agent IDs are required for corporate voucher types.");
            });

            RuleFor(x => x.ValidFrom).LessThanOrEqualTo(x => x.ValidTo)
               .WithMessage("Valid From date must be before or equal to Valid To date.");
        }
    }

    public class CreateVoucherCampaignHandler(IApplicationDbContext applicationDbContext)
        : ICommandHandler<CreateVoucherCampaignCommand, Response<CreateVoucherCampaignResponse>>
    {
        private readonly IApplicationDbContext _applicationDbContext = applicationDbContext;
        private const string _defaultCharset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

        public async Task<Response<CreateVoucherCampaignResponse>> Handle(CreateVoucherCampaignCommand request, CancellationToken cancellationToken)
        {
            int result = 0;
            var voucherCampaign = new Domain.Models.VoucherCampaign
            {
                Name = request.Name!,
                Type = request.Type,
                DiscountValue = request.DiscountValue,
                MaxDiscountAmount = request.MaxDiscountAmount,
                UsageLimit = request.VoucherQuantity,
                VoucherApplyType = request.VoucherApplyType,
                AgentIds = request.AgentIds ?? new List<Guid>(),
                ValidFrom = request.ValidFrom,
                ValidTo = request.ValidTo
            };
            if (request.TicketTypeId.HasValue)
            {
                // Validate if the TicketTypeId exists in the database
                var ticketType = await _applicationDbContext.TicketTypes
                    .FindAsync(request.TicketTypeId.Value)
                    ?? throw new TicketTypeNotFoundException(request.TicketTypeId.Value);

                voucherCampaign.TicketTypeId = request.TicketTypeId.Value;
            }
            // If generating by prefix code
            if (request.VoucherGenType == 1)
            {
                var uniqueVouchers = GenerateUniqueVouchers(request.VoucherQuantity, request.PrefixChar!.Value, request.PrefixVoucher!);
                voucherCampaign.Vouchers = uniqueVouchers.Select(code => new Domain.Models.Voucher
                {
                    Code = code,
                    Type = request.Type,
                    DiscountValue = request.DiscountValue,
                    MaxDiscountAmount = request.MaxDiscountAmount,
                    UsageLimit = 1,
                    VoucherApplyType = request.VoucherApplyType,
                    AgentIds = request.AgentIds ?? new List<Guid>(),
                    ValidFrom = request.ValidFrom,
                    ValidTo = request.ValidTo
                }).ToList();
                // Save the voucher campaign to the database

                _applicationDbContext.VoucherCampaigns.Add(voucherCampaign);
                result = await _applicationDbContext.SaveChangesAsync(cancellationToken);
            }
            // If generating by voucher code
            else if (request.VoucherGenType == 2)
            {
                var voucherCode = _applicationDbContext.Vouchers
                    .FirstOrDefault(vc => vc.Code == request.VoucherCode);
                if (voucherCode != null)
                {
                    throw new VoucherNotFoundException(request.VoucherCode!);
                }
                voucherCampaign.Vouchers = new List<Domain.Models.Voucher>
                {
                    new Domain.Models.Voucher
                    {
                        Code = request.VoucherCode!,
                        Type = request.Type,
                        DiscountValue = request.DiscountValue,
                        MaxDiscountAmount = request.MaxDiscountAmount,
                        UsageLimit = request.VoucherQuantity,
                        VoucherApplyType = request.VoucherApplyType,
                        AgentIds = request.AgentIds ?? new List<Guid>(),
                        ValidFrom = request.ValidFrom,
                        ValidTo = request.ValidTo
                    }
                };

                // Save the voucher campaign to the database
                _applicationDbContext.VoucherCampaigns.Add(voucherCampaign);
                result = await _applicationDbContext.SaveChangesAsync(cancellationToken);
            }
            // If no vouchers were generated, throw an exception
            return result <= 0
                ? throw new NoEntityCreatedException("VoucherCampaign")
                : new Response<CreateVoucherCampaignResponse>(new CreateVoucherCampaignResponse
                {
                    Id = voucherCampaign.Id
                });
        }

        private List<string> GenerateUniqueVouchers(int quantity, int randomLength, string prefix = "", string charset = _defaultCharset)
        {
            var result = new HashSet<string>();
            var rng = new Random();

            while (result.Count < quantity)
            {
                var sb = new StringBuilder(prefix);
                for (int i = 0; i < randomLength; i++)
                {
                    int idx = rng.Next(charset.Length);
                    sb.Append(charset[idx]);
                }

                result.Add(sb.ToString());
            }

            return new List<string>(result);
        }
    }
}