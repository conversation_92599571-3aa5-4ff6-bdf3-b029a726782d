﻿using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.VoucherCampaign.Commands
{
    public record ActiveVoucherCampaignCommand : ICommand<Response<VoucherCampaignResponseDto>>
    {
        /// <summary>
        /// Id của voucher campaign
        /// </summary>
        public Guid Id { get; set; }
    }

    public class VoucherActiveCampaignCommandHandler(IApplicationDbContext dbContext)
        : ICommandHandler<ActiveVoucherCampaignCommand, Response<VoucherCampaignResponseDto>>
    {
        public async Task<Response<VoucherCampaignResponseDto>> Handle(
            ActiveVoucherCampaignCommand request,
            CancellationToken cancellationToken)
        {
            // Find the voucher campaign by Id
            var voucherCampaign = await dbContext.VoucherCampaigns
                .AsTracking()
                .FirstOrDefaultAsync(vc => vc.Id == request.Id, cancellationToken)
                ?? throw new VoucherCampaignNotFoundException(request.Id);
                
            // Update the IsActive status
            voucherCampaign.IsActive = !voucherCampaign.IsActive;
            dbContext.VoucherCampaigns.Update(voucherCampaign);

            // Save changes to the database
            var result = await dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new NoEntityUpdatedException("VoucherCampaign", request.Id);
            }

            // Map to response DTO
            var responseDto = voucherCampaign.Adapt<VoucherCampaignResponseDto>();
            
            return new Response<VoucherCampaignResponseDto>(responseDto);
        }
    }
}