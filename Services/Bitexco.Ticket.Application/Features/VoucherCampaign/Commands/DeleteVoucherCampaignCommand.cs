﻿namespace Bitexco.Ticket.Application.Features.VoucherCampaign.Commands
{
    public record DeleteVoucherCampaignCommand : ICommand<Response<object>>
    {
        public Guid Id { get; init; }
    }

    public class DeleteVoucherCampaignCommandHandler(IApplicationDbContext dbContext)
        : ICommandHandler<DeleteVoucherCampaignCommand, Response<object>>
    {
        public async Task<Response<object>> Handle(DeleteVoucherCampaignCommand request, CancellationToken cancellationToken)
        {
            var campaign = await dbContext.VoucherCampaigns
                .AsTracking()
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken)
                ?? throw new VoucherCampaignInUseException(request.Id);

            if (campaign.CurrentUsage != 0)
            {
                throw new VoucherCampaignInUseException(campaign.Id);
            }

            campaign.IsDeleted = true;
            dbContext.VoucherCampaigns.Update(campaign);
            
            var result = await dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new NoEntityDeletedException("VoucherCampaign", campaign.Id);
            }

            return new Response<object>(true);
        }
    }
}