namespace Bitexco.Ticket.Application.Features.Shifts.DeleteShift;

/// <summary>
/// Command to delete a shift (soft delete)
/// </summary>
public class DeleteShiftCommand(Guid id) : IRequest<Response<object>>
{
    public Guid Id { get; set; } = id;

}

/// <summary>
/// <PERSON><PERSON> for deleting a shift (soft delete)
/// </summary>
public class DeleteShiftCommandHandler(IApplicationDbContext context) : IRequestHandler<DeleteShiftCommand, Response<object>>
{

    public async Task<Response<object>> Handle(DeleteShiftCommand request, CancellationToken cancellationToken)
    {
        // Find existing shift
        var existingShift = await context.Shifts
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);
        if (existingShift == null)
        {
            throw new EntityNotFoundException("Shift", request.Id);
        }
        
        existingShift.IsDeleted = true;
        existingShift.UpdatedAt = DateTime.UtcNow;

        context.Shifts.Update(existingShift);
        var result = await context.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityDeletedException("Shift", request.Id);
        }

        return new Response<object>(new { 
            Status = true
        });
    }
}
