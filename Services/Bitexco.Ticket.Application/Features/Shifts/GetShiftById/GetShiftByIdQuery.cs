using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Shifts.GetShiftById;

/// <summary>
/// Query to get a shift by its ID
/// </summary>
public class GetShiftByIdQuery(Guid id) : IRequest<Response<ShiftResponseDto>>
{
    public Guid Id { get; set; } = id;
}

/// <summary>
/// Handler for getting a shift by its ID
/// </summary>
public class GetShiftByIdQueryHandler(IApplicationDbContext context) : IRequestHandler<GetShiftByIdQuery, Response<ShiftResponseDto>>
{

    public async Task<Response<ShiftResponseDto>> Handle(GetShiftByIdQuery request, CancellationToken cancellationToken)
    {
        var shift = await context.Shifts
            .Include(s => s.User)
            .Include(s => s.PosDevice)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (shift == null)
        {
            throw new EntityNotFoundException("Shift", request.Id);
        }

        var responseDto = shift.Adapt<ShiftResponseDto>();
        responseDto.UserName = shift.User?.FullName;
        responseDto.PosDeviceName = shift.PosDevice?.Name;

        return new Response<ShiftResponseDto>(responseDto);
    }
}
