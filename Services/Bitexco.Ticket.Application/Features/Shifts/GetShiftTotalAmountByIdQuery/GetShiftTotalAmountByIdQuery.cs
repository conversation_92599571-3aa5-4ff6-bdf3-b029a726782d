using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Shifts.GetShiftTotalAmountById;

/// <summary>
/// Query to get a shift total amount by its ID
/// Includes total amounts for different payment methods (cash, POS, bank transfer, etc.)
/// </summary>
public record GetShiftTotalAmountByIdQuery(Guid Id, PaymentTransactionMethod? PaymentMethod) : IRequest<Response<GetShiftTotalAmountByIdResult>>;

public record GetShiftTotalAmountByIdResult
{
    public PaymentTransactionMethod? PaymentMethod { get; set; }
    public decimal TotalAmount { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
};

/// <summary>
/// Handler for getting a shift by its ID
/// </summary>
public class GetShiftTotalAmountByIdQueryHandler(IApplicationDbContext context) : IRequestHandler<GetShiftTotalAmountByIdQuery, Response<GetShiftTotalAmountByIdResult>>
{

    public async Task<Response<GetShiftTotalAmountByIdResult>> Handle(GetShiftTotalAmountByIdQuery request, CancellationToken cancellationToken)
    {
        var shift = await context.Shifts
            .AsNoTracking()
            .Include(s => s.Orders).ThenInclude(o => o.PaymentTransactions)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken)
            ?? throw new EntityNotFoundException("Shift", request.Id);

        var result = new GetShiftTotalAmountByIdResult
        {
            PaymentMethod = request.PaymentMethod,
            TotalAmount = shift.Orders
                .SelectMany(o => o.PaymentTransactions ?? [])
                .Where(pt => (request.PaymentMethod == null || pt.PaymentMethod == request.PaymentMethod)
                    && (pt.TransactionStatus == PaymentTransactionStatus.success || pt.TransactionStatus == PaymentTransactionStatus.partial_success))
                .Sum(pt => pt.PaidAmount),
            StartTime = shift.StartTime,
            EndTime = shift.EndTime
        };

        return new Response<GetShiftTotalAmountByIdResult>(result);
    }
}
