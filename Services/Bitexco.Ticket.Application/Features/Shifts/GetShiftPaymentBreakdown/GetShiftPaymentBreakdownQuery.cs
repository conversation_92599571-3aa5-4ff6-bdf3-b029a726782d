using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Shifts.GetShiftPaymentBreakdown;

/// <summary>
/// Query to get shift payment breakdown by payment method
/// </summary>
public record GetShiftPaymentBreakdownQuery(Guid ShiftId) : IRequest<Response<GetShiftPaymentBreakdownResult>>;

public record GetShiftPaymentBreakdownResult
{
    /// <summary>
    /// List of payment method breakdowns
    /// </summary>
    public List<PaymentMethodBreakdown> PaymentMethods { get; set; } = [];
}

public record PaymentMethodBreakdown
{
    /// <summary>
    /// Payment method (cash, pos, bank_transfer, etc.)
    /// </summary>
    public PaymentTransactionMethod PaymentMethod { get; set; }
    
    /// <summary>
    /// Total amount for this payment method
    /// </summary>
    public decimal Amount { get; set; }
};

/// <summary>
/// Handler for getting shift payment breakdown by method
/// </summary>
public class GetShiftPaymentBreakdownQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetShiftPaymentBreakdownQuery, Response<GetShiftPaymentBreakdownResult>>
{
    public async Task<Response<GetShiftPaymentBreakdownResult>> Handle(
        GetShiftPaymentBreakdownQuery request, 
        CancellationToken cancellationToken)
    {
        var shift = await context.Shifts
            .AsNoTracking()
            .Include(s => s.Orders)
                .ThenInclude(o => o.PaymentTransactions)
            .FirstOrDefaultAsync(s => s.Id == request.ShiftId, cancellationToken)
            ?? throw new ShiftNotFoundException(request.ShiftId);

        // Get successful payment transactions grouped by payment method
        var paymentBreakdown = shift.Orders
            .SelectMany(o => o.PaymentTransactions ?? [])
            .Where(pt => pt.TransactionStatus == PaymentTransactionStatus.success || 
                        pt.TransactionStatus == PaymentTransactionStatus.partial_success)
            .GroupBy(pt => pt.PaymentMethod)
            .Select(g => new PaymentMethodBreakdown
            {
                PaymentMethod = g.Key,
                Amount = g.Sum(pt => pt.PaidAmount)
            })
            .OrderBy(p => p.PaymentMethod)
            .ToList();

        var result = new GetShiftPaymentBreakdownResult
        {
            PaymentMethods = paymentBreakdown
        };

        return new Response<GetShiftPaymentBreakdownResult>(result);
    }
}