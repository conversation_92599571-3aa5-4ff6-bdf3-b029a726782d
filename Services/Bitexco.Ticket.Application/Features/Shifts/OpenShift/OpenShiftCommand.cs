using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace Bitexco.Ticket.Application.Features.Shifts.OpenShift;

/// <summary>
/// Command to create a new shift
/// </summary>
public class OpenShiftCommand : IRequest<Response<OpenShiftCommandResult>>
{
    public OpenShiftCommandRequest Shift { get; set; } = null!;
}

public record OpenShiftCommandRequest
{
    public Guid PosDeviceId { get; set; }
    public decimal StartCashBalance { get; set; } = 0;
}

public record OpenShiftCommandResult
{
    public Guid Id { get; set; }
    public DateTime StartTime { get; set; }
};

/// <summary>
/// Validator for OpenShiftCommand
/// </summary>
public class OpenShiftCommandValidator : AbstractValidator<OpenShiftCommand>
{
    public OpenShiftCommandValidator()
    {
        RuleFor(x => x.Shift.PosDeviceId)
            .NotEmpty().WithMessage("POS Device ID is required");

        RuleFor(x => x.Shift.StartCashBalance)
            .GreaterThanOrEqualTo(0).WithMessage("Start cash balance must be greater than or equal to 0");
    }
}

/// <summary>
/// Handler for creating a new shift
/// </summary>
public class OpenShiftCommandHandler(IApplicationDbContext context,
    IHttpContextAccessor httpContextAccessor) : IRequestHandler<OpenShiftCommand, Response<OpenShiftCommandResult>>
{

    public async Task<Response<OpenShiftCommandResult>> Handle(OpenShiftCommand request, CancellationToken cancellationToken)
    {
        // Get userId from the current user context
        var userId = httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        // Check if POS device exists
        var posDeviceExists = await context.PosDevices
            .AnyAsync(p => p.Id == request.Shift.PosDeviceId, cancellationToken);

        if (!posDeviceExists)
        {
            throw new EntityNotFoundException("PosDevice", request.Shift.PosDeviceId);
        }

        // Check if there's an active shift for this user on this POS device
        var foundShift = await context.Shifts
            .FirstOrDefaultAsync(s => s.UserId == Guid.Parse(userId)
                        && s.PosDeviceId == request.Shift.PosDeviceId 
                        && s.Status == Domain.Enums.ShiftStatus.open, cancellationToken);
        if (foundShift != null)
        {
            return new Response<OpenShiftCommandResult>(new OpenShiftCommandResult { Id = foundShift.Id, StartTime = foundShift.StartTime });
        }

        // Create new shift
        var shift = request.Shift.Adapt<Shift>();
        shift.Id = Guid.NewGuid();
        shift.UserId = Guid.Parse(userId);
        shift.Status = Domain.Enums.ShiftStatus.open;
        shift.StartTime = DateTime.UtcNow;

        context.Shifts.Add(shift);
        var result = await context.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityCreatedException("Shift");
        }

        return new Response<OpenShiftCommandResult>(new OpenShiftCommandResult { Id = shift.Id, StartTime = shift.StartTime });
    }
}
