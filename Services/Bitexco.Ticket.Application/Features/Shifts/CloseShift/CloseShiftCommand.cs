using System.Security.Claims;
using Bitexco.Ticket.Domain.Enums;
using Microsoft.AspNetCore.Http;

namespace Bitexco.Ticket.Application.Features.Shifts.CloseShift;

/// <summary>
/// Command to create a new shift
/// </summary>
public class CloseShiftCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; } // This is the ID of the shift to close
    public CloseShiftCommandRequest Shift { get; set; } = null!;
}

public record CloseShiftCommandRequest
{
    public decimal EndCashBalance { get; set; } = 0;
}

/// <summary>
/// Validator for CloseShiftCommand
/// </summary>
public class CloseShiftCommandValidator : AbstractValidator<CloseShiftCommand>
{
    public CloseShiftCommandValidator()
    {
        RuleFor(x => x.Shift.EndCashBalance)
            .GreaterThanOrEqualTo(0).WithMessage("End cash balance must be greater than or equal to 0");
    }
}

/// <summary>
/// Handler for closing an existing shift
/// </summary>
public class CloseShiftCommandHandler(IApplicationDbContext context,
    IHttpContextAccessor httpContextAccessor) : IRequestHandler<CloseShiftCommand, Response<object>>
{

    public async Task<Response<object>> Handle(CloseShiftCommand request, CancellationToken cancellationToken)
    {
        // Check if the shift exists
        var shift = await context.Shifts
            .FindAsync([request.Id], cancellationToken) ?? throw new ShiftNotFoundException(request.Id);

        // Get userId from the current user context
        var userId = httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
        
        if (string.IsNullOrEmpty(userId) || shift.UserId != Guid.Parse(userId))
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        if (shift.EndTime.HasValue)
        {
            throw new InvalidOperationException("Shift is already closed.");
        }

        shift.EndTime = DateTime.UtcNow;
        shift.EndCashBalance = request.Shift.EndCashBalance;
        shift.Status = ShiftStatus.closed;

        context.Shifts.Update(shift);
        var result = await context.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityUpdatedException("Shift", request.Id);
        }

        return new Response<object>(true);
    }
}
