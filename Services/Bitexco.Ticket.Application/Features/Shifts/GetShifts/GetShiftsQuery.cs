using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Shifts.GetShifts;

/// <summary>
/// Query to get paginated list of shifts with optional filtering
/// </summary>
public class GetShiftsQuery
    : IRequest<PaginationResponse<ShiftSummaryDto>>, IPaginationRequest
{
    // Pagination parameters
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 100;

    // Filter parameters
    public Guid? UserId { get; set; }
    public Guid? PosDeviceId { get; set; }
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public ShiftStatus? Status { get; set; }
}

/// <summary>
/// Handler for getting paginated list of shifts
/// </summary>
public class GetShiftsQueryHandler(IApplicationDbContext context) : IRequestHandler<GetShiftsQuery, PaginationResponse<ShiftSummaryDto>>
{

    public async Task<PaginationResponse<ShiftSummaryDto>> Handle(GetShiftsQuery request, CancellationToken cancellationToken)
    {
        // Build query with filters
        var query = context.Shifts
            .Include(s => s.User)
            .Include(s => s.PosDevice)
            .AsQueryable();

        // Apply filters
        if (request.UserId.HasValue)
        {
            query = query.Where(s => s.UserId == request.UserId.Value);
        }

        if (request.PosDeviceId.HasValue)
        {
            query = query.Where(s => s.PosDeviceId == request.PosDeviceId.Value);
        }

        if (request.StartDate.HasValue)
        {
            query = query.Where(s => DateOnly.FromDateTime(s.StartTime) >= request.StartDate.Value);
        }

        if (request.EndDate.HasValue)
        {
            query = query.Where(s => DateOnly.FromDateTime(s.StartTime) <= request.EndDate.Value);
        }

        if (request.Status.HasValue)
        {
            query = query.Where(s => s.Status == request.Status.Value);
        }

        // Get total count for pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var shifts = await query
            .OrderByDescending(s => s.StartTime)
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Map to DTOs
        var shiftDtos = shifts.Select(shift => {
            var dto = shift.Adapt<ShiftSummaryDto>();
            dto.TotalSales = shift.TotalCashSales + shift.TotalPosSales + shift.TotalTransferSales;
            dto.UserName = shift.User.FullName;
            dto.PosDeviceName = shift.PosDevice.Name;
            return dto;
        }).ToList();

        // Create and return pagination response
        var response = new PaginationResponse<ShiftSummaryDto>
        (
            request.PageIndex,
            request.PageSize,
            totalCount,
            shiftDtos
        );

        return response;
    }
}
