using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Shifts.GetShiftTicketTypeRevenue;

/// <summary>
/// Query to get shift ticket type revenue breakdown
/// </summary>
public record GetShiftTicketTypeRevenueQuery(Guid ShiftId) : IRequest<Response<GetShiftTicketTypeRevenueResult>>;

public record GetShiftTicketTypeRevenueResult
{
    /// <summary>
    /// List of ticket type revenue breakdowns
    /// </summary>
    public List<TicketTypeRevenue> TicketTypes { get; set; } = [];
    
    /// <summary>
    /// Total revenue across all ticket types
    /// </summary>
    public decimal TotalRevenue { get; set; }
};

public record TicketTypeRevenue
{
    /// <summary>
    /// Ticket type ID
    /// </summary>
    public Guid TicketTypeId { get; set; }
    
    /// <summary>
    /// Ticket type name
    /// </summary>
    public string TicketTypeName { get; set; } = string.Empty;
    
    /// <summary>
    /// Ticket type code
    /// </summary>
    public string TicketTypeCode { get; set; } = string.Empty;
    
    /// <summary>
    /// Total quantity sold for this ticket type
    /// </summary>
    public int Quantity { get; set; }
    
    /// <summary>
    /// Unit price for this ticket type
    /// </summary>
    public decimal UnitPrice { get; set; }
    
    /// <summary>
    /// Total amount for this ticket type (Quantity * UnitPrice)
    /// </summary>
    public decimal Amount { get; set; }
};

/// <summary>
/// Handler for getting shift ticket type revenue breakdown
/// </summary>
public class GetShiftTicketTypeRevenueQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetShiftTicketTypeRevenueQuery, Response<GetShiftTicketTypeRevenueResult>>
{
    public async Task<Response<GetShiftTicketTypeRevenueResult>> Handle(
        GetShiftTicketTypeRevenueQuery request, 
        CancellationToken cancellationToken)
    {
        var shift = await context.Shifts
            .AsNoTracking()
            .Include(s => s.Orders.Where(o => o.Status == OrderStatus.paid))
                .ThenInclude(o => o.OrderItems)
                    .ThenInclude(oi => oi.TicketType)
            .FirstOrDefaultAsync(s => s.Id == request.ShiftId, cancellationToken)
            ?? throw new ShiftNotFoundException(request.ShiftId);

        // Group order items by ticket type (only from paid orders)
        var ticketTypeRevenues = shift.Orders
            .Where(o => o.Status == OrderStatus.paid)
            .SelectMany(o => o.OrderItems)
            .GroupBy(oi => oi.TicketType)
            .Select(g => new TicketTypeRevenue
            {
                TicketTypeId = g.Key.Id,
                TicketTypeName = g.Key.Name,
                TicketTypeCode = g.Key.Code,
                Quantity = g.Sum(oi => oi.Quantity),
                UnitPrice = g.First().UnitPrice, 
                Amount = g.Sum(oi => oi.SubTotal)
            })
            .OrderBy(t => t.TicketTypeName)
            .ToList();

        var totalRevenue = ticketTypeRevenues.Sum(t => t.Amount);

        var result = new GetShiftTicketTypeRevenueResult
        {
            TicketTypes = ticketTypeRevenues,
            TotalRevenue = totalRevenue
        };

        return new Response<GetShiftTicketTypeRevenueResult>(result);
    }
}