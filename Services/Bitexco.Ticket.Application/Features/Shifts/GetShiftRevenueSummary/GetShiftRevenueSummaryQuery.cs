using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Shifts.GetShiftRevenueSummary;

/// <summary>
/// Query to get shift revenue summary including total revenue, order count, tickets sold/cancelled
/// </summary>
public record GetShiftRevenueSummaryQuery(Guid ShiftId) : IRequest<Response<GetShiftRevenueSummaryResult>>;

public record GetShiftRevenueSummaryResult
{
    /// <summary>
    /// Total revenue from successful payment transactions
    /// </summary>
    public decimal TotalRevenue { get; set; }
    
    /// <summary>
    /// Total number of orders processed in this shift
    /// </summary>
    public int OrderCount { get; set; }
    
    /// <summary>
    /// Total number of tickets sold (from paid orders)
    /// </summary>
    public int TicketsSold { get; set; }
    
    /// <summary>
    /// Total number of tickets cancelled
    /// </summary>
    public int TicketsCancelled { get; set; }
};

/// <summary>
/// Handler for getting shift revenue summary
/// </summary>
public class GetShiftRevenueSummaryQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetShiftRevenueSummaryQuery, Response<GetShiftRevenueSummaryResult>>
{
    public async Task<Response<GetShiftRevenueSummaryResult>> Handle(
        GetShiftRevenueSummaryQuery request, 
        CancellationToken cancellationToken)
    {
        var shift = await context.Shifts
            .AsNoTracking()
            .Include(s => s.Orders)
                .ThenInclude(o => o.PaymentTransactions)
            .Include(s => s.Orders)
                .ThenInclude(o => o.OrderItems)
            .FirstOrDefaultAsync(s => s.Id == request.ShiftId, cancellationToken)
            ?? throw new ShiftNotFoundException(request.ShiftId);

        // Calculate total revenue from successful payment transactions
        var totalRevenue = shift.Orders
            .SelectMany(o => o.PaymentTransactions ?? [])
            .Where(pt => pt.TransactionStatus == PaymentTransactionStatus.success || 
                        pt.TransactionStatus == PaymentTransactionStatus.partial_success)
            .Sum(pt => pt.PaidAmount);

        // Count total orders
        var orderCount = shift.Orders.Count;

        // Count tickets sold (from paid orders)
        var ticketsSold = shift.Orders
            .Where(o => o.Status == OrderStatus.paid)
            .SelectMany(o => o.OrderItems)
            .Sum(oi => oi.Quantity);

        // Count tickets cancelled  
        var ticketsCancelled = shift.Orders
            .Where(o => o.Status == OrderStatus.cancelled)
            .SelectMany(o => o.OrderItems)
            .Sum(oi => oi.Quantity);

        var result = new GetShiftRevenueSummaryResult
        {
            TotalRevenue = totalRevenue,
            OrderCount = orderCount,
            TicketsSold = ticketsSold,
            TicketsCancelled = ticketsCancelled,
        };

        return new Response<GetShiftRevenueSummaryResult>(result);
    }
}