using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Shifts.UpdateShift;

/// <summary>
/// Command to update an existing shift
/// </summary>
public class UpdateShiftCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
    public ShiftDto Shift { get; set; } = null!;
}

/// <summary>
/// Validator for UpdateShiftCommand
/// </summary>
public class UpdateShiftCommandValidator : AbstractValidator<UpdateShiftCommand>
{
    public UpdateShiftCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Shift ID is required");

        RuleFor(x => x.Shift.UserId)
            .NotEmpty().WithMessage("User ID is required");

        RuleFor(x => x.Shift.PosDeviceId)
            .NotEmpty().WithMessage("POS Device ID is required");

        RuleFor(x => x.Shift.StartTime)
            .NotEmpty().WithMessage("Start time is required");

        RuleFor(x => x.Shift.Status)
            .IsInEnum().WithMessage("Invalid shift status");

        RuleFor(x => x.Shift.StartCashBalance)
            .GreaterThanOrEqualTo(0).WithMessage("Start cash balance must be greater than or equal to 0");

        RuleFor(x => x.Shift.EndCashBalance)
            .GreaterThanOrEqualTo(0).WithMessage("End cash balance must be greater than or equal to 0")
            .When(x => x.Shift.EndCashBalance.HasValue);

        RuleFor(x => x.Shift.TotalCashSales)
            .GreaterThanOrEqualTo(0).WithMessage("Total cash sales must be greater than or equal to 0");

        RuleFor(x => x.Shift.TotalPosSales)
            .GreaterThanOrEqualTo(0).WithMessage("Total POS sales must be greater than or equal to 0");

        RuleFor(x => x.Shift.TotalTransferSales)
            .GreaterThanOrEqualTo(0).WithMessage("Total transfer sales must be greater than or equal to 0");
    }
}

/// <summary>
/// Handler for updating an existing shift
/// </summary>
public class UpdateShiftCommandHandler(IApplicationDbContext context) : IRequestHandler<UpdateShiftCommand, Response<object>>
{

    public async Task<Response<object>> Handle(UpdateShiftCommand request, CancellationToken cancellationToken)
    {
        // Find existing shift
        var existingShift = await context.Shifts
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);
        if (existingShift == null)
        {
            throw new EntityNotFoundException("Shift", request.Id);
        }

        // Check if user exists
        var userExists = await context.Users
            .AnyAsync(u => u.Id == request.Shift.UserId, cancellationToken);
        if (!userExists)
        {
            throw new EntityNotFoundException("User", request.Shift.UserId);
        }

        // Check if POS device exists
        var posDeviceExists = await context.PosDevices
            .AnyAsync(p => p.Id == request.Shift.PosDeviceId, cancellationToken);
        if (!posDeviceExists)
        {
            throw new EntityNotFoundException("PosDevice", request.Shift.PosDeviceId);
        }

        // Update shift properties
        request.Shift.Adapt(existingShift);
        existingShift.UpdatedAt = DateTime.UtcNow;

        context.Shifts.Update(existingShift);
        var result = await context.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityUpdatedException("Shift", request.Id);
        }

        return new Response<object>(new { Id = existingShift.Id });
    }
}
