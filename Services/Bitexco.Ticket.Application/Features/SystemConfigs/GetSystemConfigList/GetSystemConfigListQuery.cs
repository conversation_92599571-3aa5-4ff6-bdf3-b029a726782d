using System.Text.Json;

namespace Bitexco.Ticket.Application.Features.SystemConfigs.GetSystemConfigList;

/// <summary>
/// Base handler for SystemConfig list queries with shared logic
/// </summary>
/// <typeparam name="TQuery">The query type</typeparam>
/// <typeparam name="TDto">The DTO type to deserialize to</typeparam>
public abstract class BaseSystemConfigListHandler<TQuery, TDto>(IApplicationDbContext dbContext) 
    : IQueryHandler<TQuery, Response<List<TDto>>> 
    where TQuery : IQuery<Response<List<TDto>>> 
    where TDto : class
{
    protected abstract string SystemConfigKey { get; }

    public async Task<Response<List<TDto>>> Handle(TQuery request, CancellationToken cancellationToken)
    {
        // Get SystemConfig with specified key or throw exception
        var systemConfig = await dbContext.SystemConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(sc => sc.Key == SystemConfigKey, cancellationToken)
            ?? throw new SystemConfigNotFoundException(SystemConfigKey);

        // Check if Value is not empty or throw exception
        var jsonValue = !string.IsNullOrWhiteSpace(systemConfig.Value) 
            ? systemConfig.Value 
            : throw new SystemConfigEmptyValueException(SystemConfigKey);

        // Parse JSON value to get list of specified type or throw exception
        var items = DeserializeList(jsonValue);

        return new Response<List<TDto>>(items);
    }

    /// <summary>
    /// Deserialize JSON to list of specified type or throw exception
    /// </summary>
    /// <param name="jsonValue">JSON string to deserialize</param>
    /// <returns>List of items of type TDto</returns>
    private List<TDto> DeserializeList(string jsonValue)
    {
        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        try
        {
            return JsonSerializer.Deserialize<List<TDto>>(jsonValue, options)
                ?? throw new SystemConfigInvalidJsonException(SystemConfigKey);
        }
        catch (JsonException ex)
        {
            throw new SystemConfigInvalidJsonException(SystemConfigKey, ex);
        }
    }
}
