using Bitexco.Ticket.Application.Constants;
using Bitexco.Ticket.Application.Features.SystemConfigs.Dtos;
using Bitexco.Ticket.Application.Features.SystemConfigs.GetSystemConfigList;

namespace Bitexco.Ticket.Application.Features.SystemConfigs.GetPromotionApplyTypes;

/// <summary>
/// Query to get list of Promotion apply types from SystemConfig
/// </summary>
public record GetPromotionApplyTypesQuery : IQuery<Response<List<PromotionApplyTypeDto>>>;

/// <summary>
/// Handler for GetPromotionApplyTypesQuery using base handler logic
/// </summary>
public class GetPromotionApplyTypesQueryHandler(IApplicationDbContext dbContext) 
    : BaseSystemConfigListHandler<GetPromotionApplyTypesQuery, PromotionApplyTypeDto>(dbContext)
{
    protected override string SystemConfigKey => SystemConfigKeys.PromotionApplyTypes;
}
