using Bitexco.Ticket.Application.Constants;
using Bitexco.Ticket.Application.Features.SystemConfigs.Dtos;
using Bitexco.Ticket.Application.Features.SystemConfigs.GetSystemConfigList;

namespace Bitexco.Ticket.Application.Features.SystemConfigs.GetVoucherApplyTypes;

/// <summary>
/// Query to get list of voucher apply types from SystemConfig
/// </summary>
public record GetVoucherApplyTypesQuery : IQuery<Response<List<VoucherApplyTypeDto>>>;

/// <summary>
/// Handler for GetVoucherApplyTypesQuery using base handler logic
/// </summary>
public class GetVoucherApplyTypesQueryHandler(IApplicationDbContext dbContext) 
    : BaseSystemConfigListHandler<GetVoucherApplyTypesQuery, VoucherApplyTypeDto>(dbContext)
{
    protected override string SystemConfigKey => SystemConfigKeys.VoucherApplyTypes;
}
