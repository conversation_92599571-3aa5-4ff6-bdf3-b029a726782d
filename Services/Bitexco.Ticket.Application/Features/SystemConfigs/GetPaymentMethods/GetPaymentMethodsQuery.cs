using Bitexco.Ticket.Application.Constants;
using Bitexco.Ticket.Application.Features.SystemConfigs.Dtos;
using Bitexco.Ticket.Application.Features.SystemConfigs.GetSystemConfigList;

namespace Bitexco.Ticket.Application.Features.SystemConfigs.GetPaymentMethods;

/// <summary>
/// Query to get list of payment methods from SystemConfig
/// </summary>
public record GetPaymentMethodsQuery : IQuery<Response<List<PaymentMethodDto>>>;

/// <summary>
/// Handler for GetPaymentMethodsQuery using base handler logic
/// </summary>
public class GetPaymentMethodsQueryHandler(IApplicationDbContext dbContext) 
    : BaseSystemConfigListHandler<GetPaymentMethodsQuery, PaymentMethodDto>(dbContext)
{
    protected override string SystemConfigKey => SystemConfigKeys.PaymentMethods;
}
