using Bitexco.Ticket.Application.Constants;
using Bitexco.Ticket.Application.Features.SystemConfigs.Dtos;
using Bitexco.Ticket.Application.Features.SystemConfigs.GetSystemConfigList;

namespace Bitexco.Ticket.Application.Features.SystemConfigs.GetRegions;

/// <summary>
/// Query to get list of regions from SystemConfig
/// </summary>
public record GetRegionsQuery : IQuery<Response<List<RegionDto>>>;

/// <summary>
/// Handler for GetRegionsQuery using base handler logic
/// </summary>
public class GetRegionsQueryHandler(IApplicationDbContext dbContext) 
    : BaseSystemConfigListHandler<GetRegionsQuery, RegionDto>(dbContext)
{
    protected override string SystemConfigKey => SystemConfigKeys.Regions;
}
