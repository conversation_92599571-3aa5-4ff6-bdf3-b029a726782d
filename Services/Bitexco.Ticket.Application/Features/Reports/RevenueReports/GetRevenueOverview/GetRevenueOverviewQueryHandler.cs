namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueOverview;

/// <summary>
/// Handler for GetRevenueOverviewQuery
/// Calculates revenue overview and compares with previous period
/// </summary>
public class GetRevenueOverviewQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetRevenueOverviewQuery, Response<RevenueOverviewResponseDto>>
{
    public async Task<Response<RevenueOverviewResponseDto>> Handle(GetRevenueOverviewQuery request, CancellationToken cancellationToken)
    {
        // Calculate current period revenue statistics
        var currentPeriodStats = await GetPeriodRevenueStats(request.DateFrom, request.DateTo, cancellationToken);
        
        // Calculate previous period dates (same duration before the current period)
        var periodDuration = request.DateTo - request.DateFrom;
        var previousDateTo = request.DateFrom.AddTicks(-1); // End just before current period starts
        var previousDateFrom = previousDateTo.Subtract(periodDuration);
        
        // Calculate previous period revenue statistics
        var previousPeriodStats = await GetPeriodRevenueStats(previousDateFrom, previousDateTo, cancellationToken);
        
        // Calculate percentage changes for all fields
        var totalActualRevenueChangePercent = CalculatePercentageChange(previousPeriodStats.TotalActualRevenue, currentPeriodStats.TotalActualRevenue);
        var ticketsSoldChangePercent = CalculatePercentageChange(previousPeriodStats.TicketsSold, currentPeriodStats.TicketsSold);
        var ticketsCancelledChangePercent = CalculatePercentageChange(previousPeriodStats.TicketsCancelled, currentPeriodStats.TicketsCancelled);
        var retailRevenueChangePercent = CalculatePercentageChange(previousPeriodStats.RetailRevenue, currentPeriodStats.RetailRevenue);
        var agentRevenueChangePercent = CalculatePercentageChange(previousPeriodStats.AgentRevenue, currentPeriodStats.AgentRevenue);
        var collaboratorRevenueChangePercent = CalculatePercentageChange(previousPeriodStats.CollaboratorRevenue, currentPeriodStats.CollaboratorRevenue);        
        var result = new RevenueOverviewResponseDto
        {
            // Current period values
            TotalActualRevenue = currentPeriodStats.TotalActualRevenue,
            TicketsSold = currentPeriodStats.TicketsSold,
            TicketsCancelled = currentPeriodStats.TicketsCancelled,
            RetailRevenue = currentPeriodStats.RetailRevenue,
            AgentRevenue = currentPeriodStats.AgentRevenue,
            CollaboratorRevenue = currentPeriodStats.CollaboratorRevenue,
            
            // Percentage changes
            TotalActualRevenueChangePercent = totalActualRevenueChangePercent,
            TicketsSoldChangePercent = ticketsSoldChangePercent,
            TicketsCancelledChangePercent = ticketsCancelledChangePercent,
            RetailRevenueChangePercent = retailRevenueChangePercent,
            AgentRevenueChangePercent = agentRevenueChangePercent,
            CollaboratorRevenueChangePercent = collaboratorRevenueChangePercent,
            
            // Previous period values for reference
            PreviousPeriodTotalActualRevenue = previousPeriodStats.TotalActualRevenue,
            PreviousPeriodTicketsSold = previousPeriodStats.TicketsSold,
            PreviousPeriodTicketsCancelled = previousPeriodStats.TicketsCancelled,
            PreviousPeriodRetailRevenue = previousPeriodStats.RetailRevenue,
            PreviousPeriodAgentRevenue = previousPeriodStats.AgentRevenue,
            PreviousPeriodCollaboratorRevenue = previousPeriodStats.CollaboratorRevenue,
        };
        
        return new Response<RevenueOverviewResponseDto>(result);
    }    
    /// <summary>
    /// Get revenue statistics for a specific period
    /// </summary>
    private async Task<RevenuePeriodStats> GetPeriodRevenueStats(
        DateTime dateFrom, 
        DateTime dateTo, 
        CancellationToken cancellationToken)
    {
        // Get all orders in the period
        var orders = await context.Orders
            .Include(o => o.Agent)
            .Include(o => o.OrderItems)
            .Where(o => o.CreatedAt >= dateFrom && o.CreatedAt <= dateTo)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
            
        // Calculate total actual revenue from paid orders
        var paidOrders = orders.Where(o => o.Status == OrderStatus.paid).ToList();
        var totalActualRevenue = paidOrders.Sum(o => o.FinalAmount);
        
        // Calculate tickets sold (from paid orders)
        var ticketsSold = paidOrders.SelectMany(o => o.OrderItems ?? new List<OrderItem>()).Sum(oi => oi.Quantity);
        
        // Calculate tickets cancelled (from cancelled and refunded orders)
        var cancelledOrders = orders.Where(o => o.Status == OrderStatus.cancelled || o.Status == OrderStatus.refunded).ToList();
        var ticketsCancelled = cancelledOrders.SelectMany(o => o.OrderItems ?? new List<OrderItem>()).Sum(oi => oi.Quantity);
        
        // Calculate revenue by source (only from paid orders)
        var retailRevenue = paidOrders.Where(o => o.AgentId == null).Sum(o => o.FinalAmount); // No agent = retail
        var agentRevenue = paidOrders.Where(o => o.Agent != null && o.Agent.Type == AgentType.agent).Sum(o => o.FinalAmount);
        var collaboratorRevenue = paidOrders.Where(o => o.Agent != null && o.Agent.Type == AgentType.collaborator).Sum(o => o.FinalAmount);        
        return new RevenuePeriodStats
        {
            TotalActualRevenue = totalActualRevenue,
            TicketsSold = ticketsSold,
            TicketsCancelled = ticketsCancelled,
            RetailRevenue = retailRevenue,
            AgentRevenue = agentRevenue,
            CollaboratorRevenue = collaboratorRevenue
        };
    }
    
    /// <summary>
    /// Calculate percentage change between two values
    /// </summary>
    private static decimal CalculatePercentageChange(decimal previousValue, decimal currentValue)
    {
        if (previousValue == 0)
        {
            return currentValue > 0 ? 100 : 0;
        }
        
        var change = ((currentValue - previousValue) / previousValue) * 100;
        return Math.Round(change, 2);
    }
}

/// <summary>
/// Internal class to hold revenue statistics for a period
/// </summary>
internal class RevenuePeriodStats
{
    public decimal TotalActualRevenue { get; set; }
    public int TicketsSold { get; set; }
    public int TicketsCancelled { get; set; }
    public decimal RetailRevenue { get; set; }
    public decimal AgentRevenue { get; set; }
    public decimal CollaboratorRevenue { get; set; }
}