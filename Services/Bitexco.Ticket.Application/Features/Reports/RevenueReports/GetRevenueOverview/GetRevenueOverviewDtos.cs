namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueOverview;

/// <summary>
/// Response DTO for revenue overview with comparison to previous period
/// </summary>
public record RevenueOverviewResponseDto
{
    /// <summary>
    /// Tổng doanh thu thực tế
    /// </summary>
    public decimal TotalActualRevenue { get; set; }
    
    /// <summary>
    /// Số vé bán ra
    /// </summary>
    public int TicketsSold { get; set; }
    
    /// <summary>
    /// Số vé huỷ
    /// </summary>
    public int TicketsCancelled { get; set; }
    
    /// <summary>
    /// Doanh thu từ bán lẻ
    /// </summary>
    public decimal RetailRevenue { get; set; }
    
    /// <summary>
    /// Doanh thu từ Đại lý
    /// </summary>
    public decimal AgentRevenue { get; set; }
    
    /// <summary>
    /// Doanh thu từ Cộng tác viên
    /// </summary>
    public decimal CollaboratorRevenue { get; set; }
    
    // Percentage changes compared to previous period
    /// <summary>
    /// Phần trăm thay đổi tổng doanh thu thực tế so với cùng kỳ trước đó
    /// </summary>
    public decimal TotalActualRevenueChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi số vé bán ra so với cùng kỳ trước đó
    /// </summary>
    public decimal TicketsSoldChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi số vé huỷ so với cùng kỳ trước đó
    /// </summary>
    public decimal TicketsCancelledChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi doanh thu từ bán lẻ so với cùng kỳ trước đó
    /// </summary>
    public decimal RetailRevenueChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi doanh thu từ Đại lý so với cùng kỳ trước đó
    /// </summary>
    public decimal AgentRevenueChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi doanh thu từ Cộng tác viên so với cùng kỳ trước đó
    /// </summary>
    public decimal CollaboratorRevenueChangePercent { get; set; }
    
    // Previous period values for reference
    /// <summary>
    /// Previous period total actual revenue for reference
    /// </summary>
    public decimal PreviousPeriodTotalActualRevenue { get; set; }
    
    /// <summary>
    /// Previous period tickets sold for reference
    /// </summary>
    public int PreviousPeriodTicketsSold { get; set; }
    
    /// <summary>
    /// Previous period tickets cancelled for reference
    /// </summary>
    public int PreviousPeriodTicketsCancelled { get; set; }
    
    /// <summary>
    /// Previous period retail revenue for reference
    /// </summary>
    public decimal PreviousPeriodRetailRevenue { get; set; }
    
    /// <summary>
    /// Previous period agent revenue for reference
    /// </summary>
    public decimal PreviousPeriodAgentRevenue { get; set; }
    
    /// <summary>
    /// Previous period collaborator revenue for reference
    /// </summary>
    public decimal PreviousPeriodCollaboratorRevenue { get; set; }
}