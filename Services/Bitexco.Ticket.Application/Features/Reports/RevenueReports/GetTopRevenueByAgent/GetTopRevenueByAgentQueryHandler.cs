namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetTopRevenueByAgent;

/// <summary>
/// Handler for GetTopRevenueByAgentQuery
/// Returns top 3 revenue by agent type (CTV or Agency)
/// </summary>
public class GetTopRevenueByAgentQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetTopRevenueByAgentQuery, Response<List<TopRevenueByAgentResponseDto>>>
{
    public async Task<Response<List<TopRevenueByAgentResponseDto>>> Handle(GetTopRevenueByAgentQuery request, CancellationToken cancellationToken)
    {
        // Validate and convert type parameter
        var agentType = GetAgentTypeFromString(request.Type);
        
        // Get top 3 revenue by agent type
        var topRevenue = await context.Orders
            .Include(o => o.Agent)
            .Where(o => o.CreatedAt >= request.DateFrom && o.CreatedAt <= request.DateTo)
            .Where(o => o.Status == OrderStatus.paid)
            .Where(o => o.Agent != null && o.Agent.Type == agentType && o.Agent.IsActive)
            .GroupBy(o => new { o.Agent!.Id, o.Agent.Name, o.Agent.Code })
            .Select(g => new TopRevenueByAgentResponseDto
            {
                Name = g.Key.Name,
                Code = g.Key.Code,
                Revenue = g.Sum(o => o.FinalAmount)
            })
            .OrderByDescending(x => x.Revenue)
            .Take(3)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
            
        return new Response<List<TopRevenueByAgentResponseDto>>(topRevenue);
    }    
    /// <summary>
    /// Convert string type to AgentType enum
    /// </summary>
    private static AgentType GetAgentTypeFromString(string type)
    {
        return type.ToLower() switch
        {
            "ctv" => AgentType.collaborator,
            "agency" => AgentType.agent,
            _ => throw new ArgumentException($"Invalid agent type: {type}. Must be 'ctv' or 'agency'")
        };
    }
}