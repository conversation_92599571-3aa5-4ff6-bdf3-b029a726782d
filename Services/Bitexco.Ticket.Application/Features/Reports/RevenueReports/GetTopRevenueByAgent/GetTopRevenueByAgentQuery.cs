namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetTopRevenueByAgent;

/// <summary>
/// Query to get top 3 revenue by agent type (CTV or Agency)
/// </summary>
public class GetTopRevenueByAgentQuery : IRequest<Response<List<TopRevenueByAgentResponseDto>>>
{
    /// <summary>
    /// Start date for revenue calculation
    /// </summary>
    public DateTime DateFrom { get; set; }
    
    /// <summary>
    /// End date for revenue calculation
    /// </summary>
    public DateTime DateTo { get; set; }
    
    /// <summary>
    /// Agent type filter: "ctv" for collaborator or "agency" for agent
    /// </summary>
    public string Type { get; set; } = null!;
}