namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueByPaymentMethod;

/// <summary>
/// Handler for GetRevenueByPaymentMethodQuery
/// Returns revenue breakdown by payment method
/// </summary>
public class GetRevenueByPaymentMethodQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetRevenueByPaymentMethodQuery, Response<List<RevenueByPaymentMethodResponseDto>>>
{
    public async Task<Response<List<RevenueByPaymentMethodResponseDto>>> Handle(GetRevenueByPaymentMethodQuery request, CancellationToken cancellationToken)
    {
        // Get revenue data grouped by payment method from payment transactions
        var revenueByPaymentMethod = await context.PaymentTransactions
            .Include(pt => pt.Order)
            .Where(pt => pt.Order != null)
            .Where(pt => pt.Order!.CreatedAt >= request.DateFrom && pt.Order.CreatedAt <= request.DateTo)
            .Where(pt => pt.Order!.Status == OrderStatus.paid)
            .Where(pt => pt.TransactionType == PaymentTransactionType.income) // Only income transactions
            .GroupBy(pt => pt.PaymentMethod)
            .Select(g => new RevenueByPaymentMethodResponseDto
            {
                PaymentMethodName = GetPaymentMethodDisplayName(g.Key),
                Revenue = g.Sum(pt => pt.Amount)
            })
            .OrderByDescending(x => x.Revenue)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
            
        return new Response<List<RevenueByPaymentMethodResponseDto>>(revenueByPaymentMethod);
    }
    
    /// <summary>
    /// Get display name for payment method
    /// </summary>
    private static string GetPaymentMethodDisplayName(PaymentTransactionMethod paymentMethod)
    {
        return paymentMethod switch
        {
            PaymentTransactionMethod.cash => "Tiền mặt",
            PaymentTransactionMethod.pos => "Thẻ tín dụng/Debit",
            PaymentTransactionMethod.bank_transfer => "Chuyển khoản ngân hàng", 
            PaymentTransactionMethod.debt => "Công nợ",
            PaymentTransactionMethod.vnpay => "VNPay",
            PaymentTransactionMethod.zalopay => "ZaloPay",
            PaymentTransactionMethod.momo => "MoMo",
            _ => "Khác"
        };
    }
}