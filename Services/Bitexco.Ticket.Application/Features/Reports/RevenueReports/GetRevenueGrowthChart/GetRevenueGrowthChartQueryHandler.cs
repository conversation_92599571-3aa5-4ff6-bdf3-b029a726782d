namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueGrowthChart;

/// <summary>
/// Handler for GetRevenueGrowthChartQuery
/// Returns revenue growth chart data with different period groupings
/// </summary>
public class GetRevenueGrowthChartQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetRevenueGrowthChartQuery, Response<List<RevenueGrowthChartDataPointDto>>>
{
    public async Task<Response<List<RevenueGrowthChartDataPointDto>>> Handle(GetRevenueGrowthChartQuery request, CancellationToken cancellationToken)
    {
        // Get all paid orders in the period
        var orders = await context.Orders
            .Where(o => o.CreatedAt >= request.DateFrom && o.CreatedAt <= request.DateTo)
            .Where(o => o.Status == OrderStatus.paid)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
            
        // Determine the period type based on date range
        var periodType = DeterminePeriodType(request.DateFrom, request.DateTo);
        
        // Group data by period type
        var chartData = GroupDataByPeriod(orders, periodType, request.DateFrom, request.DateTo);
        
        return new Response<List<RevenueGrowthChartDataPointDto>>(chartData);
    }
    
    /// <summary>
    /// Determine the appropriate period type based on date range
    /// </summary>
    private static ChartPeriodType DeterminePeriodType(DateTime dateFrom, DateTime dateTo)
    {
        var timeSpan = dateTo - dateFrom;        
        // Check if it's today (same day)
        if (dateFrom.Date == dateTo.Date && dateFrom.Date == DateTime.Today)
        {
            return ChartPeriodType.Hourly; // 24 hours
        }
        
        // Check if it's this week
        var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
        var endOfWeek = startOfWeek.AddDays(6);
        if (dateFrom.Date == startOfWeek && dateTo.Date == endOfWeek)
        {
            return ChartPeriodType.Daily; // Days of week
        }
        
        // Check if it's a full year range
        if (timeSpan.TotalDays >= 350) // Approximately a year
        {
            return ChartPeriodType.Monthly; // Months of year
        }
        
        // Default: Daily grouping for other ranges
        return ChartPeriodType.Daily;
    }
    
    /// <summary>
    /// Group orders data by the specified period type
    /// </summary>
    private static List<RevenueGrowthChartDataPointDto> GroupDataByPeriod(
        List<Order> orders, 
        ChartPeriodType periodType, 
        DateTime dateFrom, 
        DateTime dateTo)
    {        return periodType switch
        {
            ChartPeriodType.Hourly => GroupByHour(orders, dateFrom),
            ChartPeriodType.Daily => GroupByDay(orders, dateFrom, dateTo),
            ChartPeriodType.Monthly => GroupByMonth(orders, dateFrom, dateTo),
            _ => GroupByDay(orders, dateFrom, dateTo)
        };
    }
    
    /// <summary>
    /// Group orders by hour (24 hours of a day)
    /// </summary>
    private static List<RevenueGrowthChartDataPointDto> GroupByHour(List<Order> orders, DateTime targetDate)
    {
        var result = new List<RevenueGrowthChartDataPointDto>();
        
        for (int hour = 0; hour < 24; hour++)
        {
            var hourOrders = orders.Where(o => o.CreatedAt.HasValue && o.CreatedAt.Value.Hour == hour).ToList();
            var revenue = hourOrders.Sum(o => o.FinalAmount);
            
            result.Add(new RevenueGrowthChartDataPointDto
            {
                Period = $"{hour:00}:00",
                Revenue = revenue
            });
        }
        
        return result;
    }    
    /// <summary>
    /// Group orders by day
    /// </summary>
    private static List<RevenueGrowthChartDataPointDto> GroupByDay(List<Order> orders, DateTime dateFrom, DateTime dateTo)
    {
        var result = new List<RevenueGrowthChartDataPointDto>();
        var currentDate = dateFrom.Date;
        
        while (currentDate <= dateTo.Date)
        {
            var dayOrders = orders.Where(o => o.CreatedAt.HasValue && o.CreatedAt.Value.Date == currentDate).ToList();
            var revenue = dayOrders.Sum(o => o.FinalAmount);
            
            result.Add(new RevenueGrowthChartDataPointDto
            {
                Period = currentDate.ToString("yyyy-MM-dd"),
                Revenue = revenue
            });
            
            currentDate = currentDate.AddDays(1);
        }
        
        return result;
    }
    
    /// <summary>
    /// Group orders by month
    /// </summary>
    private static List<RevenueGrowthChartDataPointDto> GroupByMonth(List<Order> orders, DateTime dateFrom, DateTime dateTo)
    {        var result = new List<RevenueGrowthChartDataPointDto>();
        var currentMonth = new DateTime(dateFrom.Year, dateFrom.Month, 1);
        var endMonth = new DateTime(dateTo.Year, dateTo.Month, 1);
        
        while (currentMonth <= endMonth)
        {
            var monthOrders = orders.Where(o => o.CreatedAt.HasValue && o.CreatedAt.Value.Year == currentMonth.Year && o.CreatedAt.Value.Month == currentMonth.Month).ToList();
            var revenue = monthOrders.Sum(o => o.FinalAmount);
            
            result.Add(new RevenueGrowthChartDataPointDto
            {
                Period = currentMonth.ToString("yyyy-MM"),
                Revenue = revenue
            });
            
            currentMonth = currentMonth.AddMonths(1);
        }
        
        return result;
    }
}
