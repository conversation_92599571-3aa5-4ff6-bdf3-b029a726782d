namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueGrowthChart;

/// <summary>
/// Handler for GetRevenueGrowthChartQuery
/// Returns revenue growth chart data with different period groupings
/// Properly handles timezone conversion between Vietnam local time and UTC
/// </summary>
public class GetRevenueGrowthChartQueryHandler(IApplicationDbContext context)
    : IRequestHandler<GetRevenueGrowthChartQuery, Response<List<RevenueGrowthChartDataPointDto>>>
{
    // Vietnam timezone for proper local time handling
    private static readonly TimeZoneInfo VietnamTimeZone = TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time");
    public async Task<Response<List<RevenueGrowthChartDataPointDto>>> Handle(GetRevenueGrowthChartQuery request, CancellationToken cancellationToken)
    {
        // Convert input dates from Vietnam local time to UTC for database queries
        var utcDateFrom = ConvertLocalToUtc(request.DateFrom);
        var utcDateTo = ConvertLocalToUtc(request.DateTo);

        // Get all paid orders in the period (using UTC time for database query)
        var orders = await context.Orders
            .Where(o => o.CreatedAt >= utcDateFrom && o.CreatedAt <= utcDateTo)
            .Where(o => o.Status == OrderStatus.paid)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        // Determine the period type based on local date range
        var periodType = DeterminePeriodType(request.DateFrom, request.DateTo);

        // Group data by period type (using local time for proper grouping)
        var chartData = GroupDataByPeriod(orders, periodType, request.DateFrom, request.DateTo);

        return new Response<List<RevenueGrowthChartDataPointDto>>(chartData);
    }

    /// <summary>
    /// Convert Vietnam local time to UTC for database operations
    /// </summary>
    private static DateTime ConvertLocalToUtc(DateTime localTime)
    {
        return TimeZoneInfo.ConvertTimeToUtc(localTime, VietnamTimeZone);
    }

    /// <summary>
    /// Convert UTC time to Vietnam local time for display and grouping
    /// </summary>
    private static DateTime ConvertUtcToLocal(DateTime utcTime)
    {
        return TimeZoneInfo.ConvertTimeFromUtc(utcTime, VietnamTimeZone);
    }

    /// <summary>
    /// Get current date in Vietnam timezone
    /// </summary>
    private static DateTime GetCurrentLocalDate()
    {
        return TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, VietnamTimeZone);
    }
    
    /// <summary>
    /// Determine the appropriate period type based on date range
    /// Uses Vietnam local time for proper date comparison
    /// </summary>
    private static ChartPeriodType DeterminePeriodType(DateTime dateFrom, DateTime dateTo)
    {
        var timeSpan = dateTo - dateFrom;
        var today = GetCurrentLocalDate().Date;

        // Check if it's today (same day)
        if (dateFrom.Date == dateTo.Date && dateFrom.Date == today)
        {
            return ChartPeriodType.Hourly; // 24 hours
        }

        // Check if it's this week (Monday-based week calculation)
        var startOfWeek = today.AddDays(-(int)(today.DayOfWeek + 6) % 7); // Monday = 0
        var endOfWeek = startOfWeek.AddDays(6);
        if (dateFrom.Date == startOfWeek && dateTo.Date == endOfWeek)
        {
            return ChartPeriodType.Daily; // Days of week
        }

        // Check if it's a full year range
        if (timeSpan.TotalDays >= 350) // Approximately a year
        {
            return ChartPeriodType.Monthly; // Months of year
        }

        // Default: Daily grouping for other ranges
        return ChartPeriodType.Daily;
    }
    
    /// <summary>
    /// Group orders data by the specified period type
    /// Uses Vietnam local time for proper grouping and display
    /// </summary>
    private static List<RevenueGrowthChartDataPointDto> GroupDataByPeriod(
        List<Order> orders,
        ChartPeriodType periodType,
        DateTime dateFrom,
        DateTime dateTo)
    {
        return periodType switch
        {
            ChartPeriodType.Hourly => GroupByHour(orders),
            ChartPeriodType.Daily => GroupByDay(orders, dateFrom, dateTo),
            ChartPeriodType.Monthly => GroupByMonth(orders, dateFrom, dateTo),
            _ => GroupByDay(orders, dateFrom, dateTo)
        };
    }
    
    /// <summary>
    /// Group orders by hour (24 hours of a day)
    /// Converts UTC time to Vietnam local time for proper hour grouping
    /// </summary>
    private static List<RevenueGrowthChartDataPointDto> GroupByHour(List<Order> orders)
    {
        var result = new List<RevenueGrowthChartDataPointDto>();

        for (int hour = 0; hour < 24; hour++)
        {
            // Convert UTC time to Vietnam local time before checking hour
            var hourOrders = orders.Where(o => o.CreatedAt.HasValue &&
                ConvertUtcToLocal(o.CreatedAt.Value).Hour == hour).ToList();
            var revenue = hourOrders.Sum(o => o.FinalAmount);

            result.Add(new RevenueGrowthChartDataPointDto
            {
                Period = $"{hour:00}:00",
                Revenue = revenue
            });
        }

        return result;
    }
    /// <summary>
    /// Group orders by day
    /// Converts UTC time to Vietnam local time for proper day grouping
    /// </summary>
    private static List<RevenueGrowthChartDataPointDto> GroupByDay(List<Order> orders, DateTime dateFrom, DateTime dateTo)
    {
        var result = new List<RevenueGrowthChartDataPointDto>();
        var currentDate = dateFrom.Date;

        while (currentDate <= dateTo.Date)
        {
            // Convert UTC time to Vietnam local time before comparing dates
            var dayOrders = orders.Where(o => o.CreatedAt.HasValue &&
                ConvertUtcToLocal(o.CreatedAt.Value).Date == currentDate).ToList();
            var revenue = dayOrders.Sum(o => o.FinalAmount);

            result.Add(new RevenueGrowthChartDataPointDto
            {
                Period = currentDate.ToString("yyyy-MM-dd"),
                Revenue = revenue
            });

            currentDate = currentDate.AddDays(1);
        }

        return result;
    }
    
    /// <summary>
    /// Group orders by month
    /// Converts UTC time to Vietnam local time for proper month grouping
    /// </summary>
    private static List<RevenueGrowthChartDataPointDto> GroupByMonth(List<Order> orders, DateTime dateFrom, DateTime dateTo)
    {
        var result = new List<RevenueGrowthChartDataPointDto>();
        var currentMonth = new DateTime(dateFrom.Year, dateFrom.Month, 1);
        var endMonth = new DateTime(dateTo.Year, dateTo.Month, 1);

        while (currentMonth <= endMonth)
        {
            // Convert UTC time to Vietnam local time before comparing year and month
            var monthOrders = orders.Where(o => o.CreatedAt.HasValue)
                .Where(o =>
                {
                    var localTime = ConvertUtcToLocal(o.CreatedAt!.Value);
                    return localTime.Year == currentMonth.Year && localTime.Month == currentMonth.Month;
                })
                .ToList();
            var revenue = monthOrders.Sum(o => o.FinalAmount);

            result.Add(new RevenueGrowthChartDataPointDto
            {
                Period = currentMonth.ToString("yyyy-MM"),
                Revenue = revenue
            });

            currentMonth = currentMonth.AddMonths(1);
        }

        return result;
    }
}
