namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueBySalesChannel;

/// <summary>
/// Handler for GetRevenueBySalesChannelQuery
/// Returns revenue breakdown by sales channel
/// </summary>
public class GetRevenueBySalesChannelQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetRevenueBySalesChannelQuery, Response<List<RevenueBySalesChannelResponseDto>>>
{
    public async Task<Response<List<RevenueBySalesChannelResponseDto>>> Handle(GetRevenueBySalesChannelQuery request, CancellationToken cancellationToken)
    {
        // Get revenue data grouped by retail channel
        var revenueByChannel = await context.Orders
            .Include(o => o.RetailChannel)
            .Where(o => o.CreatedAt >= request.DateFrom && o.CreatedAt <= request.DateTo)
            .Where(o => o.Status == OrderStatus.paid)
            .GroupBy(o => o.RetailChannel != null ? o.RetailChannel.Name : "Unknown")
            .Select(g => new RevenueBySalesChannelResponseDto
            {
                ChannelName = g.Key,
                Revenue = g.Sum(o => o.FinalAmount)
            })
            .OrderByDescending(x => x.Revenue)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
            
        return new Response<List<RevenueBySalesChannelResponseDto>>(revenueByChannel);
    }
}