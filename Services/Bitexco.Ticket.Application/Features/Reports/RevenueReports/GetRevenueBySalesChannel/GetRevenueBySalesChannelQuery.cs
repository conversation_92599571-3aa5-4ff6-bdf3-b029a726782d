namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueBySalesChannel;

/// <summary>
/// Query to get revenue breakdown by sales channel
/// </summary>
public class GetRevenueBySalesChannelQuery : IRequest<Response<List<RevenueBySalesChannelResponseDto>>>
{
    /// <summary>
    /// Start date for revenue calculation
    /// </summary>
    public DateTime DateFrom { get; set; }
    
    /// <summary>
    /// End date for revenue calculation
    /// </summary>
    public DateTime DateTo { get; set; }
}