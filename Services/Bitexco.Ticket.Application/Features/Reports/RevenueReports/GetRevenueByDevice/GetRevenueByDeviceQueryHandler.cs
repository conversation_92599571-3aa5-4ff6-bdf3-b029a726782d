namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueByDevice;

/// <summary>
/// Handler for GetRevenueByDeviceQuery
/// Returns revenue breakdown by POS device
/// </summary>
public class GetRevenueByDeviceQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetRevenueByDeviceQuery, Response<List<RevenueByDeviceResponseDto>>>
{
    public async Task<Response<List<RevenueByDeviceResponseDto>>> Handle(GetRevenueByDeviceQuery request, CancellationToken cancellationToken)
    {
        // Get revenue data grouped by POS device
        var revenueByDevice = await context.Orders
            .Include(o => o.PosDevice)
            .Where(o => o.CreatedAt >= request.DateFrom && o.CreatedAt <= request.DateTo)
            .Where(o => o.Status == OrderStatus.paid)
            .Where(o => o.PosDeviceId != null) // Only orders with device
            .GroupBy(o => new { o.PosDeviceId, DeviceName = o.PosDevice!.Name })
            .Select(g => new RevenueByDeviceResponseDto
            {
                DeviceId = g.Key.PosDeviceId!.Value,
                DeviceName = g.Key.DeviceName,
                Revenue = g.Sum(o => o.FinalAmount)
            })
            .OrderByDescending(x => x.Revenue)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
            
        return new Response<List<RevenueByDeviceResponseDto>>(revenueByDevice);
    }
}