namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueByDevice;

/// <summary>
/// Query to get revenue breakdown by POS device
/// </summary>
public class GetRevenueByDeviceQuery : IRequest<Response<List<RevenueByDeviceResponseDto>>>
{
    /// <summary>
    /// Start date for revenue calculation
    /// </summary>
    public DateTime DateFrom { get; set; }
    
    /// <summary>
    /// End date for revenue calculation
    /// </summary>
    public DateTime DateTo { get; set; }
}