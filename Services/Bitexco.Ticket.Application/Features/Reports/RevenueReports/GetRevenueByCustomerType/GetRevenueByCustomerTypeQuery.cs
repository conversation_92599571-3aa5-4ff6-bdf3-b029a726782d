namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueByCustomerType;

/// <summary>
/// Query to get revenue breakdown by customer type
/// </summary>
public class GetRevenueByCustomerTypeQuery : IRequest<Response<List<RevenueByCustomerTypeResponseDto>>>
{
    /// <summary>
    /// Start date for revenue calculation
    /// </summary>
    public DateTime DateFrom { get; set; }
    
    /// <summary>
    /// End date for revenue calculation
    /// </summary>
    public DateTime DateTo { get; set; }
}