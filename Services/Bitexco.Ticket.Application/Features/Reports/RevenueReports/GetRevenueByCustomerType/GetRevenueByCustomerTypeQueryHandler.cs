namespace Bitexco.Ticket.Application.Features.Reports.RevenueReports.GetRevenueByCustomerType;

/// <summary>
/// Handler for GetRevenueByCustomerTypeQuery
/// Returns revenue breakdown by customer type
/// </summary>
public class GetRevenueByCustomerTypeQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetRevenueByCustomerTypeQuery, Response<List<RevenueByCustomerTypeResponseDto>>>
{
    public async Task<Response<List<RevenueByCustomerTypeResponseDto>>> Handle(GetRevenueByCustomerTypeQuery request, CancellationToken cancellationToken)
    {
        // Get revenue data grouped by customer type
        var revenueByCustomerType = await context.Orders
            .Where(o => o.CreatedAt >= request.DateFrom && o.CreatedAt <= request.DateTo)
            .Where(o => o.Status == OrderStatus.paid)
            .GroupBy(o => o.CustomerType)
            .Select(g => new RevenueByCustomerTypeResponseDto
            {
                CustomerTypeName = GetCustomerTypeDisplayName(g.Key),
                Revenue = g.Sum(o => o.FinalAmount)
            })
            .OrderByDescending(x => x.Revenue)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
            
        return new Response<List<RevenueByCustomerTypeResponseDto>>(revenueByCustomerType);
    }
    
    /// <summary>
    /// Get display name for customer type
    /// </summary>
    private static string GetCustomerTypeDisplayName(CustomerType customerType)
    {
        return customerType switch
        {
            CustomerType.individual => "Cá nhân",
            CustomerType.corporate => "Doanh nghiệp", 
            CustomerType.ship => "Tàu thuyền",
            _ => "Khác"
        };
    }
}