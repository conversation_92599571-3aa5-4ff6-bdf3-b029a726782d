namespace Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetAgentCommissionDetails;

/// <summary>
/// Handler for GetAgentCommissionDetailsQuery
/// Returns paginated list of agent commission details
/// </summary>
public class GetAgentCommissionDetailsQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetAgentCommissionDetailsQuery, PaginationResponse<AgentCommissionDetailResponseDto>>
{
    public async Task<PaginationResponse<AgentCommissionDetailResponseDto>> Handle(GetAgentCommissionDetailsQuery request, CancellationToken cancellationToken)
    {
        // Start with base query - filter for agents only (not collaborators)
        var agentsQuery = context.Agents
            .Where(a => a.Type == AgentType.agent && a.IsActive)
            .AsQueryable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            var searchTerm = request.Search.Trim().ToLower();
            agentsQuery = agentsQuery.Where(a => 
                a.Name.ToLower().Contains(searchTerm) || 
                a.Code.ToLower().Contains(searchTerm));
        }

        // Get all agents that match the criteria
        var agents = await agentsQuery
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        if (!agents.Any())
        {
            return new PaginationResponse<AgentCommissionDetailResponseDto>(
                request.PageIndex, request.PageSize, 0, new List<AgentCommissionDetailResponseDto>());
        }

        // Calculate commission details for each agent
        var commissionDetails = new List<AgentCommissionDetailResponseDto>();
        foreach (var agent in agents)
        {
            var detail = await CalculateAgentCommissionDetail(agent, request.DateFrom, request.DateTo, cancellationToken);
            if (detail != null) // Only add agents that have data in the period
            {
                commissionDetails.Add(detail);
            }
        }

        // Apply date filter to commission details if no data found in period
        if (request.DateFrom.HasValue || request.DateTo.HasValue)
        {
            // Commission details are already filtered by date in CalculateAgentCommissionDetail
            // This is just a placeholder for any additional date filtering if needed
        }

        // Get total count before sorting and pagination
        var totalCount = commissionDetails.Count;

        // Apply sorting
        var sortedDetails = ApplySorting(commissionDetails, request.SortBy, request.IsDescending);

        // Apply pagination
        var paginatedDetails = sortedDetails
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return new PaginationResponse<AgentCommissionDetailResponseDto>(
            request.PageIndex, request.PageSize, totalCount, paginatedDetails);
    }

    /// <summary>
    /// Calculate commission details for a specific agent in the given period
    /// </summary>
    private async Task<AgentCommissionDetailResponseDto?> CalculateAgentCommissionDetail(
        Agent agent, 
        DateTime? dateFrom, 
        DateTime? dateTo, 
        CancellationToken cancellationToken)
    {        // Build orders query with date filter
        var ordersQuery = context.Orders
            .Where(o => o.AgentId == agent.Id)
            .Where(o => o.Status != OrderStatus.cancelled && o.Status != OrderStatus.refunded);

        // Apply date filters if provided
        if (dateFrom.HasValue)
        {
            ordersQuery = ordersQuery.Where(o => o.CreatedAt >= dateFrom.Value);
        }
        if (dateTo.HasValue)
        {
            ordersQuery = ordersQuery.Where(o => o.CreatedAt <= dateTo.Value);
        }

        // Get orders for this agent in the period
        var orders = await ordersQuery
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        // If no orders in period, exclude this agent
        if (!orders.Any())
        {
            return null;
        }

        // Calculate revenue and order count
        var revenue = orders.Sum(o => o.TotalAmount);
        var orderCount = orders.Count;

        // Get commission paid (đã chi)
        var commissionPaid = await GetAgentCommissionPaid(agent.Id, dateFrom, dateTo, cancellationToken);

        // Get commission recognized (ghi nhận)
        var commissionRecognized = await GetAgentCommissionRecognized(agent.Id, dateFrom, dateTo, cancellationToken);

        // Commission percentage from agent's settings
        var commissionPercentage = agent.CommissionRatePercentage ?? 0;
        return new AgentCommissionDetailResponseDto
        {
            Name = agent.Name,
            Code = agent.Code,
            Revenue = revenue,
            CommissionPercentage = commissionPercentage,
            OrderCount = orderCount,
            CommissionRecognized = commissionRecognized,
            CommissionPaid = commissionPaid
        };
    }

    /// <summary>
    /// Get commission paid for an agent in the specified period
    /// </summary>
    private async Task<decimal> GetAgentCommissionPaid(Guid agentId, DateTime? dateFrom, DateTime? dateTo, CancellationToken cancellationToken)
    {
        // From CommissionPayment
        var paymentQuery = context.CommissionPayments
            .Where(cp => cp.AgentId == agentId)
            .Where(cp => cp.Status == CommissionPaymentStatus.paid);

        if (dateFrom.HasValue)
        {
            paymentQuery = paymentQuery.Where(cp => cp.PaymentDate >= dateFrom.Value);
        }
        if (dateTo.HasValue)
        {
            paymentQuery = paymentQuery.Where(cp => cp.PaymentDate <= dateTo.Value);
        }

        var paymentAmount = await paymentQuery.SumAsync(cp => cp.Amount, cancellationToken);

        // From CommissionOrderHistory
        var orderHistoryQuery = context.CommissionOrderHistories
            .Where(coh => coh.AgentId == agentId)
            .Where(coh => coh.PaymentDate.HasValue)
            .Where(coh => coh.Status == CommissionPaymentStatus.paid);
        if (dateFrom.HasValue)
        {
            orderHistoryQuery = orderHistoryQuery.Where(coh => coh.PaymentDate >= dateFrom.Value);
        }
        if (dateTo.HasValue)
        {
            orderHistoryQuery = orderHistoryQuery.Where(coh => coh.PaymentDate <= dateTo.Value);
        }

        var orderHistoryAmount = await orderHistoryQuery.SumAsync(coh => coh.PaidAmount, cancellationToken);

        return paymentAmount + orderHistoryAmount;
    }

    /// <summary>
    /// Get commission recognized for an agent in the specified period
    /// </summary>
    private async Task<decimal> GetAgentCommissionRecognized(Guid agentId, DateTime? dateFrom, DateTime? dateTo, CancellationToken cancellationToken)
    {
        var reportQuery = context.CommissionReports
            .Where(cr => cr.AgentId == agentId)
            .Where(cr => cr.Status != CommissionReportStatus.rejected);

        // Apply date filters for commission reports
        if (dateFrom.HasValue)
        {
            var dateFromOnly = DateOnly.FromDateTime(dateFrom.Value);
            reportQuery = reportQuery.Where(cr => cr.EndDate >= dateFromOnly);
        }
        if (dateTo.HasValue)
        {
            var dateToOnly = DateOnly.FromDateTime(dateTo.Value);
            reportQuery = reportQuery.Where(cr => cr.StartDate <= dateToOnly);
        }

        return await reportQuery.SumAsync(cr => cr.CalculatedCommissionAmount, cancellationToken);
    }
    /// <summary>
    /// Apply sorting to the commission details list
    /// </summary>
    private static IEnumerable<AgentCommissionDetailResponseDto> ApplySorting(
        List<AgentCommissionDetailResponseDto> details, 
        string? sortBy, 
        bool? isDescending)
    {
        var isDesc = isDescending ?? false;

        return sortBy?.ToLower() switch
        {
            "name" => isDesc ? details.OrderByDescending(d => d.Name) : details.OrderBy(d => d.Name),
            "code" => isDesc ? details.OrderByDescending(d => d.Code) : details.OrderBy(d => d.Code),
            "revenue" => isDesc ? details.OrderByDescending(d => d.Revenue) : details.OrderBy(d => d.Revenue),
            "commissionpercentage" => isDesc ? details.OrderByDescending(d => d.CommissionPercentage) : details.OrderBy(d => d.CommissionPercentage),
            "ordercount" => isDesc ? details.OrderByDescending(d => d.OrderCount) : details.OrderBy(d => d.OrderCount),
            "commissionrecognized" => isDesc ? details.OrderByDescending(d => d.CommissionRecognized) : details.OrderBy(d => d.CommissionRecognized),
            "commissionpaid" => isDesc ? details.OrderByDescending(d => d.CommissionPaid) : details.OrderBy(d => d.CommissionPaid),
            _ => isDesc ? details.OrderByDescending(d => d.Name) : details.OrderBy(d => d.Name) // Default sort by name
        };
    }
}