namespace Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetCommissionOverview;

/// <summary>
/// Response DTO for commission overview with comparison to previous period
/// </summary>
public record CommissionOverviewResponseDto
{
    /// <summary>
    /// Tổng chi phí đối tác (tổng cộng tất cả chi phí hoa hồng và chiết khấu)
    /// </summary>
    public decimal TotalPartnerCost { get; set; }
    
    /// <summary>
    /// Chiết khấu Đại lý
    /// </summary>
    public decimal AgentDiscount { get; set; }
    
    /// <summary>
    /// Hoa hồng CTV đã chi
    /// </summary>
    public decimal CollaboratorCommissionPaid { get; set; }
    
    /// <summary>
    /// Hoa hồng CTV ghi nhận
    /// </summary>
    public decimal CollaboratorCommissionRecognized { get; set; }
    
    /// <summary>
    /// Hoa hồng Đại lý đã chi
    /// </summary>
    public decimal AgentCommissionPaid { get; set; }
    
    /// <summary>
    /// Hoa hồng Đại lý ghi nhận
    /// </summary>
    public decimal AgentCommissionRecognized { get; set; }
    
    // Percentage changes compared to previous period
    /// <summary>
    /// Phần trăm thay đổi tổng chi phí đối tác so với cùng kỳ trước đó
    /// </summary>
    public decimal TotalPartnerCostChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi chiết khấu Đại lý so với cùng kỳ trước đó
    /// </summary>
    public decimal AgentDiscountChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi hoa hồng CTV đã chi so với cùng kỳ trước đó
    /// </summary>
    public decimal CollaboratorCommissionPaidChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi hoa hồng CTV ghi nhận so với cùng kỳ trước đó
    /// </summary>
    public decimal CollaboratorCommissionRecognizedChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi hoa hồng Đại lý đã chi so với cùng kỳ trước đó
    /// </summary>
    public decimal AgentCommissionPaidChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi hoa hồng Đại lý ghi nhận so với cùng kỳ trước đó
    /// </summary>
    public decimal AgentCommissionRecognizedChangePercent { get; set; }
    
    // Previous period values for reference
    /// <summary>
    /// Previous period total partner cost for reference
    /// </summary>
    public decimal PreviousPeriodTotalPartnerCost { get; set; }
    
    /// <summary>
    /// Previous period agent discount for reference
    /// </summary>
    public decimal PreviousPeriodAgentDiscount { get; set; }
    
    /// <summary>
    /// Previous period collaborator commission paid for reference
    /// </summary>
    public decimal PreviousPeriodCollaboratorCommissionPaid { get; set; }
    
    /// <summary>
    /// Previous period collaborator commission recognized for reference
    /// </summary>
    public decimal PreviousPeriodCollaboratorCommissionRecognized { get; set; }
    
    /// <summary>
    /// Previous period agent commission paid for reference
    /// </summary>
    public decimal PreviousPeriodAgentCommissionPaid { get; set; }
    
    /// <summary>
    /// Previous period agent commission recognized for reference
    /// </summary>
    public decimal PreviousPeriodAgentCommissionRecognized { get; set; }
}