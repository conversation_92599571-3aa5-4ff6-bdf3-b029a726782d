namespace Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetCommissionOverview;

/// <summary>
/// Handler for GetCommissionOverviewQuery
/// Calculates commission overview and compares with previous period
/// </summary>
public class GetCommissionOverviewQueryHandler(IApplicationDbContext context)
    : IRequestHandler<GetCommissionOverviewQuery, Response<CommissionOverviewResponseDto>>
{
    public async Task<Response<CommissionOverviewResponseDto>> Handle(GetCommissionOverviewQuery request, CancellationToken cancellationToken)
    {
        // Calculate current period commission statistics
        var currentPeriodStats = await GetPeriodCommissionStats(request.DateFrom, request.DateTo, cancellationToken);

        // Calculate previous period dates (same duration before the current period)
        var periodDuration = request.DateTo - request.DateFrom;
        var previousDateTo = request.DateFrom.AddTicks(-1); // End just before current period starts
        var previousDateFrom = previousDateTo.Subtract(periodDuration);

        // Calculate previous period commission statistics
        var previousPeriodStats = await GetPeriodCommissionStats(previousDateFrom, previousDateTo, cancellationToken);

        // Calculate percentage changes for all fields
        var totalPartnerCostChangePercent = CalculatePercentageChange(previousPeriodStats.TotalPartnerCost, currentPeriodStats.TotalPartnerCost);
        var agentDiscountChangePercent = CalculatePercentageChange(previousPeriodStats.AgentDiscount, currentPeriodStats.AgentDiscount);
        var collaboratorCommissionPaidChangePercent = CalculatePercentageChange(previousPeriodStats.CollaboratorCommissionPaid, currentPeriodStats.CollaboratorCommissionPaid);
        var collaboratorCommissionRecognizedChangePercent = CalculatePercentageChange(previousPeriodStats.CollaboratorCommissionRecognized, currentPeriodStats.CollaboratorCommissionRecognized);
        var agentCommissionPaidChangePercent = CalculatePercentageChange(previousPeriodStats.AgentCommissionPaid, currentPeriodStats.AgentCommissionPaid);
        var agentCommissionRecognizedChangePercent = CalculatePercentageChange(previousPeriodStats.AgentCommissionRecognized, currentPeriodStats.AgentCommissionRecognized);

        var result = new CommissionOverviewResponseDto
        {
            // Current period values
            TotalPartnerCost = currentPeriodStats.TotalPartnerCost,
            AgentDiscount = currentPeriodStats.AgentDiscount,
            CollaboratorCommissionPaid = currentPeriodStats.CollaboratorCommissionPaid,
            CollaboratorCommissionRecognized = currentPeriodStats.CollaboratorCommissionRecognized,
            AgentCommissionPaid = currentPeriodStats.AgentCommissionPaid,
            AgentCommissionRecognized = currentPeriodStats.AgentCommissionRecognized,

            // Percentage changes
            TotalPartnerCostChangePercent = totalPartnerCostChangePercent,
            AgentDiscountChangePercent = agentDiscountChangePercent,
            CollaboratorCommissionPaidChangePercent = collaboratorCommissionPaidChangePercent,
            CollaboratorCommissionRecognizedChangePercent = collaboratorCommissionRecognizedChangePercent,
            AgentCommissionPaidChangePercent = agentCommissionPaidChangePercent,
            AgentCommissionRecognizedChangePercent = agentCommissionRecognizedChangePercent,

            // Previous period values for reference
            PreviousPeriodTotalPartnerCost = previousPeriodStats.TotalPartnerCost,
            PreviousPeriodAgentDiscount = previousPeriodStats.AgentDiscount,
            PreviousPeriodCollaboratorCommissionPaid = previousPeriodStats.CollaboratorCommissionPaid,
            PreviousPeriodCollaboratorCommissionRecognized = previousPeriodStats.CollaboratorCommissionRecognized,
            PreviousPeriodAgentCommissionPaid = previousPeriodStats.AgentCommissionPaid,
            PreviousPeriodAgentCommissionRecognized = previousPeriodStats.AgentCommissionRecognized,
        };

        return new Response<CommissionOverviewResponseDto>(result);
    }

    /// <summary>
    /// Get commission statistics for a specific period
    /// </summary>
    private async Task<CommissionPeriodStats> GetPeriodCommissionStats(
        DateTime dateFrom,
        DateTime dateTo,
        CancellationToken cancellationToken)
    {
        // Get commission payments (đã chi) - from CommissionPayment table
        var commissionPayments = await context.CommissionPayments
            .Include(cp => cp.Agent)
            .Where(cp => cp.PaymentDate >= dateFrom && cp.PaymentDate <= dateTo)
            .Where(cp => cp.Status == CommissionPaymentStatus.paid)
            .ToListAsync(cancellationToken);

        // Get commission order history (đã chi từ lịch sử orders)
        var commissionOrderHistory = await context.CommissionOrderHistories
            .Include(coh => coh.Agent)
            .Where(coh => coh.PaymentDate.HasValue && coh.PaymentDate >= dateFrom && coh.PaymentDate <= dateTo)
            .Where(coh => coh.Status == CommissionPaymentStatus.paid)
            .ToListAsync(cancellationToken);

        // Get commission reports (ghi nhận) - from CommissionReport table
        var commissionReports = await context.CommissionReports
            .Include(cr => cr.Agent)
            .Where(cr => cr.StartDate <= DateOnly.FromDateTime(dateTo) && cr.EndDate >= DateOnly.FromDateTime(dateFrom))
            .Where(cr => cr.Status != CommissionReportStatus.rejected)
            .ToListAsync(cancellationToken);

        // Calculate collaborator commissions (CTV)
        var collaboratorCommissionPaid = commissionPayments
                .Where(cp => cp.Agent.Type == AgentType.collaborator)
                .Sum(cp => cp.Amount) +
            commissionOrderHistory
                .Where(coh => coh.Agent.Type == AgentType.collaborator)
                .Sum(coh => coh.PaidAmount);

        var collaboratorCommissionRecognized = commissionReports
            .Where(cr => cr.Agent.Type == AgentType.collaborator)
            .Sum(cr => cr.CalculatedCommissionAmount);
        // Calculate agent commissions (Đại lý)
        var agentCommissionPaid = commissionPayments
                .Where(cp => cp.Agent.Type == AgentType.agent)
                .Sum(cp => cp.Amount) +
            commissionOrderHistory
                .Where(coh => coh.Agent.Type == AgentType.agent)
                .Sum(coh => coh.PaidAmount);

        var agentCommissionRecognized = commissionReports
            .Where(cr => cr.Agent.Type == AgentType.agent)
            .Sum(cr => cr.CalculatedCommissionAmount);

        // Calculate agent discount (chiết khấu đại lý) 
        // This requires checking orders where agent has IsDirectDiscount = true
        var agentDiscount = await context.Orders
            .Include(o => o.Agent)
            .Where(o => o.CreatedAt >= dateFrom && o.CreatedAt <= dateTo)
            .Where(o => o.Agent != null && o.Agent.IsDirectDiscount && o.Agent.Type == AgentType.agent)
            .SumAsync(o => o.DiscountAmount, cancellationToken);

        // Calculate total partner cost (tổng chi phí đối tác)
        var totalPartnerCost = collaboratorCommissionPaid + agentCommissionPaid + agentDiscount;

        return new CommissionPeriodStats
        {
            TotalPartnerCost = totalPartnerCost,
            AgentDiscount = agentDiscount,
            CollaboratorCommissionPaid = collaboratorCommissionPaid,
            CollaboratorCommissionRecognized = collaboratorCommissionRecognized,
            AgentCommissionPaid = agentCommissionPaid,
            AgentCommissionRecognized = agentCommissionRecognized
        };
    }
    /// <summary>
    /// Calculate percentage change between two values
    /// </summary>
    private static decimal CalculatePercentageChange(decimal previousValue, decimal currentValue)
    {
        if (previousValue == 0)
        {
            return currentValue > 0 ? 100 : 0;
        }

        var change = ((currentValue - previousValue) / previousValue) * 100;
        return Math.Round(change, 2);
    }
}

/// <summary>
/// Internal class to hold commission statistics for a period
/// </summary>
internal class CommissionPeriodStats
{
    public decimal TotalPartnerCost { get; set; }
    public decimal AgentDiscount { get; set; }
    public decimal CollaboratorCommissionPaid { get; set; }
    public decimal CollaboratorCommissionRecognized { get; set; }
    public decimal AgentCommissionPaid { get; set; }
    public decimal AgentCommissionRecognized { get; set; }
}