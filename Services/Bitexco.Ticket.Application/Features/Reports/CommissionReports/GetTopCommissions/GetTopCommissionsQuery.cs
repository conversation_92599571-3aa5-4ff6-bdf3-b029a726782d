namespace Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetTopCommissions;

/// <summary>
/// Query to get top 3 commission details by agent type (CTV or Agency)
/// </summary>
public class GetTopCommissionsQuery : IRequest<Response<List<TopCommissionResponseDto>>>
{
    /// <summary>
    /// Start date for commission calculation
    /// </summary>
    public DateTime DateFrom { get; set; }
    
    /// <summary>
    /// End date for commission calculation
    /// </summary>
    public DateTime DateTo { get; set; }
    
    /// <summary>
    /// Agent type filter: "ctv" for collaborator or "agency" for agent
    /// </summary>
    public string Type { get; set; } = null!;
}