namespace Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetTopCommissions;

/// <summary>
/// Handler for GetTopCommissionsQuery
/// Returns top 3 commission details by agent type (CTV or Agency)
/// </summary>
public class GetTopCommissionsQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetTopCommissionsQuery, Response<List<TopCommissionResponseDto>>>
{
    public async Task<Response<List<TopCommissionResponseDto>>> Handle(GetTopCommissionsQuery request, CancellationToken cancellationToken)
    {
        // Validate and convert type parameter
        var agentType = GetAgentTypeFromString(request.Type);
        
        // Get agents of the specified type
        var agents = await context.Agents
            .Where(a => a.Type == agentType && a.IsActive)
            .ToListAsync(cancellationToken);
            
        if (!agents.Any())
        {
            return new Response<List<TopCommissionResponseDto>>(new List<TopCommissionResponseDto>());
        }
        
        var agentIds = agents.Select(a => a.Id).ToList();
        
        // Get commission data for these agents in the specified period
        var topCommissions = new List<TopCommissionResponseDto>();
        
        foreach (var agent in agents)
        {
            var commissionDetail = await CalculateAgentCommissionDetail(agent, request.DateFrom, request.DateTo, cancellationToken);
            if (commissionDetail != null)
            {
                topCommissions.Add(commissionDetail);
            }
        }        
        // Sort by commission recognized (descending) and take top 3
        var result = topCommissions
            .OrderByDescending(tc => tc.CommissionRecognized)
            .Take(3)
            .ToList();
            
        return new Response<List<TopCommissionResponseDto>>(result);
    }
    
    /// <summary>
    /// Calculate commission details for a specific agent
    /// </summary>
    private async Task<TopCommissionResponseDto?> CalculateAgentCommissionDetail(
        Agent agent, 
        DateTime dateFrom, 
        DateTime dateTo, 
        CancellationToken cancellationToken)
    {
        // Get orders for this agent in the period (for revenue calculation)
        var orders = await context.Orders
            .Where(o => o.AgentId == agent.Id)
            .Where(o => o.CreatedAt >= dateFrom && o.CreatedAt <= dateTo)
            .Where(o => o.Status != OrderStatus.cancelled && o.Status != OrderStatus.refunded)
            .ToListAsync(cancellationToken);
            
        if (!orders.Any())
        {
            return null; // No orders, no commission
        }
        
        // Calculate revenue (total amount from orders)
        var revenue = orders.Sum(o => o.TotalAmount);
        
        // Get commission paid (đã chi) from CommissionPayment and CommissionOrderHistory
        var commissionPaid = await GetAgentCommissionPaid(agent.Id, dateFrom, dateTo, cancellationToken);        
        // Get commission recognized (ghi nhận) from CommissionReport
        var commissionRecognized = await GetAgentCommissionRecognized(agent.Id, dateFrom, dateTo, cancellationToken);
        
        // Calculate commission percentage (based on agent's commission rate)
        var commissionPercentage = agent.CommissionRatePercentage ?? 0;
        
        return new TopCommissionResponseDto
        {
            Name = agent.Name,
            Code = agent.Code,
            Revenue = revenue,
            CommissionPercentage = commissionPercentage,
            CommissionRecognized = commissionRecognized,
            CommissionPaid = commissionPaid
        };
    }
    
    /// <summary>
    /// Get commission paid for an agent in the specified period
    /// </summary>
    private async Task<decimal> GetAgentCommissionPaid(Guid agentId, DateTime dateFrom, DateTime dateTo, CancellationToken cancellationToken)
    {
        // From CommissionPayment
        var paymentAmount = await context.CommissionPayments
            .Where(cp => cp.AgentId == agentId)
            .Where(cp => cp.PaymentDate >= dateFrom && cp.PaymentDate <= dateTo)
            .Where(cp => cp.Status == CommissionPaymentStatus.paid)
            .SumAsync(cp => cp.Amount, cancellationToken);
            
        // From CommissionOrderHistory
        var orderHistoryAmount = await context.CommissionOrderHistories
            .Where(coh => coh.AgentId == agentId)
            .Where(coh => coh.PaymentDate.HasValue && coh.PaymentDate >= dateFrom && coh.PaymentDate <= dateTo)
            .Where(coh => coh.Status == CommissionPaymentStatus.paid)
            .SumAsync(coh => coh.PaidAmount, cancellationToken);
            
        return paymentAmount + orderHistoryAmount;
    }    
    /// <summary>
    /// Get commission recognized for an agent in the specified period
    /// </summary>
    private async Task<decimal> GetAgentCommissionRecognized(Guid agentId, DateTime dateFrom, DateTime dateTo, CancellationToken cancellationToken)
    {
        return await context.CommissionReports
            .Where(cr => cr.AgentId == agentId)
            .Where(cr => cr.StartDate <= DateOnly.FromDateTime(dateTo) && cr.EndDate >= DateOnly.FromDateTime(dateFrom))
            .Where(cr => cr.Status != CommissionReportStatus.rejected)
            .SumAsync(cr => cr.CalculatedCommissionAmount, cancellationToken);
    }
    
    /// <summary>
    /// Convert string type to AgentType enum
    /// </summary>
    private static AgentType GetAgentTypeFromString(string type)
    {
        return type.ToLower() switch
        {
            "ctv" => AgentType.collaborator,
            "agency" => AgentType.agent,
            _ => throw new ArgumentException($"Invalid agent type: {type}. Must be 'ctv' or 'agency'")
        };
    }
}