namespace Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetCollaboratorCommissionDetails;

/// <summary>
/// Handler for GetCollaboratorCommissionDetailsQuery
/// Returns paginated list of collaborator commission details
/// </summary>
public class GetCollaboratorCommissionDetailsQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetCollaboratorCommissionDetailsQuery, PaginationResponse<CollaboratorCommissionDetailResponseDto>>
{
    public async Task<PaginationResponse<CollaboratorCommissionDetailResponseDto>> Handle(GetCollaboratorCommissionDetailsQuery request, CancellationToken cancellationToken)
    {
        // Start with base query - filter for collaborators only
        var agentsQuery = context.Agents
            .Where(a => a.Type == AgentType.collaborator && a.IsActive)
            .AsQueryable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            var searchTerm = request.Search.Trim().ToLower();
            agentsQuery = agentsQuery.Where(a => 
                a.Name.ToLower().Contains(searchTerm) || 
                a.Code.ToLower().Contains(searchTerm));
        }

        // Get all collaborators that match the criteria
        var agents = await agentsQuery
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        if (!agents.Any())
        {
            return new PaginationResponse<CollaboratorCommissionDetailResponseDto>(
                request.PageIndex, request.PageSize, 0, new List<CollaboratorCommissionDetailResponseDto>());
        }
        // Calculate commission details for each collaborator
        var commissionDetails = new List<CollaboratorCommissionDetailResponseDto>();

        foreach (var agent in agents)
        {
            var detail = await CalculateCollaboratorCommissionDetail(agent, request.DateFrom, request.DateTo, cancellationToken);
            if (detail != null) // Only add collaborators that have data in the period
            {
                commissionDetails.Add(detail);
            }
        }

        // Get total count before sorting and pagination
        var totalCount = commissionDetails.Count;

        // Apply sorting
        var sortedDetails = ApplySorting(commissionDetails, request.SortBy, request.IsDescending);

        // Apply pagination
        var paginatedDetails = sortedDetails
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return new PaginationResponse<CollaboratorCommissionDetailResponseDto>(
            request.PageIndex, request.PageSize, totalCount, paginatedDetails);
    }

    /// <summary>
    /// Calculate commission details for a specific collaborator in the given period
    /// </summary>
    private async Task<CollaboratorCommissionDetailResponseDto?> CalculateCollaboratorCommissionDetail(
        Agent agent, 
        DateTime? dateFrom, 
        DateTime? dateTo, 
        CancellationToken cancellationToken)
    {        // Build orders query with date filter
        var ordersQuery = context.Orders
            .Where(o => o.AgentId == agent.Id)
            .Where(o => o.Status != OrderStatus.cancelled && o.Status != OrderStatus.refunded);

        // Apply date filters if provided
        if (dateFrom.HasValue)
        {
            ordersQuery = ordersQuery.Where(o => o.CreatedAt >= dateFrom.Value);
        }
        if (dateTo.HasValue)
        {
            ordersQuery = ordersQuery.Where(o => o.CreatedAt <= dateTo.Value);
        }

        // Get orders for this collaborator in the period
        var orders = await ordersQuery
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        // If no orders in period, exclude this collaborator
        if (!orders.Any())
        {
            return null;
        }

        // Calculate revenue and order count
        var revenue = orders.Sum(o => o.TotalAmount);
        var orderCount = orders.Count;

        // Get commission paid (đã chi)
        var commissionPaid = await GetCollaboratorCommissionPaid(agent.Id, dateFrom, dateTo, cancellationToken);

        // Get commission recognized (ghi nhận)
        var commissionRecognized = await GetCollaboratorCommissionRecognized(agent.Id, dateFrom, dateTo, cancellationToken);

        // Commission percentage from agent's settings
        var commissionPercentage = agent.CommissionRatePercentage ?? 0;
        return new CollaboratorCommissionDetailResponseDto
        {
            Name = agent.Name,
            Code = agent.Code,
            Revenue = revenue,
            CommissionPercentage = commissionPercentage,
            OrderCount = orderCount,
            CommissionRecognized = commissionRecognized,
            CommissionPaid = commissionPaid
        };
    }

    /// <summary>
    /// Get commission paid for a collaborator in the specified period
    /// </summary>
    private async Task<decimal> GetCollaboratorCommissionPaid(Guid agentId, DateTime? dateFrom, DateTime? dateTo, CancellationToken cancellationToken)
    {
        // From CommissionPayment
        var paymentQuery = context.CommissionPayments
            .Where(cp => cp.AgentId == agentId)
            .Where(cp => cp.Status == CommissionPaymentStatus.paid);

        if (dateFrom.HasValue)
        {
            paymentQuery = paymentQuery.Where(cp => cp.PaymentDate >= dateFrom.Value);
        }
        if (dateTo.HasValue)
        {
            paymentQuery = paymentQuery.Where(cp => cp.PaymentDate <= dateTo.Value);
        }

        var paymentAmount = await paymentQuery.SumAsync(cp => cp.Amount, cancellationToken);

        // From CommissionOrderHistory
        var orderHistoryQuery = context.CommissionOrderHistories
            .Where(coh => coh.AgentId == agentId)
            .Where(coh => coh.PaymentDate.HasValue)
            .Where(coh => coh.Status == CommissionPaymentStatus.paid);
        if (dateFrom.HasValue)
        {
            orderHistoryQuery = orderHistoryQuery.Where(coh => coh.PaymentDate >= dateFrom.Value);
        }
        if (dateTo.HasValue)
        {
            orderHistoryQuery = orderHistoryQuery.Where(coh => coh.PaymentDate <= dateTo.Value);
        }

        var orderHistoryAmount = await orderHistoryQuery.SumAsync(coh => coh.PaidAmount, cancellationToken);

        return paymentAmount + orderHistoryAmount;
    }

    /// <summary>
    /// Get commission recognized for a collaborator in the specified period
    /// </summary>
    private async Task<decimal> GetCollaboratorCommissionRecognized(Guid agentId, DateTime? dateFrom, DateTime? dateTo, CancellationToken cancellationToken)
    {
        var reportQuery = context.CommissionReports
            .Where(cr => cr.AgentId == agentId)
            .Where(cr => cr.Status != CommissionReportStatus.rejected);

        // Apply date filters for commission reports
        if (dateFrom.HasValue)
        {
            var dateFromOnly = DateOnly.FromDateTime(dateFrom.Value);
            reportQuery = reportQuery.Where(cr => cr.EndDate >= dateFromOnly);
        }
        if (dateTo.HasValue)
        {
            var dateToOnly = DateOnly.FromDateTime(dateTo.Value);
            reportQuery = reportQuery.Where(cr => cr.StartDate <= dateToOnly);
        }

        return await reportQuery.SumAsync(cr => cr.CalculatedCommissionAmount, cancellationToken);
    }
    /// <summary>
    /// Apply sorting to the commission details list
    /// </summary>
    private static IEnumerable<CollaboratorCommissionDetailResponseDto> ApplySorting(
        List<CollaboratorCommissionDetailResponseDto> details, 
        string? sortBy, 
        bool? isDescending)
    {
        var isDesc = isDescending ?? false;

        return (sortBy?.ToLower()) switch
        {
            "name" => isDesc ? details.OrderByDescending(d => d.Name) : details.OrderBy(d => d.Name),
            "code" => isDesc ? details.OrderByDescending(d => d.Code) : details.OrderBy(d => d.Code),
            "revenue" => isDesc ? details.OrderByDescending(d => d.Revenue) : details.OrderBy(d => d.Revenue),
            "commissionpercentage" => isDesc ? details.OrderByDescending(d => d.CommissionPercentage) : details.OrderBy(d => d.CommissionPercentage),
            "ordercount" => isDesc ? details.OrderByDescending(d => d.OrderCount) : details.OrderBy(d => d.OrderCount),
            "commissionrecognized" => isDesc ? details.OrderByDescending(d => d.CommissionRecognized) : details.OrderBy(d => d.CommissionRecognized),
            "commissionpaid" => isDesc ? details.OrderByDescending(d => d.CommissionPaid) : details.OrderBy(d => d.CommissionPaid),
            _ => isDesc ? details.OrderByDescending(d => d.Name) : details.OrderBy(d => d.Name), // Default sort by name
        };
    }
}