namespace Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetCollaboratorCommissionDetails;

/// <summary>
/// Query to get paginated list of collaborator commission details with optional filtering
/// </summary>
public class GetCollaboratorCommissionDetailsQuery : IRequest<PaginationResponse<CollaboratorCommissionDetailResponseDto>>, IPaginationRequest, ISortRequest
{
    /// <summary>
    /// Filter by creation date range - start date (optional)
    /// </summary>
    public DateTime? DateFrom { get; set; }

    /// <summary>
    /// Filter by creation date range - end date (optional)
    /// </summary>
    public DateTime? DateTo { get; set; }

    /// <summary>
    /// Search by collaborator name or code (optional)
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// The page index for pagination
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// The page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 100;

    /// <summary>
    /// The sort field for the results
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// The sort direction (true for descending, false for ascending)
    /// </summary>
    public bool? IsDescending { get; set; }
}