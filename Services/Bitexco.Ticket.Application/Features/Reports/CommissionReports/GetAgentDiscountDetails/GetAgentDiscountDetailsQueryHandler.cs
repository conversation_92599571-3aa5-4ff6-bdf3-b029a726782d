namespace Bitexco.Ticket.Application.Features.Reports.CommissionReports.GetAgentDiscountDetails;

/// <summary>
/// Handler for GetAgentDiscountDetailsQuery
/// Returns paginated list of agent discount details
/// </summary>
public class GetAgentDiscountDetailsQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetAgentDiscountDetailsQuery, PaginationResponse<AgentDiscountDetailResponseDto>>
{
    public async Task<PaginationResponse<AgentDiscountDetailResponseDto>> Handle(GetAgentDiscountDetailsQuery request, CancellationToken cancellationToken)
    {
        // Start with base query - filter for agents with direct discount enabled
        var agentsQuery = context.Agents
            .Where(a => a.Type == AgentType.agent && a.IsActive && a.IsDirectDiscount)
            .AsQueryable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            var searchTerm = request.Search.Trim().ToLower();
            agentsQuery = agentsQuery.Where(a => 
                a.Name.ToLower().Contains(searchTerm) || 
                a.Code.ToLower().Contains(searchTerm));
        }

        // Get all agents that match the criteria
        var agents = await agentsQuery
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        if (!agents.Any())
        {
            return new PaginationResponse<AgentDiscountDetailResponseDto>(
                request.PageIndex, request.PageSize, 0, new List<AgentDiscountDetailResponseDto>());
        }
        // Calculate discount details for each agent
        var discountDetails = new List<AgentDiscountDetailResponseDto>();

        foreach (var agent in agents)
        {
            var detail = await CalculateAgentDiscountDetail(agent, request.DateFrom, request.DateTo, cancellationToken);
            if (detail != null) // Only add agents that have data in the period
            {
                discountDetails.Add(detail);
            }
        }

        // Get total count before sorting and pagination
        var totalCount = discountDetails.Count;

        // Apply sorting
        var sortedDetails = ApplySorting(discountDetails, request.SortBy, request.IsDescending);

        // Apply pagination
        var paginatedDetails = sortedDetails
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return new PaginationResponse<AgentDiscountDetailResponseDto>(
            request.PageIndex, request.PageSize, totalCount, paginatedDetails);
    }

    /// <summary>
    /// Calculate discount details for a specific agent in the given period
    /// </summary>
    private async Task<AgentDiscountDetailResponseDto?> CalculateAgentDiscountDetail(
        Agent agent, 
        DateTime? dateFrom, 
        DateTime? dateTo, 
        CancellationToken cancellationToken)
    {        // Build orders query with date filter - focus on orders with discount
        var ordersQuery = context.Orders
            .Where(o => o.AgentId == agent.Id)
            .Where(o => o.Status != OrderStatus.cancelled && o.Status != OrderStatus.refunded)
            .Where(o => o.DiscountAmount > 0); // Only orders with discount

        // Apply date filters if provided
        if (dateFrom.HasValue)
        {
            ordersQuery = ordersQuery.Where(o => o.CreatedAt >= dateFrom.Value);
        }
        if (dateTo.HasValue)
        {
            ordersQuery = ordersQuery.Where(o => o.CreatedAt <= dateTo.Value);
        }

        // Get orders for this agent in the period with discount
        var orders = await ordersQuery
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        // If no orders with discount in period, exclude this agent
        if (!orders.Any())
        {
            return null;
        }

        // Calculate revenue, order count and total discount
        var revenue = orders.Sum(o => o.TotalAmount);
        var orderCount = orders.Count;
        var totalDiscount = orders.Sum(o => o.DiscountAmount);

        return new AgentDiscountDetailResponseDto
        {
            Name = agent.Name,
            Code = agent.Code,
            OrderCount = orderCount,
            Revenue = revenue,
            Discount = totalDiscount
        };
    }
    /// <summary>
    /// Apply sorting to the discount details list
    /// </summary>
    private static IEnumerable<AgentDiscountDetailResponseDto> ApplySorting(
        List<AgentDiscountDetailResponseDto> details, 
        string? sortBy, 
        bool? isDescending)
    {
        var isDesc = isDescending ?? false;

        return (sortBy?.ToLower()) switch
        {
            "name" => isDesc ? details.OrderByDescending(d => d.Name) : details.OrderBy(d => d.Name),
            "code" => isDesc ? details.OrderByDescending(d => d.Code) : details.OrderBy(d => d.Code),
            "ordercount" => isDesc ? details.OrderByDescending(d => d.OrderCount) : details.OrderBy(d => d.OrderCount),
            "revenue" => isDesc ? details.OrderByDescending(d => d.Revenue) : details.OrderBy(d => d.Revenue),
            "discount" => isDesc ? details.OrderByDescending(d => d.Discount) : details.OrderBy(d => d.Discount),
            _ => isDesc ? details.OrderByDescending(d => d.Name) : details.OrderBy(d => d.Name), // Default sort by name
        };
    }
}