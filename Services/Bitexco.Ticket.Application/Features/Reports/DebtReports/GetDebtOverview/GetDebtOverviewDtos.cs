namespace Bitexco.Ticket.Application.Features.Reports.DebtReports.GetDebtOverview;

/// <summary>
/// Response DTO for debt overview with comparison to previous period
/// </summary>
public record DebtOverviewResponseDto
{
    /// <summary>
    /// Tổng số tiền mà các đại lý cần trả trong khoảng thời gian
    /// </summary>
    public decimal TotalAmountDue { get; set; }
    
    /// <summary>
    /// Tổng số tiền mà các đại lý đã thanh toán trong khoảng thời gian
    /// </summary>
    public decimal TotalAmountPaid { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi của số tiền cần trả so với cùng kỳ trước đó
    /// </summary>
    public decimal AmountDueChangePercent { get; set; }
    
    /// <summary>
    /// Phần trăm thay đổi của số tiền đã trả so với cùng kỳ trước đó
    /// </summary>
    public decimal AmountPaidChangePercent { get; set; }
    
    /// <summary>
    /// Previous period total amount due for reference
    /// </summary>
    public decimal PreviousPeriodAmountDue { get; set; }
    
    /// <summary>
    /// Previous period total amount paid for reference
    /// </summary>
    public decimal PreviousPeriodAmountPaid { get; set; }
}
