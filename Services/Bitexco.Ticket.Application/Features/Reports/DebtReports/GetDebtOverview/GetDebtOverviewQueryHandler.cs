namespace Bitexco.Ticket.Application.Features.Reports.DebtReports.GetDebtOverview;

/// <summary>
/// Handler for GetDebtOverviewQuery
/// Calculates debt overview and compares with previous period
/// </summary>
public class GetDebtOverviewQueryHandler(IApplicationDbContext context) 
    : IRequestHandler<GetDebtOverviewQuery, Response<DebtOverviewResponseDto>>
{
    public async Task<Response<DebtOverviewResponseDto>> Handle(GetDebtOverviewQuery request, CancellationToken cancellationToken)
    {
        // Calculate current period debt statistics
        var currentPeriodStats = await GetPeriodDebtStats(request.DateFrom, request.DateTo, cancellationToken);
        
        // Calculate previous period dates (same duration before the current period)
        var periodDuration = request.DateTo - request.DateFrom;
        var previousDateTo = request.DateFrom.AddTicks(-1); // End just before current period starts
        var previousDateFrom = previousDateTo.Subtract(periodDuration);
        
        // Calculate previous period debt statistics
        var previousPeriodStats = await GetPeriodDebtStats(previousDateFrom, previousDateTo, cancellationToken);
        
        // Calculate percentage changes
        var amountDueChangePercent = CalculatePercentageChange(previousPeriodStats.TotalAmountDue, currentPeriodStats.TotalAmountDue);
        var amountPaidChangePercent = CalculatePercentageChange(previousPeriodStats.TotalAmountPaid, currentPeriodStats.TotalAmountPaid);
        
        var result = new DebtOverviewResponseDto
        {
            TotalAmountDue = currentPeriodStats.TotalAmountDue,
            TotalAmountPaid = currentPeriodStats.TotalAmountPaid,
            AmountDueChangePercent = amountDueChangePercent,
            AmountPaidChangePercent = amountPaidChangePercent,
            PreviousPeriodAmountDue = previousPeriodStats.TotalAmountDue,
            PreviousPeriodAmountPaid = previousPeriodStats.TotalAmountPaid,
        };
        
        return new Response<DebtOverviewResponseDto>(result);
    }
    
    /// <summary>
    /// Get debt statistics for a specific period
    /// </summary>
    private async Task<(decimal TotalAmountDue, decimal TotalAmountPaid)> GetPeriodDebtStats(
        DateTime dateFrom, 
        DateTime dateTo, 
        CancellationToken cancellationToken)
    {
        var debtStats = await context.AgentDebts
            .Where(d => d.CreatedAt >= dateFrom && d.CreatedAt <= dateTo)
            .GroupBy(d => 1) // Group all records
            .Select(g => new
            {
                TotalOriginalAmount = g.Sum(d => d.OriginalAmount),
                TotalPaidAmount = g.Sum(d => d.PaidAmount)
            })
            .FirstOrDefaultAsync(cancellationToken);
            
        if (debtStats == null)
        {
            return (0, 0);
        }
        
        var totalAmountDue = debtStats.TotalOriginalAmount - debtStats.TotalPaidAmount;
        var totalAmountPaid = debtStats.TotalPaidAmount;
        
        return (totalAmountDue, totalAmountPaid);
    }
    
    /// <summary>
    /// Calculate percentage change between two values
    /// </summary>
    private static decimal CalculatePercentageChange(decimal previousValue, decimal currentValue)
    {
        if (previousValue == 0)
        {
            return currentValue > 0 ? 100 : 0;
        }
        
        var change = ((currentValue - previousValue) / previousValue) * 100;
        return Math.Round(change, 2);
    }
}
