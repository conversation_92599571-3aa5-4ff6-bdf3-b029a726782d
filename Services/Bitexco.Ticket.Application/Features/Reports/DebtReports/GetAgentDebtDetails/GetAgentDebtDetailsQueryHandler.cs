namespace Bitexco.Ticket.Application.Features.Reports.DebtReports.GetAgentDebtDetails;

/// <summary>
/// Handler for GetAgentDebtDetailsQuery
/// Gets paginated agent debt details with statistics aggregation
/// </summary>
public class GetAgentDebtDetailsQueryHandler(IApplicationDbContext context)
    : IRequestHandler<GetAgentDebtDetailsQuery, PaginationResponse<AgentDebtDetailResponseDto>>
{
    public async Task<PaginationResponse<AgentDebtDetailResponseDto>> Handle(
        GetAgentDebtDetailsQuery request, 
        CancellationToken cancellationToken)
    {
        // Build base query for agent debts in date range
        var baseQuery = context.AgentDebts
            .Include(d => d.Agent)
            .Include(d => d.Order)
            .Where(d => d.CreatedAt >= request.DateFrom && d.CreatedAt <= request.DateTo)
            .AsQueryable();

        // Apply search filter if provided
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            baseQuery = baseQuery.Where(d => 
                d.Agent.Name.ToLower().Contains(searchTerm) ||
                d.Agent.Code.ToLower().Contains(searchTerm));
        }

        // Group by agent and calculate statistics
        var agentDebtStats = await baseQuery
            .GroupBy(d => new { d.AgentId, d.Agent.Name, d.Agent.Code })
            .Select(g => new
            {
                g.Key.AgentId,
                AgentName = g.Key.Name,
                AgentCode = g.Key.Code,
                OrderCount = g.Select(d => d.OrderId).Distinct().Count(),
                TotalDebtAmount = g.Sum(d => d.OriginalAmount),
                TotalPaidAmount = g.Sum(d => d.PaidAmount)
            })
            .ToListAsync(cancellationToken);

        // Convert to DTOs with calculated TotalDueAmount
        var agentDebtDetails = agentDebtStats.Select(stat => new AgentDebtDetailResponseDto
        {
            AgentId = stat.AgentId,
            AgentName = stat.AgentName,
            AgentCode = stat.AgentCode,
            OrderCount = stat.OrderCount,
            TotalDebtAmount = stat.TotalDebtAmount,
            TotalPaidAmount = stat.TotalPaidAmount,
            TotalDueAmount = stat.TotalDebtAmount - stat.TotalPaidAmount
        }).AsQueryable();

        // Get total count before pagination
        var totalCount = agentDebtDetails.Count();

        // Apply sorting
        agentDebtDetails = request.SortBy?.ToLower() switch
        {
            "agentname" => request.IsDescending == true 
                ? agentDebtDetails.OrderByDescending(a => a.AgentName) 
                : agentDebtDetails.OrderBy(a => a.AgentName),
            "agentcode" => request.IsDescending == true 
                ? agentDebtDetails.OrderByDescending(a => a.AgentCode) 
                : agentDebtDetails.OrderBy(a => a.AgentCode),
            "ordercount" => request.IsDescending == true 
                ? agentDebtDetails.OrderByDescending(a => a.OrderCount) 
                : agentDebtDetails.OrderBy(a => a.OrderCount),
            "totaldebtamount" => request.IsDescending == true 
                ? agentDebtDetails.OrderByDescending(a => a.TotalDebtAmount) 
                : agentDebtDetails.OrderBy(a => a.TotalDebtAmount),
            "totalpaidamount" => request.IsDescending == true 
                ? agentDebtDetails.OrderByDescending(a => a.TotalPaidAmount) 
                : agentDebtDetails.OrderBy(a => a.TotalPaidAmount),
            "totaldueamount" => request.IsDescending == true 
                ? agentDebtDetails.OrderByDescending(a => a.TotalDueAmount) 
                : agentDebtDetails.OrderBy(a => a.TotalDueAmount),
            _ => request.IsDescending == true 
                ? agentDebtDetails.OrderByDescending(a => a.TotalDueAmount) 
                : agentDebtDetails.OrderBy(a => a.TotalDueAmount)
        };

        // Apply pagination
        var paginatedResults = agentDebtDetails
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return new PaginationResponse<AgentDebtDetailResponseDto>(
            request.PageIndex,
            request.PageSize,
            totalCount,
            paginatedResults);
    }
}
