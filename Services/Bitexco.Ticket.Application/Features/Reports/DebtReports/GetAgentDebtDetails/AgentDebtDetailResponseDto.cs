namespace Bitexco.Ticket.Application.Features.Reports.DebtReports.GetAgentDebtDetails;

/// <summary>
/// Response DTO for individual agent debt details
/// </summary>
public record AgentDebtDetailResponseDto
{
    /// <summary>
    /// Agent ID
    /// </summary>
    public Guid AgentId { get; set; }
    
    /// <summary>
    /// Tên đại lý
    /// </summary>
    public string AgentName { get; set; } = null!;
    
    /// <summary>
    /// Mã đại lý
    /// </summary>
    public string AgentCode { get; set; } = null!;
    
    /// <summary>
    /// Số đơn hàng trong khoảng thời gian
    /// </summary>
    public int OrderCount { get; set; }
    
    /// <summary>
    /// Tổng công nợ (OriginalAmount)
    /// </summary>
    public decimal TotalDebtAmount { get; set; }
    
    /// <summary>
    /// Số tiền đại lý đã trả
    /// </summary>
    public decimal TotalPaidAmount { get; set; }
    
    /// <summary>
    /// Số tiền đại lý cần trả (TotalDebtAmount - TotalPaidAmount)
    /// </summary>
    public decimal TotalDueAmount { get; set; }
}
