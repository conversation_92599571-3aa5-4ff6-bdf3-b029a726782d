namespace Bitexco.Ticket.Application.Features.Reports.DebtReports.GetAgentDebtDetails;

/// <summary>
/// Query to get paginated agent debt details with filtering and sorting
/// </summary>
public class GetAgentDebtDetailsQuery 
    : IRequest<PaginationResponse<AgentDebtDetailResponseDto>>, IPaginationRequest, ISortRequest
{
    /// <summary>
    /// Search term for agent name or code
    /// </summary>
    public string? SearchTerm { get; set; }
    
    /// <summary>
    /// Start date for debt calculation
    /// </summary>
    public DateTime? DateFrom { get; set; }
    
    /// <summary>
    /// End date for debt calculation
    /// </summary>
    public DateTime? DateTo { get; set; }
    
    /// <summary>
    /// Page index for pagination (1-based)
    /// </summary>
    public int PageIndex { get; set; } = 1;
    
    /// <summary>
    /// Page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 100;
    
    /// <summary>
    /// Field to sort by
    /// </summary>
    public string? SortBy { get; set; }
    
    /// <summary>
    /// Sort direction (true for descending, false for ascending)
    /// </summary>
    public bool? IsDescending { get; set; }
}
