namespace Bitexco.Ticket.Application.Features.Invoices.GetInvoices;

using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Abstractions;

/// <summary>
/// Query to get paginated list of invoices with optional filtering
/// </summary>
public record GetInvoicesQuery
    : IRequest<PaginationResponse<GetInvoicesResponseDto>>, IPaginationRequest, ISortRequest
{
    // Pagination parameters
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 100;
    public string? SortBy { get; set; }
    public bool? IsDescending { get; set; }
    public InvoiceStatus Status { get; set; }
}
