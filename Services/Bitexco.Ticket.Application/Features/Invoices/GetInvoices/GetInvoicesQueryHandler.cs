namespace Bitexco.Ticket.Application.Features.Invoices.GetInvoices;

using Bitexco.Ticket.Application.Data;
using BuildingBlocks.Abstractions;

/// <summary>
/// Hand<PERSON> for getting paginated list of invoices
/// </summary>
public class GetInvoicesQueryHandler(IApplicationDbContext dbContext) : IRequestHandler<GetInvoicesQuery, PaginationResponse<GetInvoicesResponseDto>>
{
    public async Task<PaginationResponse<GetInvoicesResponseDto>> Handle(GetInvoicesQuery request, CancellationToken cancellationToken)
    {
        // Get paginated invoices with applied filters
        var query = dbContext.Invoices
            .AsNoTracking()
            .Include(i => i.Customer)
            .Where(i => i.Status == request.Status)
            .AsQueryable();

        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = request.SortBy?.ToLowerInvariant() switch
        {
            "customertype" => query = request.IsDescending == true
                ? query.OrderByDescending(i => i.Order.CustomerType)
                : query.OrderBy(i => i.Order.CustomerType),
            "amount" => query = request.IsDescending == true
                ? query.OrderByDescending(i => i.TotalAmount)
                : query.OrderBy(i => i.TotalAmount),
            "customername" => query = request.IsDescending == true
                ? query.OrderByDescending(i => i.Customer != null ? i.Customer.FullName : string.Empty)
                : query.OrderBy(i => i.Customer != null ? i.Customer.FullName : string.Empty),
            _ => query = request.IsDescending == true
                ? query.OrderByDescending(i => i.CreatedAt)
                : query.OrderBy(i => i.CreatedAt)
        };

        // Apply pagination
        var invoices = await query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ProjectToType<GetInvoicesResponseDto>()
            .ToListAsync(cancellationToken);

        return new PaginationResponse<GetInvoicesResponseDto>
        (
            request.PageIndex,
            request.PageSize,
            totalCount,
            invoices
        );
    }
}
