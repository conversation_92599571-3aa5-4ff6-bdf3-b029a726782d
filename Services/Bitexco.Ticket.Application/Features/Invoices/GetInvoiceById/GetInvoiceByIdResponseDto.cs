namespace Bitexco.Ticket.Application.Features.Invoices.GetInvoiceById;

using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Orders.GetOrderById;

/// <summary>
/// Detailed response DTO for a single invoice
/// </summary>
public record GetInvoiceByIdResponseDto : OrderResponseDto
{
    public CustomerResponseDto? Customer { get; set; }
    public AgentResponseDto? Agent { get; set; }
    public RetailChannelResponseDto? RetailChannel { get; set; }
    public UserDetailDto? PosUser { get; set; }
    public List<OrderItemDetailDto>? OrderItems { get; set; } = [];
}