namespace Bitexco.Ticket.Application.Features.Invoices.GetInvoiceById;

using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Application.Features.Orders.GetOrderById;
using BuildingBlocks.Abstractions;

/// <summary>
/// Handler for getting invoice details by ID
/// </summary>
public class GetInvoiceByIdQueryHandler(IApplicationDbContext dbContext) : IRequestHandler<GetInvoiceByIdQuery, Response<GetInvoiceByIdResponseDto>>
{
    public async Task<Response<GetInvoiceByIdResponseDto>> Handle(GetInvoiceByIdQuery request, CancellationToken cancellationToken)
    {
        // Retrieve invoice with all related data
        var invoice = await dbContext.Invoices
            .AsNoTracking()
            .Include(i => i.Customer)
            .FirstOrDefaultAsync(i => i.Id == request.Id, cancellationToken)
            ?? throw new InvoiceNotFoundException(request.Id);

        var invoiceDto = invoice.Adapt<GetInvoiceByIdResponseDto>();

        //get order items
        var order = await dbContext.Orders
            .AsNoTracking()
            .Include(o => o.Agent)
            .Include(o => o.RetailChannel)
            .Include(o => o.PosUser)
            .Include(o => o.OrderItems).ThenInclude(i => i.TicketType)
            .FirstOrDefaultAsync(o => o.Id == invoice.OrderId, cancellationToken)
            ?? throw new OrderNotFoundException(invoice.OrderId);

        // Map order details to invoice DTO
        invoiceDto.Agent = order.Agent?.Adapt<AgentResponseDto>();
        invoiceDto.RetailChannel = order.RetailChannel?.Adapt<RetailChannelResponseDto>();
        invoiceDto.PosUser = order.PosUser?.Adapt<UserDetailDto>();
        invoiceDto.OrderItems = [.. order.OrderItems.Select(i => i.Adapt<OrderItemDetailDto>())];

        return new Response<GetInvoiceByIdResponseDto>(invoiceDto);
    }
}
