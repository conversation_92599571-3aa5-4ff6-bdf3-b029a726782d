using System.Collections.Concurrent;

namespace Bitexco.Ticket.Application.Features.LanguageLogic;

/// <summary>
/// Static storage for language messages, following DataStorageObject pattern from MediTrack
/// Thread-safe using ConcurrentDictionary only (no locks needed)
/// </summary>
public static class LanguageDataStorage
{
    private static readonly ConcurrentDictionary<string, string> _languageMessages = new();
    private static bool _isInitialized = false;

    /// <summary>
    /// Get language message by key with language suffix
    /// Key format: "MessageKey_en", "MessageKey_vi", etc.
    /// </summary>
    /// <param name="key">Base message key</param>
    /// <param name="languageCode">Language code (default: "en")</param>
    /// <returns>Localized message or key if not found</returns>
    public static string GetMessage(string key, string languageCode = "en")
    {
        if (string.IsNullOrWhiteSpace(key))
            return key;

        // Try with language suffix first
        var keyWithLang = $"{key}_{languageCode}";
        if (_languageMessages.TryGetValue(keyWithLang, out var message))
        {
            return message;
        }

        // Fallback to English if current language not found
        if (languageCode != "en")
        {
            var englishKey = $"{key}_en";
            if (_languageMessages.TryGetValue(englishKey, out var englishMessage))
            {
                return englishMessage;
            }
        }

        // Fallback to base key without language suffix
        if (_languageMessages.TryGetValue(key, out var baseMessage))
        {
            return baseMessage;
        }

        // Return key if nothing found
        return key;
    }

    /// <summary>
    /// Add or update language message
    /// </summary>
    /// <param name="key">Message key (with or without language suffix)</param>
    /// <param name="message">Message content</param>
    public static void AddOrUpdateMessage(string key, string message)
    {
        _languageMessages.AddOrUpdate(key, message, (k, oldValue) => message);
    }

    /// <summary>
    /// Add range of language messages
    /// </summary>
    /// <param name="messages">Dictionary of key-value pairs</param>
    public static void AddRangeMessages(IDictionary<string, string> messages)
    {
        foreach (var kvp in messages)
        {
            _languageMessages.TryAdd(kvp.Key, kvp.Value);
        }
    }

    /// <summary>
    /// Clear all language messages
    /// </summary>
    public static void Clear()
    {
        _languageMessages.Clear();
    }

    /// <summary>
    /// Get all language messages
    /// </summary>
    /// <returns>Dictionary of all messages</returns>
    public static Dictionary<string, string> GetAllMessages()
    {
        return new Dictionary<string, string>(_languageMessages);
    }

    /// <summary>
    /// Get total count of language messages
    /// </summary>
    public static int Count => _languageMessages.Count;

    /// <summary>
    /// Check if initialized
    /// </summary>
    public static bool IsInitialized => _isInitialized;

    /// <summary>
    /// Mark as initialized (following DataStorageObject pattern - no lock needed)
    /// </summary>
    public static void MarkAsInitialized()
    {
        _isInitialized = true;
    }

    /// <summary>
    /// Reset initialization status
    /// </summary>
    public static void ResetInitialization()
    {
        _isInitialized = false;
    }
}
