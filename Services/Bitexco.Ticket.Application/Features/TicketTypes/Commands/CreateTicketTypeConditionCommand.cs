using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to add a new condition to a ticket type
/// </summary>
public record CreateTicketTypeConditionCommand(Guid TicketTypeId, TicketTypeConditionDto Condition) 
    : ICommand<Response<CreateTicketTypeConditionResult>>;

/// <summary>
/// Result model for CreateTicketTypeConditionCommand
/// </summary>
public record CreateTicketTypeConditionResult(Guid Id);

/// <summary>
/// Validator for CreateTicketTypeConditionCommand
/// </summary>
public class CreateTicketTypeConditionCommandValidator : AbstractValidator<CreateTicketTypeConditionCommand>
{
    public CreateTicketTypeConditionCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");

        RuleFor(x => x.Condition.Name)
            .NotEmpty().WithMessage("Condition name is required.")
            .MaximumLength(100).WithMessage("Condition name cannot exceed 100 characters.");

        RuleFor(x => x.Condition.Description)
            .NotEmpty().WithMessage("Description is required.")
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters.");

        // If FromValue is provided, it must be less than ToValue if also provided
        When(x => !string.IsNullOrEmpty(x.Condition.FromValue) && !string.IsNullOrEmpty(x.Condition.ToValue), () =>
        {
            RuleFor(x => x.Condition.FromValue!)
                .LessThan(x => x.Condition.ToValue!)
                .WithMessage("Minimum value must be less than maximum value.");
        });
    }
}

/// <summary>
/// Handler for CreateTicketTypeConditionCommand
/// </summary>
public class CreateTicketTypeConditionCommandHandler(IApplicationDbContext dbContext) 
    : ICommandHandler<CreateTicketTypeConditionCommand, Response<CreateTicketTypeConditionResult>>
{
    public async Task<Response<CreateTicketTypeConditionResult>> Handle(
        CreateTicketTypeConditionCommand request,
        CancellationToken cancellationToken)
    {
        // Check if the ticket type exists
        var ticketType = await dbContext.TicketTypes
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Create new condition
        var condition = request.Condition.Adapt<TicketTypeCondition>();

        condition.Id = Guid.NewGuid();
        condition.TicketTypeId = request.TicketTypeId;

        await dbContext.TicketTypeConditions.AddAsync(condition, cancellationToken);
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (result <= 0)
        {
            throw new NoEntityCreatedException("TicketTypeCondition");
        }

        return new Response<CreateTicketTypeConditionResult>(new CreateTicketTypeConditionResult(condition.Id));
    }
}
