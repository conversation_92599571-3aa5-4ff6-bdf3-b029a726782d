using System.Text.Json;
using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.TicketTypes.Dtos;
using Bitexco.Ticket.Application.Services;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Exceptions;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to approve or reject price changes
/// </summary>
public record ApprovePriceChangesCommand(Guid TicketTypeId, bool Approve, string? Comments) 
    : ICommand<Response<ApprovePriceChangesResult>>;

/// <summary>
/// Result model for ApprovePriceChangesCommand
/// </summary>
public record ApprovePriceChangesResult(bool Success);

/// <summary>
/// Validator for ApprovePriceChangesCommand
/// </summary>
public class ApprovePriceChangesCommandValidator : AbstractValidator<ApprovePriceChangesCommand>
{
    public ApprovePriceChangesCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");

        RuleFor(x => x.Comments)
            .MaximumLength(1000).WithMessage("Comments cannot exceed 1000 characters.")
            .When(x => !string.IsNullOrEmpty(x.Comments));
    }
}

/// <summary>
/// Handler for ApprovePriceChangesCommand
/// </summary>
public class ApprovePriceChangesCommandHandler(IApplicationDbContext dbContext, ICurrentUserService currentUserService) 
    : ICommandHandler<ApprovePriceChangesCommand, Response<ApprovePriceChangesResult>>
{
    public async Task<Response<ApprovePriceChangesResult>> Handle(
        ApprovePriceChangesCommand request,
        CancellationToken cancellationToken)
    {
        // Get current user
        var currentUserId = currentUserService.GetUserId() 
            ?? throw new UnauthorizedAccessException("User must be authenticated to approve or reject price changes");


        // Check if ticket type exists
        var ticketType = await dbContext.TicketTypes
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Find pending approval for this ticket type
        var pendingApproval = await dbContext.PriceApprovals
            .AsTracking()
            .FirstOrDefaultAsync(pa => pa.TicketTypeId == request.TicketTypeId && 
                                      pa.Status == PriceApprovalStatus.pending, 
                                cancellationToken)
            ?? throw new PricingRuleNotFoundException(
                $"No pending price approval found for ticket type with ID {request.TicketTypeId}");

        // Update approval status
        pendingApproval.Status = request.Approve ? PriceApprovalStatus.approved : PriceApprovalStatus.rejected;
        pendingApproval.ApprovalComments = request.Comments;
        pendingApproval.ApprovedByUserId = Guid.Parse(currentUserId);
        pendingApproval.ApprovedAt = DateTime.UtcNow;

        // If approved, apply the price changes
        if (request.Approve)
        {
            try
            {
                var priceChanges = System.Text.Json.JsonSerializer.Deserialize<dynamic>(
                    pendingApproval.ProposedChanges, 
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                // Extract proposed prices
                List<TicketPriceDto> proposedPrices = [];
                
                if (priceChanges?.ProposedPrices is JsonElement proposedPricesElement && 
                    proposedPricesElement.ValueKind == JsonValueKind.Array)
                {
                    foreach (var priceElement in proposedPricesElement.EnumerateArray())
                    {
                        var price = new TicketPriceDto
                        {
                            Price = priceElement.GetProperty("price").GetDecimal(),
                            EffectiveDateFrom = DateOnly.Parse(priceElement.GetProperty("effectiveDateFrom").GetString()!)
                        };

                        if (priceElement.TryGetProperty("effectiveDateTo", out var dateToElement) && 
                            dateToElement.ValueKind != JsonValueKind.Null)
                        {
                            price.EffectiveDateTo = DateOnly.Parse(dateToElement.GetString()!);
                        }

                        if (priceElement.TryGetProperty("pricingRuleId", out var ruleIdElement) && 
                            ruleIdElement.ValueKind != JsonValueKind.Null)
                        {
                            price.PricingRuleId = Guid.Parse(ruleIdElement.GetString()!);
                        }

                        proposedPrices.Add(price);
                    }
                }

                // Apply the proposed prices
                foreach (var proposedPrice in proposedPrices)
                {
                    var newPrice = new TicketPrice
                    {
                        Id = Guid.NewGuid(),
                        TicketTypeId = request.TicketTypeId,
                        PricingRuleId = proposedPrice.PricingRuleId,
                        Price = proposedPrice.Price,
                        EffectiveDateFrom = proposedPrice.EffectiveDateFrom,
                        EffectiveDateTo = proposedPrice.EffectiveDateTo
                    };
                    
                    await dbContext.TicketPrices.AddAsync(newPrice, cancellationToken);
                }

                // Update approval status to implemented once changes are applied
                pendingApproval.Status = PriceApprovalStatus.implemented;
                pendingApproval.ScheduledEffectiveDate = proposedPrices.Min(p => p.EffectiveDateFrom);
            }
            catch (Exception ex)
            {
                // If there's an error applying price changes, revert to approved state
                pendingApproval.Status = PriceApprovalStatus.approved;
                pendingApproval.ApprovalComments += $"\nError implementing price changes: {ex.Message}";
            }
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("PriceApproval", pendingApproval.Id);
        }

        return new Response<ApprovePriceChangesResult>(new ApprovePriceChangesResult(true));
    }
}
