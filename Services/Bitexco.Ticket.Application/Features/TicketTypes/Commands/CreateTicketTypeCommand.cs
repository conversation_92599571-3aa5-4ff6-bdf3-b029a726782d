using Bitexco.Ticket.Application.Features.TicketTypes.Dtos;
using BuildingBlocks.Utils;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to create a new ticket type
/// </summary>
public record CreateTicketTypeCommand(TicketTypeUpsertDto TicketType) : ICommand<Response<CreateTicketTypeResult>>;

/// <summary>
/// Result model for CreateTicketTypeCommand
/// </summary>
public record CreateTicketTypeResult(Guid Id);

/// <summary>
/// Validator for CreateTicketTypeCommand
/// </summary>
public class CreateTicketTypeCommandValidator : AbstractValidator<CreateTicketTypeCommand>
{
    public CreateTicketTypeCommandValidator()
    {
        RuleFor(x => x.TicketType.Name)
            .NotEmpty().WithMessage("Name is required.")
            .MaximumLength(100).WithMessage("Name cannot exceed 100 characters.");

        RuleFor(x => x.TicketType.Description)
            .MaximumLength(500).WithMessage("Description cannot exceed 500 characters.")
            .When(x => !string.IsNullOrEmpty(x.TicketType.Description));

        RuleFor(x => x.TicketType.TicketCodeLengthAfterPrefix)
            .GreaterThan(0).WithMessage("Ticket code length after prefix must be greater than 0.");
    }
}

/// <summary>
/// Handler for CreateTicketTypeCommand
/// </summary>
public class CreateTicketTypeCommandHandler(IApplicationDbContext dbContext)
    : ICommandHandler<CreateTicketTypeCommand, Response<CreateTicketTypeResult>>
{
    public async Task<Response<CreateTicketTypeResult>> Handle(
        CreateTicketTypeCommand request,
        CancellationToken cancellationToken)
    {
        var entity = request.TicketType.Adapt<TicketType>();

        //randomly generate code 7 char if not provided
        if (string.IsNullOrEmpty(entity.Code))
        {
            entity.Code = "TT" +CommonUtils.GenerateNewCode(7);
        }

        await dbContext.TicketTypes.AddAsync(entity, cancellationToken);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityCreatedException("TicketType");
        }

        return new Response<CreateTicketTypeResult>(new CreateTicketTypeResult(entity.Id));
    }
}
