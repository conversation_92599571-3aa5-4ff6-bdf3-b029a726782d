using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to update an existing ticket type condition
/// </summary>
public record UpdateTicketTypeConditionCommand(Guid TicketTypeId, Guid ConditionId, TicketTypeConditionDto Condition) 
    : ICommand<Response<UpdateTicketTypeConditionResult>>;

/// <summary>
/// Result model for UpdateTicketTypeConditionCommand
/// </summary>
public record UpdateTicketTypeConditionResult(bool Success);

/// <summary>
/// Validator for UpdateTicketTypeConditionCommand
/// </summary>
public class UpdateTicketTypeConditionCommandValidator : AbstractValidator<UpdateTicketTypeConditionCommand>
{
    public UpdateTicketTypeConditionCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");
            
        RuleFor(x => x.ConditionId)
            .NotEmpty().WithMessage("Condition Id is required.");

        RuleFor(x => x.Condition.Name)
            .NotEmpty().WithMessage("Condition name is required.")
            .MaximumLength(100).WithMessage("Condition name cannot exceed 100 characters.");

        RuleFor(x => x.Condition.Description)
            .NotEmpty().WithMessage("Description is required.")
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters.");

        // If FromValue is provided, it must be less than ToValue if also provided
        When(x => !string.IsNullOrEmpty(x.Condition.FromValue) && !string.IsNullOrEmpty(x.Condition.ToValue), () =>
        {
            RuleFor(x => x.Condition.FromValue!)
                .LessThan(x => x.Condition.ToValue!)
                .WithMessage("Minimum value must be less than maximum value.");
        });
    }
}

/// <summary>
/// Handler for UpdateTicketTypeConditionCommand
/// </summary>
public class UpdateTicketTypeConditionCommandHandler(IApplicationDbContext dbContext) 
    : ICommandHandler<UpdateTicketTypeConditionCommand, Response<UpdateTicketTypeConditionResult>>
{
    public async Task<Response<UpdateTicketTypeConditionResult>> Handle(
        UpdateTicketTypeConditionCommand request,
        CancellationToken cancellationToken)
    {
        // Ensure ticket type exists
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Find the condition to update
        var condition = await dbContext.TicketTypeConditions
            .AsTracking()
            .FirstOrDefaultAsync(c => c.Id == request.ConditionId && c.TicketTypeId == request.TicketTypeId, cancellationToken)
            ?? throw new TicketPriceNotFoundException(request.ConditionId, request.TicketTypeId);

        // Update the condition properties
        condition.Name = request.Condition.Name;
        condition.Type = request.Condition.Type;
        condition.Description = request.Condition.Description;
        condition.FromValue = request.Condition.FromValue;
        condition.ToValue = request.Condition.ToValue;
        condition.DisplayOrder = request.Condition.DisplayOrder;
        condition.IsActive = request.Condition.IsActive;

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("TicketTypeCondition", request.ConditionId);
        }

        return new Response<UpdateTicketTypeConditionResult>(new UpdateTicketTypeConditionResult(true));
    }
}
