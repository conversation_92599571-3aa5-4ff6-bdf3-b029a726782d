using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to update an existing visit time rule
/// </summary>
public record UpdateVisitTimeRuleCommand(Guid TicketTypeId, Guid TimeRuleId, VisitTimeRuleDto TimeRule) 
    : ICommand<Response<UpdateVisitTimeRuleResult>>;

/// <summary>
/// Result model for UpdateVisitTimeRuleCommand
/// </summary>
public record UpdateVisitTimeRuleResult(bool Success);

/// <summary>
/// Validator for UpdateVisitTimeRuleCommand
/// </summary>
public class UpdateVisitTimeRuleCommandValidator : AbstractValidator<UpdateVisitTimeRuleCommand>
{
    public UpdateVisitTimeRuleCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");
            
        RuleFor(x => x.TimeRuleId)
            .NotEmpty().WithMessage("Time Rule Id is required.");

        RuleFor(x => x.TimeRule.Name)
            .NotEmpty().WithMessage("Time rule name is required.")
            .MaximumLength(100).WithMessage("Time rule name cannot exceed 100 characters.");

        // If both entry times are provided, latest must be after earliest
        When(x => x.TimeRule.EarliestEntryTime.HasValue && x.TimeRule.LatestEntryTime.HasValue, () =>
        {
            RuleFor(x => x.TimeRule.LatestEntryTime!.Value)
                .GreaterThan(x => x.TimeRule.EarliestEntryTime!.Value)
                .WithMessage("Latest entry time must be after earliest entry time.");
        });

        // Max duration must be positive if provided
        When(x => x.TimeRule.MaxDurationMinutes.HasValue, () =>
        {
            RuleFor(x => x.TimeRule.MaxDurationMinutes!.Value)
                .GreaterThan(0)
                .WithMessage("Maximum duration must be greater than 0 minutes.");
        });

        // Days of week must be valid
        RuleForEach(x => x.TimeRule.DaysOfWeek)
            .InclusiveBetween(0, 6)
            .WithMessage("Days of week must be between 0 (Sunday) and 6 (Saturday).");
    }
}

/// <summary>
/// Handler for UpdateVisitTimeRuleCommand
/// </summary>
public class UpdateVisitTimeRuleCommandHandler(IApplicationDbContext dbContext) 
    : ICommandHandler<UpdateVisitTimeRuleCommand, Response<UpdateVisitTimeRuleResult>>
{
    public async Task<Response<UpdateVisitTimeRuleResult>> Handle(
        UpdateVisitTimeRuleCommand request,
        CancellationToken cancellationToken)
    {
        // Check if ticket type exists
        var ticketType = await dbContext.TicketTypes
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Find the time rule to update
        var timeRule = await dbContext.VisitTimeRules
            .AsTracking()
            .FirstOrDefaultAsync(tr => tr.Id == request.TimeRuleId && tr.TicketTypeId == request.TicketTypeId, cancellationToken)
            ?? throw new VisitTimeRuleNotFoundException(request.TimeRuleId, request.TicketTypeId);

        // Update time rule properties
        timeRule.Name = request.TimeRule.Name;
        timeRule.EarliestEntryTime = request.TimeRule.EarliestEntryTime;
        timeRule.LatestEntryTime = request.TimeRule.LatestEntryTime;
        timeRule.MaxDurationMinutes = request.TimeRule.MaxDurationMinutes;
        timeRule.DaysOfWeek = request.TimeRule.DaysOfWeek ?? [];
        timeRule.AppliesToHolidays = request.TimeRule.AppliesToHolidays;
        timeRule.IsActive = request.TimeRule.IsActive;

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("VisitTimeRule", request.TimeRuleId);
        }

        return new Response<UpdateVisitTimeRuleResult>(new UpdateVisitTimeRuleResult(true));
    }
}
