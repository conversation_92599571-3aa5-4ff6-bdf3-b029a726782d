using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to create a new visit time rule for a ticket type
/// </summary>
public record CreateVisitTimeRuleCommand(Guid TicketTypeId, VisitTimeRuleDto TimeRule) 
    : ICommand<Response<CreateVisitTimeRuleResult>>;

/// <summary>
/// Result model for CreateVisitTimeRuleCommand
/// </summary>
public record CreateVisitTimeRuleResult(Guid Id);

/// <summary>
/// Validator for CreateVisitTimeRuleCommand
/// </summary>
public class CreateVisitTimeRuleCommandValidator : AbstractValidator<CreateVisitTimeRuleCommand>
{
    public CreateVisitTimeRuleCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");

        RuleFor(x => x.TimeRule.Name)
            .NotEmpty().WithMessage("Time rule name is required.")
            .MaximumLength(100).WithMessage("Time rule name cannot exceed 100 characters.");

        // If both entry times are provided, latest must be after earliest
        When(x => x.TimeRule.EarliestEntryTime.HasValue && x.TimeRule.LatestEntryTime.HasValue, () =>
        {
            RuleFor(x => x.TimeRule.LatestEntryTime!.Value)
                .GreaterThan(x => x.TimeRule.EarliestEntryTime!.Value)
                .WithMessage("Latest entry time must be after earliest entry time.");
        });

        // Max duration must be positive if provided
        When(x => x.TimeRule.MaxDurationMinutes.HasValue, () =>
        {
            RuleFor(x => x.TimeRule.MaxDurationMinutes!.Value)
                .GreaterThan(0)
                .WithMessage("Maximum duration must be greater than 0 minutes.");
        });

        // Days of week must be valid
        RuleForEach(x => x.TimeRule.DaysOfWeek)
            .InclusiveBetween(0, 6)
            .WithMessage("Days of week must be between 0 (Sunday) and 6 (Saturday).");
    }
}

/// <summary>
/// Handler for CreateVisitTimeRuleCommand
/// </summary>
public class CreateVisitTimeRuleCommandHandler(IApplicationDbContext dbContext) 
    : ICommandHandler<CreateVisitTimeRuleCommand, Response<CreateVisitTimeRuleResult>>
{
    public async Task<Response<CreateVisitTimeRuleResult>> Handle(
        CreateVisitTimeRuleCommand request,
        CancellationToken cancellationToken)
    {
        // Check if ticket type exists
        var ticketType = await dbContext.TicketTypes
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Create new visit time rule
        var timeRule = new VisitTimeRule
        {
            Id = Guid.NewGuid(),
            TicketTypeId = request.TicketTypeId,
            Name = request.TimeRule.Name,
            EarliestEntryTime = request.TimeRule.EarliestEntryTime,
            LatestEntryTime = request.TimeRule.LatestEntryTime,
            MaxDurationMinutes = request.TimeRule.MaxDurationMinutes,
            DaysOfWeek = request.TimeRule.DaysOfWeek ?? [],
            AppliesToHolidays = request.TimeRule.AppliesToHolidays,
            IsActive = request.TimeRule.IsActive
        };

        await dbContext.VisitTimeRules.AddAsync(timeRule, cancellationToken);
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (result <= 0)
        {
            throw new NoEntityCreatedException("VisitTimeRule");
        }

        return new Response<CreateVisitTimeRuleResult>(new CreateVisitTimeRuleResult(timeRule.Id));
    }
}
