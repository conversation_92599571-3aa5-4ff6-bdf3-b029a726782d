namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to delete a ticket type condition
/// </summary>
public record DeleteTicketTypeConditionCommand(Guid TicketTypeId, Guid ConditionId) 
    : ICommand<Response<DeleteTicketTypeConditionResult>>;

/// <summary>
/// Result model for DeleteTicketTypeConditionCommand
/// </summary>
public record DeleteTicketTypeConditionResult(bool Success);

/// <summary>
/// Validator for DeleteTicketTypeConditionCommand
/// </summary>
public class DeleteTicketTypeConditionCommandValidator : AbstractValidator<DeleteTicketTypeConditionCommand>
{
    public DeleteTicketTypeConditionCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");
            
        RuleFor(x => x.ConditionId)
            .NotEmpty().WithMessage("Condition Id is required.");
    }
}

/// <summary>
/// Handler for DeleteTicketTypeConditionCommand
/// </summary>
public class DeleteTicketTypeConditionCommandHandler(IApplicationDbContext dbContext) 
    : ICommandHandler<DeleteTicketTypeConditionCommand, Response<DeleteTicketTypeConditionResult>>
{
    public async Task<Response<DeleteTicketTypeConditionResult>> Handle(
        DeleteTicketTypeConditionCommand request,
        CancellationToken cancellationToken)
    {
        // Ensure ticket type exists
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Find the condition to delete
        var condition = await dbContext.TicketTypeConditions
            .AsTracking()
            .FirstOrDefaultAsync(c => c.Id == request.ConditionId && c.TicketTypeId == request.TicketTypeId, cancellationToken)
            ?? throw new TicketPriceNotFoundException(request.ConditionId, request.TicketTypeId);

        // Soft delete
        condition.IsDeleted = true;

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (result <= 0)
        {
            throw new NoEntityDeletedException("TicketTypeCondition", request.ConditionId);
        }

        return new Response<DeleteTicketTypeConditionResult>(new DeleteTicketTypeConditionResult(true));
    }
}
