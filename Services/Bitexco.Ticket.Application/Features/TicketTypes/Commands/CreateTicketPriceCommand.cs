using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to add a new price to a ticket type
/// </summary>
public record CreateTicketPriceCommand(Guid TicketTypeId, TicketPriceDto Price) 
    : ICommand<Response<CreateTicketPriceResult>>;

/// <summary>
/// Result model for CreateTicketPriceCommand
/// </summary>
public record CreateTicketPriceResult(Guid Id);

/// <summary>
/// Validator for CreateTicketPriceCommand
/// </summary>
public class CreateTicketPriceCommandValidator : AbstractValidator<CreateTicketPriceCommand>
{
    public CreateTicketPriceCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");

        RuleFor(x => x.Price.Price)
            .GreaterThanOrEqualTo(0).WithMessage("Price must be greater than or equal to 0.");

        // If EffectiveDateTo is provided, it must be later than EffectiveDateFrom
        When(x => x.Price.EffectiveDateTo.HasValue, () =>
        {
            RuleFor(x => x.Price.EffectiveDateTo!.Value)
                .GreaterThan(x => x.Price.EffectiveDateFrom)
                .WithMessage("Effective end date must be later than the effective start date.");
        });
    }
}

/// <summary>
/// Handler for CreateTicketPriceCommand
/// </summary>
public class CreateTicketPriceCommandHandler(IApplicationDbContext dbContext) 
    : ICommandHandler<CreateTicketPriceCommand, Response<CreateTicketPriceResult>>
{
    public async Task<Response<CreateTicketPriceResult>> Handle(
        CreateTicketPriceCommand request,
        CancellationToken cancellationToken)
    {
        // Check if the ticket type exists
        var ticketType = await dbContext.TicketTypes
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Validate pricing rule reference if provided
        if (request.Price.PricingRuleId.HasValue)
        {
            var pricingRuleExists = await dbContext.PricingRules
                .AnyAsync(p => p.Id == request.Price.PricingRuleId.Value, cancellationToken);

            if (!pricingRuleExists)
            {
                throw new PricingRuleNotFoundException(request.Price.PricingRuleId.Value);
            }
        }

        // Create new ticket price
        var ticketPrice = new TicketPrice
        {
            Id = Guid.NewGuid(),
            TicketTypeId = request.TicketTypeId,
            PricingRuleId = request.Price.PricingRuleId,
            Price = request.Price.Price,
            EffectiveDateFrom = request.Price.EffectiveDateFrom,
            EffectiveDateTo = request.Price.EffectiveDateTo
        };

        await dbContext.TicketPrices.AddAsync(ticketPrice, cancellationToken);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityCreatedException("TicketPrice");
        }

        return new Response<CreateTicketPriceResult>(new CreateTicketPriceResult(ticketPrice.Id));
    }
}
