using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Services;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to submit price changes for approval
/// </summary>
public record SubmitPriceApprovalCommand(Guid TicketTypeId, List<TicketPriceDto> ProposedPrices, string? Comments) 
    : ICommand<Response<SubmitPriceApprovalResult>>;

/// <summary>
/// Result model for SubmitPriceApprovalCommand
/// </summary>
public record SubmitPriceApprovalResult(Guid ApprovalId);

/// <summary>
/// Validator for SubmitPriceApprovalCommand
/// </summary>
public class SubmitPriceApprovalCommandValidator : AbstractValidator<SubmitPriceApprovalCommand>
{
    public SubmitPriceApprovalCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");
            
        RuleFor(x => x.ProposedPrices)
            .NotEmpty().WithMessage("At least one proposed price must be provided.");

        RuleForEach(x => x.ProposedPrices)
            .ChildRules(price => {
                price.RuleFor(p => p.Price)
                     .GreaterThanOrEqualTo(0).WithMessage("Price must be greater than or equal to 0.");
                
                price.RuleFor(p => p.EffectiveDateFrom)
                     .NotEmpty().WithMessage("Effective start date is required.");
                
                // When end date is provided, it must be after start date
                price.When(p => p.EffectiveDateTo.HasValue, () => {
                    price.RuleFor(p => p.EffectiveDateTo!.Value)
                         .GreaterThan(p => p.EffectiveDateFrom)
                         .WithMessage("Effective end date must be later than the effective start date.");
                });
            });

        RuleFor(x => x.Comments)
            .MaximumLength(1000).WithMessage("Comments cannot exceed 1000 characters.")
            .When(x => !string.IsNullOrEmpty(x.Comments));
    }
}

/// <summary>
/// Handler for SubmitPriceApprovalCommand
/// </summary>
public class SubmitPriceApprovalCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService) 
    : ICommandHandler<SubmitPriceApprovalCommand, Response<SubmitPriceApprovalResult>>
{
    public async Task<Response<SubmitPriceApprovalResult>> Handle(
        SubmitPriceApprovalCommand request,
        CancellationToken cancellationToken)
    {
        // Validate ticket type exists
        var ticketType = await dbContext.TicketTypes
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Get current user ID
        var currentUserId = currentUserService.GetUserId() 
            ?? throw new UnauthorizedAccessException("User must be authenticated to submit price approvals");

        // Check if there's already a pending approval for this ticket type
        var pendingApproval = await dbContext.PriceApprovals
            .AnyAsync(pa => pa.TicketTypeId == request.TicketTypeId && 
                           pa.Status == PriceApprovalStatus.pending, 
                     cancellationToken);

        if (pendingApproval)
        {
            throw new ValidationException("There is already a pending price approval for this ticket type.");
        }

        // Get current prices for comparison
        var currentPrices = await dbContext.TicketPrices
            .Where(tp => tp.TicketTypeId == request.TicketTypeId)
            .ToListAsync(cancellationToken);

        // Create JSON representation of price changes
        var priceChanges = new
        {
            CurrentPrices = currentPrices.Select(p => new
            {
                p.Id,
                p.Price,
                p.EffectiveDateFrom,
                p.EffectiveDateTo,
                p.PricingRuleId
            }),
            request.ProposedPrices
        };
        
        var jsonContent = System.Text.Json.JsonSerializer.Serialize(priceChanges);

        // Create price approval
        var priceApproval = new PriceApproval
        {
            Id = Guid.NewGuid(),
            TicketTypeId = request.TicketTypeId,
            Status = PriceApprovalStatus.pending,
            ProposedChanges = jsonContent,
            RequestComments = request.Comments,
            RequestedByUserId = Guid.Parse(currentUserId),
            RequestedAt = DateTime.UtcNow
        };

        await dbContext.PriceApprovals.AddAsync(priceApproval, cancellationToken);
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        
        if (result <= 0)
        {
            throw new NoEntityCreatedException("PriceApproval");
        }

        return new Response<SubmitPriceApprovalResult>(new SubmitPriceApprovalResult(priceApproval.Id));
    }
}
