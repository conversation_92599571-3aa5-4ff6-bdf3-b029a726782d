namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to delete a ticket price
/// </summary>
public record DeleteTicketPriceCommand(Guid TicketTypeId, Guid PriceId) 
    : ICommand<Response<DeleteTicketPriceResult>>;

/// <summary>
/// Result model for DeleteTicketPriceCommand
/// </summary>
public record DeleteTicketPriceResult(bool Success);

/// <summary>
/// Validator for DeleteTicketPriceCommand
/// </summary>
public class DeleteTicketPriceCommandValidator : AbstractValidator<DeleteTicketPriceCommand>
{
    public DeleteTicketPriceCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");
            
        RuleFor(x => x.PriceId)
            .NotEmpty().WithMessage("Price Id is required.");
    }
}

/// <summary>
/// Handler for DeleteTicketPriceCommand
/// </summary>
public class DeleteTicketPriceCommandHandler(IApplicationDbContext dbContext) 
    : ICommandHandler<DeleteTicketPriceCommand, Response<DeleteTicketPriceResult>>
{
    public async Task<Response<DeleteTicketPriceResult>> Handle(
        DeleteTicketPriceCommand request,
        CancellationToken cancellationToken)
    {
        // Validate ticket type exists
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Find the price to delete
        var price = await dbContext.TicketPrices
            .AsTracking()
            .FirstOrDefaultAsync(p => p.Id == request.PriceId && p.TicketTypeId == request.TicketTypeId, cancellationToken)
            ?? throw new TicketPriceNotFoundException(request.PriceId, request.TicketTypeId);

        // Check if this price is already in use by existing orders/tickets
        var isInUse = await dbContext.OrderItems
            .AnyAsync(oi => oi.TicketTypeId == request.TicketTypeId && oi.UnitPrice == price.Price, cancellationToken);

        if (isInUse)
        {
            throw new ValidationException("Cannot delete this price because it is already being used by existing orders.");
        }

        // Soft delete
        price.IsDeleted = true;

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityDeletedException("TicketPrice", request.PriceId);
        }

        return new Response<DeleteTicketPriceResult>(new DeleteTicketPriceResult(true));
    }
}
