using BuildingBlocks.Exceptions;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to delete (soft delete) a ticket type
/// </summary>
public record DeleteTicketTypeCommand(Guid Id) : ICommand<Response<DeleteTicketTypeResult>>;

/// <summary>
/// Result model for DeleteTicketTypeCommand
/// </summary>
public record DeleteTicketTypeResult(bool Success);

/// <summary>
/// Validator for DeleteTicketTypeCommand
/// </summary>
public class DeleteTicketTypeCommandValidator : AbstractValidator<DeleteTicketTypeCommand>
{
    public DeleteTicketTypeCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Id is required.");
    }
}

/// <summary>
/// Handler for DeleteTicketTypeCommand
/// </summary>
public class DeleteTicketTypeCommandHandler(IApplicationDbContext dbContext)
    : ICommandHandler<DeleteTicketTypeCommand, Response<DeleteTicketTypeResult>>
{
    public async Task<Response<DeleteTicketTypeResult>> Handle(
        DeleteTicketTypeCommand request,
        CancellationToken cancellationToken)
    {
        //do not filter by IsDeleted here, because we are filtering in Configuration
        var ticketType = await dbContext.TicketTypes
            .Include(tt => tt.TicketPrices)
            .Include(tt => tt.Conditions)
            .AsTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.Id, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.Id);

        // Check if there are any tickets associated with this ticket type
        bool hasAssociatedTickets = await dbContext.Tickets
            .AnyAsync(t => t.TicketTypeId == request.Id, cancellationToken);

        if (hasAssociatedTickets)
        {
            throw new ValidationException("Cannot delete ticket type because there are existing tickets associated with it.");
        }

        // Soft delete the ticket type
        ticketType.IsDeleted = true;

        // Remove associated ticket prices and conditions
        if (ticketType.Conditions != null)
        {
            foreach (var condition in ticketType.Conditions)
            {
                condition.IsDeleted = true; // Soft delete conditions
            }
        }

        if (ticketType.TicketPrices != null)
        {
            foreach (var price in ticketType.TicketPrices)
            {
                price.IsDeleted = true; // Soft delete ticket prices
            }
        }
        
        // Save changes to the database
        dbContext.TicketTypes.Update(ticketType);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityDeletedException("TicketType", request.Id);
        }

        return new Response<DeleteTicketTypeResult>(new DeleteTicketTypeResult(true));
    }
}
