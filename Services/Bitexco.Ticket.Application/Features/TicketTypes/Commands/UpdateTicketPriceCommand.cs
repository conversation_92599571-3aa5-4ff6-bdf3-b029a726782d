using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to update an existing ticket price
/// </summary>
public record UpdateTicketPriceCommand(Guid TicketTypeId, Guid PriceId, TicketPriceDto Price) 
    : ICommand<Response<UpdateTicketPriceResult>>;

/// <summary>
/// Result model for UpdateTicketPriceCommand
/// </summary>
public record UpdateTicketPriceResult(bool Success);

/// <summary>
/// Validator for UpdateTicketPriceCommand
/// </summary>
public class UpdateTicketPriceCommandValidator : AbstractValidator<UpdateTicketPriceCommand>
{
    public UpdateTicketPriceCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("Ticket Type Id is required.");
            
        RuleFor(x => x.PriceId)
            .NotEmpty().WithMessage("Price Id is required.");

        RuleFor(x => x.Price.Price)
            .GreaterThanOrEqualTo(0).WithMessage("Price must be greater than or equal to 0.");

        // If EffectiveDateTo is provided, it must be later than EffectiveDateFrom
        When(x => x.Price.EffectiveDateTo.HasValue, () =>
        {
            RuleFor(x => x.Price.EffectiveDateTo!.Value)
                .GreaterThan(x => x.Price.EffectiveDateFrom)
                .WithMessage("Effective end date must be later than the effective start date.");
        });
    }
}

/// <summary>
/// Handler for UpdateTicketPriceCommand
/// </summary>
public class UpdateTicketPriceCommandHandler(IApplicationDbContext dbContext) 
    : ICommandHandler<UpdateTicketPriceCommand, Response<UpdateTicketPriceResult>>
{
    public async Task<Response<UpdateTicketPriceResult>> Handle(
        UpdateTicketPriceCommand request,
        CancellationToken cancellationToken)
    {
        // Check if the ticket type exists
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Find the price to update
        var price = await dbContext.TicketPrices
            .AsTracking()
            .FirstOrDefaultAsync(p => p.Id == request.PriceId && p.TicketTypeId == request.TicketTypeId, cancellationToken)
            ?? throw new TicketPriceNotFoundException(request.PriceId, request.TicketTypeId);

        // Validate pricing rule reference if provided
        if (request.Price.PricingRuleId.HasValue)
        {
            var pricingRuleExists = await dbContext.PricingRules
                .AnyAsync(p => p.Id == request.Price.PricingRuleId.Value, cancellationToken);

            if (!pricingRuleExists)
            {
                throw new PricingRuleNotFoundException(request.Price.PricingRuleId.Value);
            }
        }

        // Update price properties
        price.PricingRuleId = request.Price.PricingRuleId;
        price.Price = request.Price.Price;
        price.EffectiveDateFrom = request.Price.EffectiveDateFrom;
        price.EffectiveDateTo = request.Price.EffectiveDateTo;

        // Update the price in the context
        dbContext.TicketPrices.Update(price);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("TicketPrice", request.PriceId);
        }

        return new Response<UpdateTicketPriceResult>(new UpdateTicketPriceResult(true));
    }
}
