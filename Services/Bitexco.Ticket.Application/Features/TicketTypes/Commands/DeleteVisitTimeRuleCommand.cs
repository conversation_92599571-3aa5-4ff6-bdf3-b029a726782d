namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands
{
    public record DeleteVisitTimeRuleCommand : IRequest<Response<object>>
    {
        public Guid VisitTimeRuleId { get; set; }
        public Guid TicketTypeId { get; set; }
    }

    public class DeleteVisitTimeRuleCommandValidator : AbstractValidator<DeleteVisitTimeRuleCommand>
    {
        public DeleteVisitTimeRuleCommandValidator()
        {
            RuleFor(x => x.VisitTimeRuleId)
                .NotEmpty().WithMessage("VisitTimeRuleId is required.");

            RuleFor(x => x.TicketTypeId)
                .NotEmpty().WithMessage("TicketTypeId is required.");
        }
    }

    public class DeleteVisitTimeRuleCommandHandler(IApplicationDbContext context) : IRequestHandler<DeleteVisitTimeRuleCommand, Response<object>>
    {
        private readonly IApplicationDbContext _context = context;

        public async Task<Response<object>> Handle(DeleteVisitTimeRuleCommand request, CancellationToken cancellationToken)
        {
            var visitTimeRule = await _context.VisitTimeRules
                .AsTracking()
                .FirstOrDefaultAsync(vtr => vtr.Id == request.VisitTimeRuleId && vtr.TicketTypeId == request.TicketTypeId, cancellationToken)
                ?? throw new VisitTimeRuleNotFoundException(request.VisitTimeRuleId, request.TicketTypeId);

            // Soft delete the visit time rule
            visitTimeRule.IsDeleted = true;

            _context.VisitTimeRules.Update(visitTimeRule);
            var result = await _context.SaveChangesAsync(cancellationToken);
            
            if (result <= 0)
            {
                throw new NoEntityDeletedException("VisitTimeRule", request.VisitTimeRuleId);
            }

            return new Response<object>(true);
        }
    }
}
