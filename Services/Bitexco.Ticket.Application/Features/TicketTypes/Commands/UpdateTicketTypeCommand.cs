using Bitexco.Ticket.Application.Features.TicketTypes.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Commands;

/// <summary>
/// Command to update an existing ticket type
/// </summary>
public record UpdateTicketTypeCommand(Guid Id, TicketTypeUpsertDto TicketType) : ICommand<Response<UpdateTicketTypeResult>>;

/// <summary>
/// Result model for UpdateTicketTypeCommand
/// </summary>
public record UpdateTicketTypeResult(bool Success);

/// <summary>
/// Validator for UpdateTicketTypeCommand
/// </summary>
public class UpdateTicketTypeCommandValidator : AbstractValidator<UpdateTicketTypeCommand>
{
    public UpdateTicketTypeCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Id is required.");

        RuleFor(x => x.TicketType.Name)
            .NotEmpty().WithMessage("Name is required.")
            .MaximumLength(100).WithMessage("Name cannot exceed 100 characters.");

        RuleFor(x => x.TicketType.Description)
            .MaximumLength(500).WithMessage("Description cannot exceed 500 characters.")
            .When(x => !string.IsNullOrEmpty(x.TicketType.Description));
        
        RuleFor(x => x.TicketType.TicketCodeLengthAfterPrefix)
            .GreaterThan(0).WithMessage("Ticket code length after prefix must be greater than 0.");
    }
}

/// <summary>
/// Handler for UpdateTicketTypeCommand
/// </summary>
public class UpdateTicketTypeCommandHandler(IApplicationDbContext dbContext)
    : ICommandHandler<UpdateTicketTypeCommand, Response<UpdateTicketTypeResult>>
{
    public async Task<Response<UpdateTicketTypeResult>> Handle(
        UpdateTicketTypeCommand request,
        CancellationToken cancellationToken)
    {
        var ticketType = await dbContext.TicketTypes
            .Include(tt => tt.Conditions)
            .AsTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.Id, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.Id);

        // Update properties
        ticketType.Name = request.TicketType.Name;
        ticketType.Description = request.TicketType.Description;
        ticketType.Icon = request.TicketType.Icon;
        ticketType.DefaultPrice = request.TicketType.DefaultPrice;
        ticketType.TicketCodePrefix = request.TicketType.TicketCodePrefix;
        ticketType.TicketCodeLengthAfterPrefix = request.TicketType.TicketCodeLengthAfterPrefix;
        ticketType.TicketExpirationAfterDurations = request.TicketType.TicketExpirationAfterDurations;
        ticketType.TicketExpirationDurationUnit = request.TicketType.TicketExpirationDurationUnit;
        ticketType.TicketExpirationFromDate = request.TicketType.TicketExpirationFromDate;
        ticketType.TicketExpirationToDate = request.TicketType.TicketExpirationToDate;
        ticketType.Conditions = request.TicketType.Conditions?.Adapt<ICollection<TicketTypeCondition>>();
        ticketType.IsActive = request.TicketType.IsActive;

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("TicketType", request.Id);
        }

        return new Response<UpdateTicketTypeResult>(new UpdateTicketTypeResult(true));
    }
}
