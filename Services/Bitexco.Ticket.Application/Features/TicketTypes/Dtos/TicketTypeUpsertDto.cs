using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Dtos;

/// <summary>
/// Data Transfer Object for creating or updating a Ticket Type
/// </summary>
public record TicketTypeUpsertDto : TicketTypeDto
{
    /// <summary>
    /// Application conditions or restrictions for this ticket type
    /// </summary>
    public ICollection<TicketTypeConditionDto>? Conditions { get; set; }
}
