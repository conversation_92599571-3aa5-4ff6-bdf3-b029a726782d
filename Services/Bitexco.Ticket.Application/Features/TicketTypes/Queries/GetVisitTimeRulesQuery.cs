using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.TicketTypes.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Queries;

/// <summary>
/// Query to retrieve all visit time rules for a specific ticket type
/// </summary>
public record GetVisitTimeRulesQuery(Guid TicketTypeId) : IQuery<Response<List<VisitTimeRuleResponseDto>>>;

/// <summary>
/// Handler for GetVisitTimeRulesQuery
/// </summary>
public class GetVisitTimeRulesQueryHandler(IApplicationDbContext dbContext) 
    : IQueryHandler<GetVisitTimeRulesQuery, Response<List<VisitTimeRuleResponseDto>>>
{
    public async Task<Response<List<VisitTimeRuleResponseDto>>> Handle(
        GetVisitTimeRulesQuery request,
        CancellationToken cancellationToken)
    {
        // Check if ticket type exists
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Get all time rules for the ticket type
        var timeRules = await dbContext.VisitTimeRules
            .AsNoTracking()
            .Where(r => r.TicketTypeId == request.TicketTypeId)
            .ProjectToType<VisitTimeRuleResponseDto>()
            .ToListAsync(cancellationToken);

        return new Response<List<VisitTimeRuleResponseDto>>(timeRules);
    }
}
