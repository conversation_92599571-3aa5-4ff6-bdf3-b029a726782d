using Bitexco.Ticket.Application.Features.TicketTypes.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Queries;

/// <summary>
/// Query to retrieve all prices for a specific ticket type
/// </summary>
public record GetTicketTypePricesQuery(Guid TicketTypeId) : IQuery<Response<List<TicketPriceResponse>>>;

/// <summary>
/// Handler for GetTicketTypePricesQuery
/// </summary>
public class GetTicketTypePricesQueryHandler(IApplicationDbContext dbContext) 
    : IQueryHandler<GetTicketTypePricesQuery, Response<List<TicketPriceResponse>>>
{
    public async Task<Response<List<TicketPriceResponse>>> Handle(
        GetTicketTypePricesQuery request,
        CancellationToken cancellationToken)
    {
        // Check if the ticket type exists
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Get all prices for the ticket type, including pricing rule information
        var prices = await dbContext.TicketPrices
            .AsNoTracking()
            .Where(p => p.TicketTypeId == request.TicketTypeId)
            .OrderByDescending(p => p.EffectiveDateFrom)
            .ThenByDescending(p => p.CreatedAt)
            .Select(p => new TicketPriceResponse
            {
                Id = p.Id,
                TicketTypeId = p.TicketTypeId,
                PricingRuleId = p.PricingRuleId,
                PricingRuleName = p.PricingRule != null ? p.PricingRule.Name : null,
                Price = p.Price,
                EffectiveDateFrom = p.EffectiveDateFrom,
                EffectiveDateTo = p.EffectiveDateTo
            })
            .ToListAsync(cancellationToken);

        return new Response<List<TicketPriceResponse>>(prices);
    }
}
