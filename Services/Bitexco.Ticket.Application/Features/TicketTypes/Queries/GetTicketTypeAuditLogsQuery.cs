using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Queries
{
    /// <summary>
    /// Query to get audit logs for a specific ticket type
    /// </summary>
    public class GetTicketTypeAuditLogsQuery : IRequest<Response<List<AuditLogResponseDto>>>
    {
        /// <summary>
        /// The ticket type ID to retrieve audit logs for
        /// </summary>
        public Guid TicketTypeId { get; set; }
        
        /// <summary>
        /// Optional start date to filter logs
        /// </summary>
        public DateTime? FromDate { get; set; }
        
        /// <summary>
        /// Optional end date to filter logs
        /// </summary>
        public DateTime? ToDate { get; set; }
    }

    /// <summary>
    /// Handler for GetTicketTypeAuditLogsQuery
    /// </summary>
    public class GetTicketTypeAuditLogsQueryHandler(IApplicationDbContext context) : IRequestHandler<GetTicketTypeAuditLogsQuery, Response<List<AuditLogResponseDto>>>
    {
        private readonly IApplicationDbContext _context = context;

        public async Task<Response<List<AuditLogResponseDto>>> Handle(GetTicketTypeAuditLogsQuery request, CancellationToken cancellationToken)
        {
            // Check if ticket type exists
            bool ticketTypeExists = await _context.TicketTypes
                .AnyAsync(tt => tt.Id == request.TicketTypeId, cancellationToken);
                
            if (!ticketTypeExists)
            {
                throw new TicketTypeNotFoundException(request.TicketTypeId);
            }

            // Build query for audit logs related to this ticket type
            var query = _context.AuditLogs
                .Where(log => 
                    // Get logs directly for the ticket type
                    (log.EntityType == "TicketType" && log.EntityId == request.TicketTypeId) ||
                    // Get logs for related entities (prices, conditions, visit time rules)
                    (log.EntityType == "TicketPrice" && _context.TicketPrices.Any(tp => tp.Id == log.EntityId && tp.TicketTypeId == request.TicketTypeId)) ||
                    (log.EntityType == "TicketTypeCondition" && _context.TicketTypeConditions.Any(ttc => ttc.Id == log.EntityId && ttc.TicketTypeId == request.TicketTypeId)) ||
                    (log.EntityType == "VisitTimeRule" && _context.VisitTimeRules.Any(vtr => vtr.Id == log.EntityId && vtr.TicketTypeId == request.TicketTypeId)) ||
                    (log.EntityType == "PriceApproval" && _context.PriceApprovals.Any(pa => pa.Id == log.EntityId && pa.TicketTypeId == request.TicketTypeId))
                );

            // Apply date filters if provided
            if (request.FromDate.HasValue)
            {
                query = query.Where(log => log.Timestamp >= request.FromDate.Value);
            }
            
            if (request.ToDate.HasValue)
            {
                query = query.Where(log => log.Timestamp <= request.ToDate.Value);
            }

            // Order by most recent first
            query = query.OrderByDescending(log => log.Timestamp);

            // Execute query and map to DTOs
            var auditLogs = await query
                .ProjectToType<AuditLogResponseDto>()
                .ToListAsync(cancellationToken);

            return new Response<List<AuditLogResponseDto>>(auditLogs);
        }
    }
}
