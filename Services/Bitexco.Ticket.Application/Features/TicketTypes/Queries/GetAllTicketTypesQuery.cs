using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Queries;

/// <summary>
/// Query to retrieve all ticket types
/// </summary>
public record GetAllTicketTypesQuery : IQuery<Response<List<GetAllTicketTypesResponseDto>>>;

public record GetAllTicketTypesResponseDto : TicketTypeResponseDto
{
    public List<TicketTypeConditionResponseDto> Conditions { get; set; } = [];
}

/// <summary>
/// Handler for GetAllTicketTypesQuery
/// </summary>
public class GetAllTicketTypesQueryHandler(IApplicationDbContext dbContext)
    : IQueryHandler<GetAllTicketTypesQuery, Response<List<GetAllTicketTypesResponseDto>>>
{
    public async Task<Response<List<GetAllTicketTypesResponseDto>>> Handle(
        GetAllTicketTypesQuery request,
        CancellationToken cancellationToken)
    {
        var ticketTypes = await dbContext.TicketTypes
            .AsNoTracking()
            .Include(tt => tt.Conditions)
            .ProjectToType<GetAllTicketTypesResponseDto>()
            .ToListAsync(cancellationToken);

        return new Response<List<GetAllTicketTypesResponseDto>>(ticketTypes);
    }
}
