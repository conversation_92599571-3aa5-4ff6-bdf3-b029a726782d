using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.TicketTypes.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Queries;

/// <summary>
/// Query to retrieve a ticket type by its ID
/// </summary>
public record GetTicketTypeByIdQuery(Guid Id) : IQuery<Response<TicketTypeDetailResponse>>;

/// <summary>
/// Handler for GetTicketTypeByIdQuery
/// </summary>
public class GetTicketTypeByIdQueryHandler(IApplicationDbContext dbContext) 
    : IQueryHandler<GetTicketTypeByIdQuery, Response<TicketTypeDetailResponse>>
{
    public async Task<Response<TicketTypeDetailResponse>> Handle(
        GetTicketTypeByIdQuery request,
        CancellationToken cancellationToken)
    {
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .Include(tt => tt.Conditions)
            .Include(tt => tt.TicketPrices)
            .FirstOrDefaultAsync(tt => tt.Id == request.Id, cancellationToken) ?? throw new TicketTypeNotFoundException(request.Id);

        var result = ticketType.Adapt<TicketTypeDetailResponse>();

        return new Response<TicketTypeDetailResponse>(result);
    }
}
