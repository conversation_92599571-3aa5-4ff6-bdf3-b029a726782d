using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.TicketTypes.Queries;

/// <summary>
/// Query to retrieve all conditions for a specific ticket type
/// </summary>
public record GetTicketTypeConditionsQuery(Guid TicketTypeId) : IQuery<Response<List<TicketTypeConditionResponseDto>>>;

/// <summary>
/// Handler for GetTicketTypeConditionsQuery
/// </summary>
public class GetTicketTypeConditionsQueryHandler(IApplicationDbContext dbContext)
    : IQueryHandler<GetTicketTypeConditionsQuery, Response<List<TicketTypeConditionResponseDto>>>
{
    public async Task<Response<List<TicketTypeConditionResponseDto>>> Handle(
        GetTicketTypeConditionsQuery request,
        CancellationToken cancellationToken)
    {
        // Check if the ticket type exists
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Get all conditions for the ticket type
        var conditions = await dbContext.TicketTypeConditions
            .AsNoTracking()
            .Where(c => c.TicketTypeId == request.TicketTypeId)
            .OrderBy(c => c.DisplayOrder)
            .ProjectToType<TicketTypeConditionResponseDto>()
            .ToListAsync(cancellationToken);

        return new Response<List<TicketTypeConditionResponseDto>>(conditions);
    }
}
