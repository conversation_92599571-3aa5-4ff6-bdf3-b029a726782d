using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Exceptions;
using Bitexco.Ticket.Domain.Models;

namespace Bitexco.Ticket.Application.Features.RetailChannels.CreateRetailChannel;

/// <summary>
/// Command to create a new retail channel
/// </summary>
public record CreateRetailChannelCommand : IRequest<Response<CreateRetailChannelResult>>
{
    public RetailChannelDto RetailChannel { get; set; } = null!;
}

public record CreateRetailChannelResult(Guid Id);

/// <summary>
/// Handler for CreateRetailChannelCommand
/// </summary>
public class CreateRetailChannelCommandHandler(IApplicationDbContext context) : IRequestHandler<CreateRetailChannelCommand, Response<CreateRetailChannelResult>>
{
    public async Task<Response<CreateRetailChannelResult>> Handle(CreateRetailChannelCommand request, CancellationToken cancellationToken)
    {
        // Check if retail channel with same name already exists
        var existingByName = await context.RetailChannels
            .AsNoTracking()
            .AnyAsync(r => r.Name == request.RetailChannel.Name, cancellationToken);
        
        if (existingByName)
        {
            throw new DuplicatedDataException("Name");
        }

        var retailChannel = request.RetailChannel.Adapt<RetailChannel>();
        
        context.RetailChannels.Add(retailChannel);
        var result = await context.SaveChangesAsync(cancellationToken);
        
        if (result == 0)
        {
            throw new NoEntityCreatedException("RetailChannel");
        }

        return new Response<CreateRetailChannelResult>(new CreateRetailChannelResult(retailChannel.Id));
    }
}