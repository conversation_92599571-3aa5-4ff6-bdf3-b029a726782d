namespace Bitexco.Ticket.Application.Features.RetailChannels.CreateRetailChannel;

/// <summary>
/// Validator for CreateRetailChannelCommand
/// </summary>
public class CreateRetailChannelCommandValidator : AbstractValidator<CreateRetailChannelCommand>
{
    public CreateRetailChannelCommandValidator()
    {
        RuleFor(x => x.RetailChannel)
            .NotNull().WithMessage("Retail channel data is required");

        RuleFor(x => x.RetailChannel.Name)
            .NotEmpty().WithMessage("Retail channel name is required")
            .MaximumLength(200).WithMessage("Retail channel name cannot exceed 200 characters");
    }
}