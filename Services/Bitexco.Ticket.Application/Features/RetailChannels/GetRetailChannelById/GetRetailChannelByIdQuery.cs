using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Exceptions;

namespace Bitexco.Ticket.Application.Features.RetailChannels.GetRetailChannelById;

/// <summary>
/// Query to get retail channel by ID
/// </summary>
public record GetRetailChannelByIdQuery : IRequest<Response<RetailChannelResponseDto>>
{
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for GetRetailChannelByIdQuery
/// </summary>
public class GetRetailChannelByIdQueryHandler(IApplicationDbContext context) : IRequestHandler<GetRetailChannelByIdQuery, Response<RetailChannelResponseDto>>
{
    public async Task<Response<RetailChannelResponseDto>> Handle(GetRetailChannelByIdQuery request, CancellationToken cancellationToken)
    {
        var retailChannel = await context.RetailChannels
            .Where(r => r.Id == request.Id)
            .AsNoTracking()
            .ProjectToType<RetailChannelResponseDto>()
            .FirstOrDefaultAsync(cancellationToken);

        if (retailChannel == null)
        {
            throw new RetailChannelNotFoundException(request.Id);
        }

        return new Response<RetailChannelResponseDto>(retailChannel);
    }
}