using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.RetailChannels.GetRetailChannels;

/// <summary>
/// Query to get all retail channels
/// </summary>
public class GetRetailChannelsQuery : IRequest<Response<List<RetailChannelResponseDto>>>
{
    // No parameters - just get all retail channels
}

/// <summary>
/// Handler for GetRetailChannelsQuery
/// </summary>
public class GetRetailChannelsQueryHandler(IApplicationDbContext context) : IRequestHandler<GetRetailChannelsQuery, Response<List<RetailChannelResponseDto>>>
{
    public async Task<Response<List<RetailChannelResponseDto>>> Handle(GetRetailChannelsQuery request, CancellationToken cancellationToken)
    {
        var retailers = await context.RetailChannels
            .AsNoTracking()
            .OrderBy(r => r.Name)
            .ProjectToType<RetailChannelResponseDto>()
            .ToListAsync(cancellationToken);

        return new Response<List<RetailChannelResponseDto>>(retailers);
    }
}