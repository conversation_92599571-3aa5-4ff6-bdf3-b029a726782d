using Bitexco.Ticket.Application.Exceptions;

namespace Bitexco.Ticket.Application.Features.RetailChannels.DeleteRetailChannel;

/// <summary>
/// Command to delete a retail channel (soft delete)
/// </summary>
public record DeleteRetailChannelCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
}

/// <summary>
/// Handler for DeleteRetailChannelCommand
/// </summary>
public class DeleteRetailChannelCommandHandler(IApplicationDbContext context) : IRequestHandler<DeleteRetailChannelCommand, Response<object>>
{
    public async Task<Response<object>> Handle(DeleteRetailChannelCommand request, CancellationToken cancellationToken)
    {
        var existingRetailChannel = await context.RetailChannels
            .Where(r => r.Id == request.Id)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingRetailChannel == null)
        {
            throw new RetailChannelNotFoundException(request.Id);
        }

        // Check if retail channel is being used in active orders
        var hasActiveOrders = await context.Orders
            .AsNoTracking()
            .AnyAsync(o => o.RetailChannelId == request.Id, cancellationToken);

        if (hasActiveOrders)
        {
            throw new RetailChannelInUseException(request.Id);
        }

        // Check if retail channel is being used in voucher channels
        var hasVoucherChannels = await context.VoucherChannels
            .AsNoTracking()
            .AnyAsync(vc => vc.ChannelId == request.Id, cancellationToken);

        if (hasVoucherChannels)
        {
            throw new RetailChannelInUseException(request.Id);
        }

        // Perform soft delete
        existingRetailChannel.IsDeleted = true;
        context.RetailChannels.Update(existingRetailChannel);
        var result = await context.SaveChangesAsync(cancellationToken);

        if (result == 0)
        {
            throw new NoEntityDeletedException("RetailChannel", request.Id);
        }

        return new Response<object>(new { status = true });
    }
}