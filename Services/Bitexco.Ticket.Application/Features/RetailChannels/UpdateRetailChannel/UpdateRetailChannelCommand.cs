using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Exceptions;

namespace Bitexco.Ticket.Application.Features.RetailChannels.UpdateRetailChannel;

/// <summary>
/// Command to update an existing retail channel
/// </summary>
public record UpdateRetailChannelCommand : IRequest<Response<UpdateRetailChannelResult>>
{
    public Guid Id { get; set; }
    public RetailChannelDto RetailChannel { get; set; } = null!;
}

public record UpdateRetailChannelResult(bool Status);

/// <summary>
/// Handler for UpdateRetailChannelCommand
/// </summary>
public class UpdateRetailChannelCommandHandler(IApplicationDbContext context) : IRequestHandler<UpdateRetailChannelCommand, Response<UpdateRetailChannelResult>>
{
    public async Task<Response<UpdateRetailChannelResult>> Handle(UpdateRetailChannelCommand request, CancellationToken cancellationToken)
    {
        var existingRetailChannel = await context.RetailChannels
            .Where(r => r.Id == request.Id)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingRetailChannel == null)
        {
            throw new RetailChannelNotFoundException(request.Id);
        }

        // Check if another retail channel with same name already exists (excluding current one)
        var duplicateByName = await context.RetailChannels
            .AsNoTracking()
            .AnyAsync(r => r.Name == request.RetailChannel.Name && r.Id != request.Id, cancellationToken);

        if (duplicateByName)
        {
            throw new DuplicatedDataException("Name");
        }

        // Update fields
        existingRetailChannel.Name = request.RetailChannel.Name;
        existingRetailChannel.IsActive = request.RetailChannel.IsActive;

        context.RetailChannels.Update(existingRetailChannel);
        var result = await context.SaveChangesAsync(cancellationToken);

        if (result == 0)
        {
            throw new NoEntityUpdatedException("RetailChannel", request.Id);
        }

        return new Response<UpdateRetailChannelResult>(new UpdateRetailChannelResult(true));
    }
}