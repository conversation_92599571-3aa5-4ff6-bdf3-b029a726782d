namespace Bitexco.Ticket.Application.Features.RetailChannels.UpdateRetailChannel;

/// <summary>
/// Validator for UpdateRetailChannelCommand
/// </summary>
public class UpdateRetailChannelCommandValidator : AbstractValidator<UpdateRetailChannelCommand>
{
    public UpdateRetailChannelCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Retail channel ID is required");

        RuleFor(x => x.RetailChannel)
            .NotNull().WithMessage("Retail channel data is required");

        RuleFor(x => x.RetailChannel.Name)
            .NotEmpty().WithMessage("Retail channel name is required")
            .MaximumLength(200).WithMessage("Retail channel name cannot exceed 200 characters");
    }
}