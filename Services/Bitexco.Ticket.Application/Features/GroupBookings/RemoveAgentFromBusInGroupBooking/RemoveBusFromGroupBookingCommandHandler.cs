namespace Bitexco.Ticket.Application.Features.GroupBookings.RemoveAgentFromBusInGroupBooking;

public class RemoveAgentFromBusInGroupBookingCommandHandler(IApplicationDbContext dbContext) 
    : IRequestHandler<RemoveAgentFromBusInGroupBookingCommand, Response<object>>
{
    public async Task<Response<object>> Handle(RemoveAgentFromBusInGroupBookingCommand request, CancellationToken cancellationToken)
    {
        var groupBooking = await dbContext.GroupBookings
            .Include(x => x.GroupBookingBuses)
            .AsTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken)
            ?? throw new GroupBookingNotFoundException(request.Id);

        if (groupBooking.GroupBookingBuses == null || groupBooking.GroupBookingBuses.Count == 0)
        {
            throw new NoEntityDeletedException("GroupBookingBuses", request.Id);
        }

        var busHasAgentToRemove = groupBooking.GroupBookingBuses
            .FirstOrDefault(x => x.Id == request.BusId)
            ?? throw new NoEntityDeletedException("GroupBookingBuses", request.Id);
        
        if (!busHasAgentToRemove.AgentIds.Contains(request.AgentId))
        {
            throw new NoEntityDeletedException("GroupBookingBuses", request.AgentId);
        }

        busHasAgentToRemove.AgentIds.Remove(request.AgentId);
        
        if (busHasAgentToRemove.AgentIds.Count == 0)
        {
            busHasAgentToRemove.IsDeleted = true;
        }

        dbContext.GroupBookings.Update(groupBooking);
        var saveResult = await dbContext.SaveChangesAsync(cancellationToken);
        if (saveResult <= 0)
        {
            throw new NoEntityDeletedException("GroupBookingBuses", request.Id);
        }

        return new Response<object>(true);
    }
}