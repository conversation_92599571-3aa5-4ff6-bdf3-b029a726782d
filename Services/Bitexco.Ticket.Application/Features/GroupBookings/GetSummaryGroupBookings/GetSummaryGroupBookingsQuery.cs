using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.GroupBookings.GetSummaryGroupBookings;

/// <summary>
/// Command to get summary of group bookings (POS)
/// </summary>
public record GetSummaryGroupBookingsQuery : IRequest<Response<List<GetSummaryGroupBookingResult>>>
{
    public DateOnly? VisitDate { get; set; }
    public GroupBookingType? Type { get; set; }
}

public record GetSummaryGroupBookingResult : GroupBookingResponseDto
{
    public string AgentCode { get; set; } = string.Empty;
    public string AgentName { get; set; } = string.Empty;
    public List<GetSummaryGroupBookingBusDetailDto> GroupBookingBuses { get; set; } = [];
}

public record GetSummaryGroupBookingBusDetailDto
{
    public Guid Id { get; set; }
    public string Number { get; set; } = string.Empty;
    public List<AgentResponseDto>? Agents { get; set; }
}
