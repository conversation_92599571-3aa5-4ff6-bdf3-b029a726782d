using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.GroupBookings.GetSummaryGroupBookings;

public class GetSummaryGroupBookingQueryHandler(IApplicationDbContext dbContext) : IRequestHandler<GetSummaryGroupBookingsQuery, Response<List<GetSummaryGroupBookingResult>>>
{
    public async Task<Response<List<GetSummaryGroupBookingResult>>> Handle(GetSummaryGroupBookingsQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.GroupBookings
            .AsNoTracking()
            .Include(x => x.Agent)
            .Include(x => x.GroupBookingBuses)
            .Where(x => x.Status == GroupBookingStatus.waiting_for_check_in)
            .AsQueryable();

        if (request.VisitDate.HasValue)
        {
            query = query.Where(x => x.VisitDate == request.VisitDate.Value);
        }

        if (request.Type.HasValue)
        {
            query = query.Where(x => x.Type == request.Type.Value);
        }

        var agents = await dbContext.Agents
                .AsNoTracking()
                .ToListAsync(cancellationToken);

        var groupBookingsEntities = await query
            .OrderBy(x => x.CreatedAt)
            .ToListAsync(cancellationToken);

        var groupBookings = groupBookingsEntities.Select(x => new GetSummaryGroupBookingResult
            {
                Id = x.Id,
                Code = x.Code,
                AgentId = x.AgentId,
                AgentCode = x.Agent.Code,
                AgentName = x.Agent.Name,
                Type = x.Type,
                VisitDate = x.VisitDate,
                VisitTime = x.VisitTime,
                DepositAmount = x.DepositAmount,
                PaymentMethod = x.PaymentMethod,
                Status = x.Status,
                CheckedInAt = x.CheckedInAt,
                CreatedAt = x.CreatedAt,
                UpdatedAt = x.UpdatedAt,
                GroupBookingBuses = [.. x.GroupBookingBuses.Select(bus => new GetSummaryGroupBookingBusDetailDto
                {
                    Id = bus.Id,
                    Number = bus.Number,
                    Agents = [.. agents.Where(a => bus.AgentIds.Contains(a.Id))
                        .Select(a => new AgentResponseDto
                        {
                            Id = a.Id,
                            Code = a.Code,
                            Name = a.Name,
                            SapCode = a.SapCode
                        })]
                })]
        }).ToList();

        return new Response<List<GetSummaryGroupBookingResult>>(groupBookings);
    }
}