using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookingById;

namespace Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookingBuses;

public class GetGroupBookingBusesQueryHandler(IApplicationDbContext dbContext)
    : IRequestHandler<GetGroupBookingBusesQuery, Response<List<GetGroupBookingBusDetailDto>>>
{
    public async Task<Response<List<GetGroupBookingBusDetailDto>>> Handle(GetGroupBookingBusesQuery request, CancellationToken cancellationToken)
    {
        var groupBookingBus = await dbContext.GroupBookingBuses
            .Where(x => x.GroupBookingId == request.Id)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        var groupBookingBusResults = groupBookingBus.Adapt<List<GetGroupBookingBusDetailDto>>();

        var agenIds = groupBookingBusResults.SelectMany(x => x.AgentIds).Distinct();
        var agents = await dbContext.Agents
            .AsNoTracking()
            .Where(x => agenIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        foreach (var bus in groupBookingBusResults)
        {
            bus.Agents = agents.Where(x => bus.AgentIds.Contains(x.Id)).Adapt<List<AgentResponseDto>>();
        }

        return new Response<List<GetGroupBookingBusDetailDto>>(groupBookingBusResults);
    }
}