using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookings;

/// <summary>
/// Command to create a new group booking
/// </summary>
public record GetGroupBookingQuery
    : IRequest<PaginationResponse<GetGroupBookingResult>>, IPaginationRequest, ISortRequest
{
    public string? AgentQuery { get; set; }
    public GroupBookingStatus? Status { get; set; }
    public DateOnly? VisitDate { get; set; }
    public GroupBookingType? Type { get; set; }
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 100;
    public string? SortBy { get; set; }
    public bool? IsDescending { get; set; }
}

public record GetGroupBookingResult : GroupBookingResponseDto
{
    public string AgentCode { get; set; } = string.Empty;
    public string AgentName { get; set; } = string.Empty;
}
