namespace Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookings;

public class GetGroupBookingQueryHandler(IApplicationDbContext dbContext) : IRequestHandler<GetGroupBookingQuery, PaginationResponse<GetGroupBookingResult>>
{
    public async Task<PaginationResponse<GetGroupBookingResult>> Handle(GetGroupBookingQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.GroupBookings
            .AsNoTracking()
            .Include(x => x.Agent)
            .AsQueryable();

        if (!string.IsNullOrEmpty(request.AgentQuery))
        {
            query = query.Where(x => x.Agent.Name.ToLower().Contains(request.AgentQuery.ToLower())
                || x.Agent.Code.ToLower().Contains(request.AgentQuery.ToLower())
                || (!string.IsNullOrEmpty(x.Agent.SapCode) && x.Agent.SapCode.ToLower().Contains(request.AgentQuery.ToLower())));
        }

        if (request.Status.HasValue)
        {
            query = query.Where(x => x.Status == request.Status.Value);
        }

        if (request.VisitDate.HasValue)
        {
            query = query.Where(x => x.VisitDate == request.VisitDate.Value);
        }

        if (request.Type.HasValue)
        {
            query = query.Where(x => x.Type == request.Type.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        //order by
        query = (request.SortBy?.ToLower()) switch
        {
            "visitdate" => request.IsDescending == true ? query.OrderByDescending(x => x.VisitDate) : query.OrderBy(x => x.VisitDate),
            "agentcode" => request.IsDescending == true ? query.OrderByDescending(x => x.Agent.Code) : query.OrderBy(x => x.Agent.Code),
            "depositamount" => request.IsDescending == true ? query.OrderByDescending(x => x.DepositAmount) : query.OrderBy(x => x.DepositAmount),
            "status" => request.IsDescending == true ? query.OrderByDescending(x => x.Status) : query.OrderBy(x => x.Status),
            _ => request.IsDescending == true ? query.OrderByDescending(x => x.CreatedAt) : query.OrderBy(x => x.CreatedAt),
        };

        var groupBookings = await query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new GetGroupBookingResult
            {
                Id = x.Id,
                Code = x.Code,
                AgentId = x.AgentId,
                AgentCode = x.Agent.Code,
                AgentName = x.Agent.Name,
                Type = x.Type,
                VisitDate = x.VisitDate,
                VisitTime = x.VisitTime,
                DepositAmount = x.DepositAmount,
                Status = x.Status,
                CheckedInAt = x.CheckedInAt,
                CreatedAt = x.CreatedAt,
                UpdatedAt = x.UpdatedAt
            })
            .ToListAsync(cancellationToken);

        return new PaginationResponse<GetGroupBookingResult>(
            request.PageIndex,
            request.PageSize,
            totalCount,
            groupBookings
        );
    }
}