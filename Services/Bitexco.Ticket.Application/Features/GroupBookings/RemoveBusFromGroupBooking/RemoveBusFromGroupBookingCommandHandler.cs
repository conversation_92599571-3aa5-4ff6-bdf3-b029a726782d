namespace Bitexco.Ticket.Application.Features.GroupBookings.RemoveBusFromGroupBooking;

public class AddBusToGroupBookingCommandHandler(IApplicationDbContext dbContext) 
    : IRequestHandler<RemoveBusFromGroupBookingCommand, Response<object>>
{
    public async Task<Response<object>> Handle(RemoveBusFromGroupBookingCommand request, CancellationToken cancellationToken)
    {
        var groupBooking = await dbContext.GroupBookings
            .Include(x => x.GroupBookingBuses)
            .AsTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken)
            ?? throw new GroupBookingNotFoundException(request.Id);

        if (groupBooking.GroupBookingBuses == null || groupBooking.GroupBookingBuses.Count == 0)
        {
            throw new NoEntityDeletedException("GroupBookingBuses", request.Id);
        }

        var busToRemove = groupBooking.GroupBookingBuses
            .FirstOrDefault(x => x.Id == request.BusId)
            ?? throw new NoEntityDeletedException("GroupBookingBuses", request.BusId);
        busToRemove.IsDeleted = true;

        dbContext.GroupBookings.Update(groupBooking);
        var saveResult = await dbContext.SaveChangesAsync(cancellationToken);
        if (saveResult <= 0)
        {
            throw new NoEntityDeletedException("GroupBookingBuses", request.Id);
        }

        return new Response<object>(true);
    }
}