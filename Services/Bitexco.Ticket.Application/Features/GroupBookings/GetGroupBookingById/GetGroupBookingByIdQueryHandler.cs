using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookingById;

public class GetGroupBookingByIdQueryHandler(IApplicationDbContext dbContext) : IRequestHandler<GetGroupBookingByIdQuery, Response<GetGroupBookingByIdResult>>
{
    public async Task<Response<GetGroupBookingByIdResult>> Handle(GetGroupBookingByIdQuery request, CancellationToken cancellationToken)
    {
        var groupBooking = await dbContext.GroupBookings
            .AsNoTracking()
            .Include(x => x.Agent)
            .Include(x => x.GroupBookingBuses)
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken) ?? throw new GroupBookingNotFoundException(request.Id);

        var result = new GetGroupBookingByIdResult
        {
            Id = groupBooking.Id,
            Code = groupBooking.Code,
            AgentId = groupBooking.AgentId,
            AgentCode = groupBooking.Agent.Code,
            AgentName = groupBooking.Agent.Name,
            IsDirectDiscount = groupBooking.Agent.IsDirectDiscount,
            Type = groupBooking.Type,
            VisitDate = groupBooking.VisitDate,
            VisitTime = groupBooking.VisitTime,
            DepositAmount = groupBooking.DepositAmount,
            Status = groupBooking.Status,
            CheckedInAt = groupBooking.CheckedInAt,
            CreatedAt = groupBooking.CreatedAt,
            UpdatedAt = groupBooking.UpdatedAt,
            GroupBookingBuses = groupBooking.GroupBookingBuses.Adapt<List<GetGroupBookingBusDetailDto>>()
        };

        if (result.GroupBookingBuses.Count > 0)
        {
            var agenIds = result.GroupBookingBuses.SelectMany(x => x.AgentIds).Distinct();
            var agents = await dbContext.Agents
                .AsNoTracking()
                .Where(x => agenIds.Contains(x.Id))
                .ToListAsync(cancellationToken);

            foreach (var groupBookingBus in result.GroupBookingBuses)
            {
                groupBookingBus.Agents = agents.Where(x => groupBookingBus.AgentIds.Contains(x.Id)).Adapt<List<AgentResponseDto>>();
            }
        }

        return new Response<GetGroupBookingByIdResult>(result);
    }
}