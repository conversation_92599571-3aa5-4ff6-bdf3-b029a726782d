using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.GroupBookings.GetGroupBookingById;

/// <summary>
/// Command to create a new group booking
/// </summary>
public record GetGroupBookingByIdQuery : IRequest<Response<GetGroupBookingByIdResult>>
{
    public Guid Id { get; set; }
}

public record GetGroupBookingByIdResult : GroupBookingResponseDto
{
    public string AgentCode { get; set; } = string.Empty;
    public string AgentName { get; set; } = string.Empty;
    public bool IsDirectDiscount { get; set; }

    public List<GetGroupBookingBusDetailDto> GroupBookingBuses { get; set; } = [];
}

public record GetGroupBookingBusDetailDto : GroupBookingBusResponseDto
{
    public List<AgentResponseDto>? Agents { get; set; }
}
