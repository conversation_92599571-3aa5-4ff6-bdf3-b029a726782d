using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.GroupBookings.CreateGroupBooking;

/// <summary>
/// Command to create a new group booking
/// </summary>
public record CreateGroupBookingCommand : IRequest<Response<CreateGroupBookingResult>>
{
    public Guid AgentId { get; set; }
    public GroupBookingType Type { get; set; }
    public PaymentTransactionMethod PaymentMethod { get; set; }
    public DateOnly VisitDate { get; set; }
    public TimeOnly VisitTime { get; set; }
    public decimal DepositAmount { get; set; } = 0;

    public List<CreateGroupBookingBus>? Buses { get; set; }
}

public record CreateGroupBookingBus
{
    public string Number { get; set; } = string.Empty;
    public List<Guid> AgentIds { get; set; } = [];
}

public record CreateGroupBookingResult(Guid Id);
