namespace Bitexco.Ticket.Application.Features.GroupBookings.CreateGroupBooking;

/// <summary>
/// Validator for CreateGroupBookingCommand
/// </summary>
public class CreateGroupBookingCommandValidator : AbstractValidator<CreateGroupBookingCommand>
{
    public CreateGroupBookingCommandValidator()
    {
        RuleFor(x => x.AgentId)
            .NotEmpty().WithMessage("Agent ID is required");

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("Invalid group booking type");

        RuleFor(x => x.VisitDate)
            .NotEmpty().WithMessage("Visit date is required");

        RuleFor(x => x.VisitTime)
            .NotEmpty().WithMessage("Visit time is required");

        RuleFor(x => x.DepositAmount)
            .GreaterThanOrEqualTo(0).WithMessage("Deposit amount must be positive");
    }
}