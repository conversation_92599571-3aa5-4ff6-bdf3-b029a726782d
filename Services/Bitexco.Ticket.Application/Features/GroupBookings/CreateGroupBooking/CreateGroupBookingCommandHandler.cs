using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Utils;

namespace Bitexco.Ticket.Application.Features.GroupBookings.CreateGroupBooking;

public class CreateGroupBookingCommandHandler(IApplicationDbContext dbContext) : IRequestHandler<CreateGroupBookingCommand, Response<CreateGroupBookingResult>>
{
    public async Task<Response<CreateGroupBookingResult>> Handle(CreateGroupBookingCommand request, CancellationToken cancellationToken)
    {
        var groupBooking = request.Adapt<GroupBooking>();

        //get agent from db
        var agent = await dbContext.Agents.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == request.AgentId, cancellationToken) ?? throw new AgentNotFoundException(request.AgentId);

        if(request.PaymentMethod == PaymentTransactionMethod.debt && !agent.IsDebtAllowed)
        {
            throw new AgentNotAllowedForDebtPaymentException(agent.Code);
        }

        // validate buses if provided
        if (request.Buses != null && request.Buses.Count > 0)
        {
            // Validate each bus' agents exist
            var busAgentIds = request.Buses.SelectMany(b => b.AgentIds).Distinct().ToList();
            var existingAgents = await dbContext.Agents.AsNoTracking()
                .Where(a => busAgentIds.Contains(a.Id))
                .Select(a => a.Id)
                .ToListAsync(cancellationToken);

            foreach (var bus in request.Buses)
            {
                if (bus.AgentIds.Any(id => !existingAgents.Contains(id)))
                {
                    throw new AgentNotFoundException();
                }
            }

            groupBooking.GroupBookingBuses = request.Buses.Adapt<List<GroupBookingBus>>();
        }

        groupBooking.Status = GroupBookingStatus.waiting_for_check_in;
        groupBooking.Code = $"GB-{CommonUtils.GenerateNewCode(7)}";

        await dbContext.GroupBookings.AddAsync(groupBooking, cancellationToken);
        var saveResult = await dbContext.SaveChangesAsync(cancellationToken);
        if (saveResult <= 0)
        {
            throw new NoEntityCreatedException("GroupBooking");
        }

        return new Response<CreateGroupBookingResult>(new CreateGroupBookingResult(groupBooking.Id));
    }
}