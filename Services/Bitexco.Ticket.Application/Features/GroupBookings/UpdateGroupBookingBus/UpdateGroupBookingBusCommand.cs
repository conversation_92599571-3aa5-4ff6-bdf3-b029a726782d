using Bitexco.Ticket.Application.Features.GroupBookings.CreateGroupBooking;

namespace Bitexco.Ticket.Application.Features.GroupBookings.UpdateGroupBookingBus;

/// <summary>
/// Command to create a bus for a group booking
/// </summary>
public record UpdateGroupBookingBusCommand : IRequest<Response<object>>
{
    public Guid Id { get; set; }
    public Guid BusId { get; set; }
    public CreateGroupBookingBus Bus { get; set; } = null!;
}