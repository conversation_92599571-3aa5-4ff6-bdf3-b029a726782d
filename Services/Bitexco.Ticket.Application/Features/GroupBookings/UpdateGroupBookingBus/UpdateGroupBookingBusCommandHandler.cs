namespace Bitexco.Ticket.Application.Features.GroupBookings.UpdateGroupBookingBus;

public class UpdateGroupBookingBusCommandHandler(IApplicationDbContext dbContext) 
    : IRequestHandler<UpdateGroupBookingBusCommand, Response<object>>
{
    public async Task<Response<object>> Handle(UpdateGroupBookingBusCommand request, CancellationToken cancellationToken)
    {
        var groupBooking = await dbContext.GroupBookings
            .Include(x => x.GroupBookingBuses)
            .AsTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken)
            ?? throw new GroupBookingNotFoundException(request.Id);

        if (groupBooking.GroupBookingBuses == null)
        {
            throw new NoEntityUpdatedException("GroupBookingBuses", request.Id);
        }
        
        var bus = groupBooking.GroupBookingBuses
            .FirstOrDefault(x => x.Id == request.BusId)
            ?? throw new GroupBookingBusNotFoundException(request.BusId);

        bus.Number = request.Bus.Number;
        bus.AgentIds = request.Bus.AgentIds;
        dbContext.GroupBookings.Update(groupBooking);

        var saveResult = await dbContext.SaveChangesAsync(cancellationToken);
        if (saveResult <= 0)
        {
            throw new NoEntityUpdatedException("GroupBookingBuses", request.Id);
        }

        return new Response<object>(true);
    }
}