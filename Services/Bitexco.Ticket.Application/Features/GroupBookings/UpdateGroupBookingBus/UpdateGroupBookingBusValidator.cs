namespace Bitexco.Ticket.Application.Features.GroupBookings.UpdateGroupBookingBus;

/// <summary>
/// Validator for UpdateGroupBookingBus
/// </summary>
public class UpdateGroupBookingBusValidator : AbstractValidator<UpdateGroupBookingBusCommand>
{
    public UpdateGroupBookingBusValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Group booking ID is required");

        RuleFor(x => x.BusId)
            .NotEmpty().WithMessage("Bus ID is required");

        RuleFor(x => x.Bus)
            .NotNull().WithMessage("Bus information is required");
    }
}