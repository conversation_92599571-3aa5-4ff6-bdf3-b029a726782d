﻿using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.FileServices;
using System.ComponentModel.DataAnnotations;

namespace Bitexco.Ticket.Application.Features.GroupBookings.ExportGroupBookings
{
    public class ExportGroupBookingCommand : ICommand<Response<ExportGroupBookingResponse>>
    {
        public GroupBookingType GroupBookingType { get; set; } = GroupBookingType.corporate; // Default to corporate booking type
        public GroupBookingStatus? GroupBookingStatus { get; set; }

        /// <summary>
        /// Date when the visit is scheduled
        /// </summary>
        public DateOnly? VisitDate { get; set; }
    }

    public class ExportGroupBookingResponse
    {
        public string? File { get; set; }
    }

    public class ExportGroupBookingData
    {
        [Display(Name = "STT")]
        public int STT { get; set; }

        [Display(Name = "Mã kế hoạch")]
        public string? BookingCode { get; set; }

        [Display(Name = "Ngày dự kiến")]
        public string? VisitDateTime { get; set; }

        [Display(Name = "Mã đại lý")]
        public string? AgentCode { get; set; }

        [Display(Name = "Tên đại lý")]
        public string? AgentName { get; set; }

        [Display(Name = "Giá trị")]
        public string? EstimatedFinalAmount { get; set; }

        [Display(Name = "Trạng thái")]
        public string? Status { get; set; }
    }

    public class ExportGroupBookingCommandHandler(IApplicationDbContext applicationDbContext)
        : ICommandHandler<ExportGroupBookingCommand, Response<ExportGroupBookingResponse>>
    {
        private readonly IApplicationDbContext _applicationDbContext = applicationDbContext;

        public async Task<Response<ExportGroupBookingResponse>> Handle(ExportGroupBookingCommand request, CancellationToken cancellationToken)
        {
            var query = _applicationDbContext.GroupBookings.
                Include(x => x.Agent).AsNoTracking().
                AsQueryable();
            
            query = query.Where(x => x.Type == request.GroupBookingType);

            if (request.GroupBookingStatus.HasValue)
            {
                query = query.Where(x => x.Status == request.GroupBookingStatus.Value);
            }
            if (request.VisitDate.HasValue)
            {
                query = query.Where(x => x.VisitDate == request.VisitDate.Value);
            }
          

            var groupBookings = await query.ToListAsync();
            int i = 1;
            var exportData = groupBookings.Select(x => new ExportGroupBookingData
            {
                STT = i++, // This will be set later
                BookingCode = x.Code,
                VisitDateTime = $"{x.VisitTime:hh\\:mm} {x.VisitDate:dd/MM/yyyy}",
                AgentCode = x.Agent.Code,
                AgentName = x.Agent.Name,
                EstimatedFinalAmount = x.DepositAmount.ToString("C"),
                Status = CastStatus(x.Status)
            }).ToList();

            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "Bookings", "BookingExportFile.xlsx");

            string path = "exports/bookings";
            string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);
            string fileName = $"BookingExport_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
            string filefolder = Path.Combine(folder, fileName);

            ExcelServices.ExportData(exportData, templatePath, filefolder, 2);
            return new Response<ExportGroupBookingResponse>
            {
                Data = new ExportGroupBookingResponse { File = path + "/" + fileName }
            };
        }

        private string CastStatus(GroupBookingStatus status)
        {
            return status switch
            {
                GroupBookingStatus.draft => "Nháp",
                GroupBookingStatus.waiting_for_check_in => "Đã kế hoạch",
                GroupBookingStatus.checked_in => "Đã hoàn thành",
                GroupBookingStatus.cancelled => "Đã hủy",
                _ => "Không xác định"
            };
        }
    }
}