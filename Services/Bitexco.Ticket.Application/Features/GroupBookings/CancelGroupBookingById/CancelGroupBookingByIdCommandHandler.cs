using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.Utils;

namespace Bitexco.Ticket.Application.Features.GroupBookings.CancelGroupBookingById;

public class CancelGroupBookingByIdCommandHandler(IApplicationDbContext dbContext) : IRequestHandler<CancelGroupBookingByIdCommand, Response<object>>
{
    public async Task<Response<object>> Handle(CancelGroupBookingByIdCommand request, CancellationToken cancellationToken)
    {
        var groupBooking = await dbContext.GroupBookings
            .AsTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken) ?? throw new GroupBookingNotFoundException(request.Id);

        if(groupBooking.Status != GroupBookingStatus.draft && groupBooking.Status != GroupBookingStatus.waiting_for_check_in)
        {
            throw new GroupBookingStatusNotCancellableException(groupBooking.Status);
        }

        groupBooking.Status = GroupBookingStatus.cancelled;

        dbContext.GroupBookings.Update(groupBooking);
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("GroupBooking", request.Id);
        }

        return new Response<object>(true);
    }
}