namespace Bitexco.Ticket.Application.Features.GroupBookings.AddBusToGroupBooking;

public class AddBusToGroupBookingCommandHandler(IApplicationDbContext dbContext) 
    : IRequestHandler<AddBusToGroupBookingCommand, Response<AddBusToGroupBookingResult>>
{
    public async Task<Response<AddBusToGroupBookingResult>> Handle(AddBusToGroupBookingCommand request, CancellationToken cancellationToken)
    {
        var groupBooking = await dbContext.GroupBookings
            .Include(x => x.GroupBookingBuses)
            .AsTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken)
            ?? throw new GroupBookingNotFoundException(request.Id);

        groupBooking.GroupBookingBuses ??= [];
        groupBooking.GroupBookingBuses.Add(
            new GroupBookingBus
            {
                Number = request.Bus?.Number ?? string.Empty,
                AgentIds = request.Bus?.AgentIds ?? [],
                GroupBookingId = groupBooking.Id
            }
        );

        dbContext.GroupBookings.Update(groupBooking);
        var saveResult = await dbContext.SaveChangesAsync(cancellationToken);
        if (saveResult <= 0)
        {
            throw new NoEntityUpdatedException("GroupBooking", request.Id);
        }

        return new Response<AddBusToGroupBookingResult>(new AddBusToGroupBookingResult(groupBooking.Id));
    }
}