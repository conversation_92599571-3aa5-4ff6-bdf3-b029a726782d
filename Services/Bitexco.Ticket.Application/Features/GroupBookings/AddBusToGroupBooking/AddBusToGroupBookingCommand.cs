using Bitexco.Ticket.Application.Features.GroupBookings.CreateGroupBooking;

namespace Bitexco.Ticket.Application.Features.GroupBookings.AddBusToGroupBooking;

/// <summary>
/// Command to create a bus for a group booking
/// </summary>
public record AddBusToGroupBookingCommand : IRequest<Response<AddBusToGroupBookingResult>>
{
    public Guid Id { get; set; }
    public CreateGroupBookingBus? Bus { get; set; }
}

public record AddBusToGroupBookingResult(Guid Id);
