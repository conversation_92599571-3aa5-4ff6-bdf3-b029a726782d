namespace Bitexco.Ticket.Application.Features.GroupBookings.AddBusToGroupBooking;

/// <summary>
/// Validator for AddBusToGroupBookingCommand
/// </summary>
public class AddBusToGroupBookingCommandValidator : AbstractValidator<AddBusToGroupBookingCommand>
{
    public AddBusToGroupBookingCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Group booking ID is required");

        RuleFor(x => x.Bus)
            .NotNull().WithMessage("Bus information is required");
    }
}