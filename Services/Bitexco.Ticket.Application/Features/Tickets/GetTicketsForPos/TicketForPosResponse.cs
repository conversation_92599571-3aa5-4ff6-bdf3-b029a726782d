using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.GetTicketsForPos;

public record TicketForPosResponse
{
    public Guid Id { get; set; }
    public string Code { get; set; } = null!;
    public string? OrderCode { get; set; }
    public string QrCode => Code;
    public Guid? OrderItemId { get; set; }
    public Guid? CustomerId { get; set; }
    public Guid? PosDeviceId { get; set; }
    public TicketStatus Status { get; set; }
    public string? StatusName => Status.ToString();
    public DateTime? SoldAt { get; set; }
    public DateTime? UsedAt { get; set; }
    public DateTime? CancelledAt { get; set; }
    public DateTime? RefundedAt { get; set; }
    public DateTime? ExpiredAt { get; set; }
    public DateOnly? PlannedVisitDate { get; set; }
    public TimeOnly? PlannedVisitTime { get; set; }
    public decimal Price { get; set; }
    public TicketTypeForPosDto? TicketType { get; set; }
}

public record TicketTypeForPosDto
{
    public Guid Id { get; set; }
    public string? Code { get; set; }
    public string? Name { get; set; }
    public string? Icon { get; set; }
    public string? Description { get; set; }
}
