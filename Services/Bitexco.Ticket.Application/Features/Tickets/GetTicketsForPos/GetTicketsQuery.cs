namespace Bitexco.Ticket.Application.Features.Tickets.GetTicketsForPos;

/// <summary>
/// Query to retrieve tickets
/// </summary>
public record GetTicketsForPosQuery : IRequest<Response<List<TicketForPosResponse>>>
{
    /// <summary>
    /// The sole date of the ticket to retrieve
    /// </summary>
    public DateOnly SoldDate { get; init; }
}

/// <summary>
/// Handler for retrieving tickets for POS
/// </summary>
public class GetTicketsForPosQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetTicketsForPosQuery, Response<List<TicketForPosResponse>>>
{
    public async Task<Response<List<TicketForPosResponse>>> Handle(GetTicketsForPosQuery request, CancellationToken cancellationToken)
    {
        var startDate = request.SoldDate.ToDateTime(new TimeOnly(0, 0), DateTimeKind.Utc);
        var endDate = request.SoldDate.ToDateTime(new TimeOnly(23, 59, 59), DateTimeKind.Utc);

        var ticketsDatas = await dbContext.Tickets
            .AsNoTracking()
            .Include(t => t.TicketType)
            .Include(t => t.OrderItem).ThenInclude(oi => oi!.Order)
            .Where(t => t.SoldAt >= startDate && t.SoldAt <= endDate)
            .OrderBy(t => t.SoldAt)
            .ToListAsync(cancellationToken);
        
        
        var ticketForPosResponse = ticketsDatas.Select(ticket => new TicketForPosResponse
        {
            Id = ticket.Id,
            Code = ticket.Code,
            OrderCode = ticket.OrderItem?.Order?.Code,
            OrderItemId = ticket.OrderItem?.Id,
            CustomerId = ticket.CustomerId,
            PosDeviceId = ticket.PosDeviceId,
            Status = ticket.Status,
            SoldAt = ticket.SoldAt,
            UsedAt = ticket.UsedAt,
            CancelledAt = ticket.CancelledAt,
            RefundedAt = ticket.RefundedAt,
            ExpiredAt = ticket.ExpiredAt,
            PlannedVisitDate = ticket.PlannedVisitDate,
            PlannedVisitTime = ticket.PlannedVisitTime,
            Price = ticket.OrderItem?.UnitPrice ?? 0,
            TicketType = new TicketTypeForPosDto
            {
                Id = ticket.TicketType.Id,
                Code = ticket.TicketType.Code,
                Name = ticket.TicketType.Name,
                Icon = ticket.TicketType.Icon,
                Description = ticket.TicketType.Description
            }
        }).ToList();

        return new Response<List<TicketForPosResponse>>(ticketForPosResponse);
    }
}
