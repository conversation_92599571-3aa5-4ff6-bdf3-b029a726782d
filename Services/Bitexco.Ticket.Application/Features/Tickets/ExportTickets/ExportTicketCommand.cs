﻿using Bitexco.Ticket.Application.Features.GroupBookings.ExportGroupBookings;
using Bitexco.Ticket.Domain.Enums;
using BuildingBlocks.FileServices;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bitexco.Ticket.Application.Features.Tickets.ExportTickets
{
    public class ExportTicketCommand : ICommand<Response<ExportTicketResponse>>
    {
        public string? Code { get; set; }

        /// <summary>
        /// The ID of the ticket type to filter by
        /// </summary>
        public Guid? TicketTypeId { get; init; }

        /// <summary>
        /// The status of the ticket to filter by
        /// </summary>
        public TicketStatus? Status { get; init; }

        /// <summary>
        /// The start date for filtering tickets by purchase date (SoldAt)
        /// </summary>
        public DateTime? StartDate { get; init; }

        /// <summary>
        /// The end date for filtering tickets by purchase date (SoldAt)
        /// </summary>
        public DateTime? EndDate { get; init; }
    }

    public class ExportTicketResponse
    {
        public string? File { get; set; }
    }

    public class ExportTicketData
    {
        [Display(Name = "STT")]
        public int STT { get; set; }

        [Display(Name = "Mã vé")]
        public string? Code { get; set; }

        [Display(Name = "Mã đại lý")]
        public string? AgentCode { get; set; }

        [Display(Name = "Tên đại lý")]
        public string? AgentName { get; set; }

        [Display(Name = "Loại vé")]
        public string? TicketTypeName { get; set; }

        [Display(Name = "Ngày mua")]
        public string? SoldAt { get; set; }

        [Display(Name = "Ngày xuất vé")]
        public string? UsedAt { get; set; }

        [Display(Name = "Trạng thái")]
        public string? Status { get; set; }
    }

    public class ExportTicketCommandHandler(IApplicationDbContext applicationDbContext)
        : ICommandHandler<ExportTicketCommand, Response<ExportTicketResponse>>
    {
        private readonly IApplicationDbContext _applicationDbContext = applicationDbContext;

        public async Task<Response<ExportTicketResponse>> Handle(ExportTicketCommand request, CancellationToken cancellationToken)
        {
            var query = _applicationDbContext.Tickets
                .Include(x => x.TicketType)
                .Include(x => x.Agent)
                .AsNoTracking()
                .AsQueryable();
            if (!string.IsNullOrEmpty(request.Code))
            {
                query = query.Where(x => x.Code == request.Code);
            }
            if (request.TicketTypeId.HasValue)
            {
                query = query.Where(x => x.TicketTypeId == request.TicketTypeId.Value);
            }
            if (request.Status.HasValue)
            {
                query = query.Where(x => x.Status == request.Status.Value);
            }
            if (request.StartDate.HasValue)
            {
                query = query.Where(x => x.SoldAt >= request.StartDate.Value);
            }
            if (request.EndDate.HasValue)
            {
                query = query.Where(x => x.SoldAt <= request.EndDate.Value);
            }

            var tickets = await query.ToListAsync(cancellationToken);
            var exportData = tickets.Select((ticket, index) => new ExportTicketData
            {
                STT = index + 1,
                Code = ticket.Code,
                TicketTypeName = ticket.TicketType?.Name,
                SoldAt = ticket.SoldAt?.ToString("HH:mm dd-MM-yyy"),
                UsedAt = ticket.UsedAt?.ToString("HH:mm dd-MM-yyy"),
                Status = CastStatus(ticket.Status)
            }).ToList();
            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Templates", "Bookings", "BookingExportFile.xlsx");

            string path = "exports/bookings";
            string folder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", path);
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);
            string fileName = $"BookingExport_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
            string filefolder = Path.Combine(folder, fileName);

            ExcelServices.ExportData(exportData, templatePath, filefolder, 2);
            return new Response<ExportTicketResponse>
            {
                Data = new ExportTicketResponse { File = path + "/" + fileName }
            };
        }

        private string CastStatus(TicketStatus status)
        {
            return status switch
            {
                TicketStatus.created => "Đã tạo",
                TicketStatus.sold => "Đã bán",
                TicketStatus.used => "Đã sử dụng",
                TicketStatus.cancelled => "Đã hủy",
                TicketStatus.refunded => "Đã hoàn",
                TicketStatus.expired => "Đã hết hạn",
                _ => "Không xác định"
            };
        }
    }
}