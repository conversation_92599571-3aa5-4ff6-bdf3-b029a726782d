using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Tickets.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.GetTicketHistoryByCode;

/// <summary>
/// Query to retrieve ticket history
/// </summary>
public record GetTicketHistoryByCodeQuery(string Code) : IRequest<Response<TicketHistoryResponse>>;

/// <summary>
/// Handler for retrieving ticket history
/// </summary>
public class GetTicketHistoryQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetTicketHistoryByCodeQuery, Response<TicketHistoryResponse>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<TicketHistoryResponse>> Handle(GetTicketHistoryByCodeQuery request, CancellationToken cancellationToken)
    {
        var ticket = await _dbContext.Tickets
            .AsNoTracking()
            .Include(t => t.TicketType)
            .FirstOrDefaultAsync(t => t.Code == request.Code, cancellationToken)
            ?? throw new TicketNotFoundException(request.Code);

        // Get ticket history records
        var statusChanges = await _dbContext.TicketHistories
            .Where(th => th.TicketId == ticket.Id)
            .OrderBy(th => th.ChangedAt)
            .ToListAsync(cancellationToken);

        var ticketDto = ticket.Adapt<TicketResponse>();
        ticketDto.TicketTypeName = ticket.TicketType?.Name ?? "Unknown Type";

        // Determine if ticket is valid
        bool isValid = ticket.Status == TicketStatus.created || ticket.Status == TicketStatus.sold;
        if (isValid && ticket.ExpiredAt.HasValue && ticket.ExpiredAt.Value < DateTime.UtcNow)
        {
            isValid = false;
        }
        ticketDto.IsValid = isValid;

        var statusChangeDtos = statusChanges.Adapt<List<TicketHistoryDto>>();

        var result = new TicketHistoryResponse
        {
            Ticket = ticketDto,
            StatusChanges = statusChangeDtos
        };

        return new Response<TicketHistoryResponse>(result);
    }
}