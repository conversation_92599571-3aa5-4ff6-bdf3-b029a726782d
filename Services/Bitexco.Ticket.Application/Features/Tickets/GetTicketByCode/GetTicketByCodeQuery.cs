using Bitexco.Ticket.Application.Features.Tickets.Dtos;

namespace Bitexco.Ticket.Application.Features.Tickets.GetTicketByCode;

/// <summary>
/// Query to retrieve detailed ticket information by code
/// </summary>
public record GetTicketByCodeQuery(string Code) : IRequest<Response<DetailTicketResponse>>;

/// <summary>
/// Handler for retrieving detailed ticket information by code
/// </summary>
public class GetTicketByCodeQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetTicketByCodeQuery, Response<DetailTicketResponse>>
{
    public async Task<Response<DetailTicketResponse>> Handle(GetTicketByCodeQuery request, CancellationToken cancellationToken)
    {
        var ticket = await dbContext.Tickets
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.Code == request.Code, cancellationToken)
            ?? throw new TicketNotFoundException(request.Code);

        var ticketType = await dbContext.TicketTypes
                .AsNoTracking()
                .FirstOrDefaultAsync(tt => tt.Id == ticket.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(ticket.TicketTypeId);
        
        Customer? customer = null;
        if (ticket.CustomerId.HasValue)
        {
            customer = await dbContext.Customers
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == ticket.CustomerId.Value, cancellationToken);
        }
        
        Order? order = null;
        OrderItem? orderItem = null;
        if (ticket.OrderItemId.HasValue)
        {
            orderItem = await dbContext.OrderItems
                .AsNoTracking()
                .FirstOrDefaultAsync(oi => oi.Id == ticket.OrderItemId.Value, cancellationToken);
                
            if (orderItem != null)
            {
                order = await dbContext.Orders
                    .AsNoTracking()
                    .Include(o => o.Agent)
                    .Include(o => o.RetailChannel)
                    .FirstOrDefaultAsync(o => o.Id == orderItem.OrderId, cancellationToken);
            }
        }
        
        var ticketHistories = await dbContext.TicketHistories
            .AsNoTracking()
            .Where(th => th.TicketId == ticket.Id)
            .OrderBy(th => th.ChangedAt)
            .ToListAsync(cancellationToken);
        
        var response = new DetailTicketResponse
        {
            Ticket = BuildTicketInfo(ticket, ticketType, orderItem, order),
            Customer = BuildCustomerInfo(customer, order),
            Lifecycle = BuildTicketLifecycle(ticket, ticketHistories)
        };

        return new Response<DetailTicketResponse>(response);
    }

    /// <summary>
    /// Build thông tin vé
    /// </summary>
    private static TicketInfo BuildTicketInfo(
        Domain.Models.Ticket ticket, 
        TicketType? ticketType,
        OrderItem? orderItem,
        Order? order)
    {
        return new TicketInfo
        {
            OrderId = order?.Id,
            Code = ticket.Code,
            TicketTypeName = ticketType?.Name ?? "Unknown Type",
            Price = (orderItem?.UnitPrice ?? 0) * (orderItem?.Quantity ?? 0), 
            PurchaseChannel = order?.RetailChannel?.Name,
            ValidFrom = ticket.SoldAt, // Ngày hiệu lực = ngày bán
            ValidUntil = ticket.ExpiredAt, // Ngày hết hạn
            Status = ticket.Status,
            StatusName = ticket.Status.ToString().ToLowerInvariant()
        };
    }

    /// <summary>
    /// Build thông tin khách hàng theo thứ tự ưu tiên: Agent > Customer > null
    /// </summary>
    private static CustomerInfo? BuildCustomerInfo(Customer? customer, Order? order)
    {
        var agent = order?.Agent;
        if (agent != null)
        {
            return new CustomerInfo
            {
                Type = "agent",
                Code = agent.Code,
                Name = agent.Name,
                Contact = agent.PhoneNumber ?? agent.Email
            };
        }
        
        if (customer != null)
        {
            return new CustomerInfo
            {
                Type = "customer", 
                Code = customer.Id.ToString(),
                Name = customer.FullName,
                Contact = customer.PhoneNumber ?? customer.Email
            };
        }
        
        return null;
    }

    /// <summary>
    /// Build vòng đời vé với history
    /// </summary>
    private static TicketLifecycle BuildTicketLifecycle(Domain.Models.Ticket ticket, List<TicketHistory> ticketHistories)
    {
        return new TicketLifecycle
        {
            UsedDate = ticket.UsedAt,          // Ngày dùng
            IssuedDate = ticket.CreatedAt,     // Ngày xuất (tạo)
            SoldDate = ticket.SoldAt,          // Ngày bán
            History = ticketHistories
                .Select(h => new TicketHistoryItem
                {
                    FromStatus = h.FromStatus,
                    ToStatus = h.ToStatus,
                    ChangedAt = h.ChangedAt,
                    Location = h.Location,
                    FromStatusName = h.FromStatus?.ToString().ToLowerInvariant(),
                    ToStatusName = h.ToStatus.ToString().ToLowerInvariant()
                })
                .ToList()
        };
    }
}
