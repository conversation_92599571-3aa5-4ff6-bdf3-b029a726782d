using Bitexco.Ticket.Application.Features.Tickets.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.GetTickets;

/// <summary>
/// Query to retrieve tickets
/// </summary>
public record GetTicketsQuery
    : IRequest<PaginationResponse<TicketResponse>>, IPaginationRequest
{
    /// <summary>
    /// The code of the ticket to retrieve
    /// </summary>
    public string? Code { get; init; }

    /// <summary>
    /// The ID of the ticket type to filter by
    /// </summary>
    public Guid? TicketTypeId { get; init; }

    /// <summary>
    /// The status of the ticket to filter by
    /// </summary>
    public TicketStatus? Status { get; init; }

    /// <summary>
    /// The start date to filter tickets sold after this date
    /// </summary>
    public DateOnly? SoldDate { get; init; }

    /// <summary>
    /// The page index for pagination
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// The page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 100;
}

/// <summary>
/// Handler for retrieving a ticket by code
/// </summary>
public class GetTicketsQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetTicketsQuery, PaginationResponse<TicketResponse>>
{

    public async Task<PaginationResponse<TicketResponse>> Handle(GetTicketsQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Tickets
            .AsNoTracking()
            .Include(t => t.TicketType)
            .Include(t => t.Agent)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(request.Code))
        {
            query = query.Where(t => t.Code.ToLower().Contains(request.Code.ToLower()));
        }
        
        if (request.TicketTypeId.HasValue)
        {
            query = query.Where(t => t.TicketTypeId == request.TicketTypeId.Value);
        }

        if (request.Status.HasValue)
        {
            query = query.Where(t => t.Status == request.Status.Value);
        }

        if (request.SoldDate.HasValue)
        {
            var startDate = request.SoldDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
            var endDate = request.SoldDate.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);
            query = query.Where(t => t.SoldAt >= startDate && t.SoldAt <= endDate);
        }

        var totalCount = await query.CountAsync(cancellationToken);
        
        var ticketsData = await query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);
        
        var tickets = ticketsData.Select(x => 
        {
            var response = x.Adapt<TicketResponse>();
            response.AgentName = x.Agent?.Name;
            response.AgentCode = x.Agent?.Code;
            response.StatusName = x.Status.ToString().ToLowerInvariant();
            return response;
        }).ToList();

        return new PaginationResponse<TicketResponse>(request.PageIndex, request.PageSize, totalCount, tickets);
    }
}
