namespace Bitexco.Ticket.Application.Features.Tickets.UseTicketByCode;

/// <summary>
/// Command for marking a ticket as used by its code
/// </summary>
public record UseTicketByCodeCommand(
    string Code, 
    string AccessLocation, 
    Guid? ValidatedByUserId, 
    Guid? ValidatedAtDeviceId) : IRequest<Response<UseTicketResult>>;
    
/// <summary>
/// Result object for using a ticket
/// </summary>
public record UseTicketResult(bool Success);