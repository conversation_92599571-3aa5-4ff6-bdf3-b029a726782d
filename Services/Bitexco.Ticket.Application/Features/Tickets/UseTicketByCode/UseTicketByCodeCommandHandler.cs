using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.UseTicketByCode;

/// <summary>
/// Handler for marking a ticket as used by QR code
/// </summary>
public class UseTicketByCodeCommandHandler(
    IApplicationDbContext dbContext) : IRequestHandler<UseTicketByCodeCommand, Response<UseTicketResult>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<UseTicketResult>> Handle(UseTicketByCodeCommand request, CancellationToken cancellationToken)
    {
        // Find ticket by QR code
        var ticket = await _dbContext.Tickets
            .AsTracking()
            .Include(t => t.TicketType)
            .FirstOrDefaultAsync(t => t.Code == request.Code, cancellationToken)
            ?? throw new TicketNotFoundException(request.Code);

        // Validate ticket can be used
        if (ticket.Status != TicketStatus.created && ticket.Status != TicketStatus.sold)
        {
            throw new TicketUsedException(ticket.Code);
        }

        if (ticket.ExpiredAt.HasValue && ticket.ExpiredAt.Value < DateTime.UtcNow)
        {
            throw new TicketExpiredException(ticket.Code);
        }

        // Update ticket status
        var previousStatus = ticket.Status;
        ticket.Status = TicketStatus.used;
        ticket.UsedAt = DateTime.UtcNow;
        ticket.ValidatedByUserId = request.ValidatedByUserId;
        ticket.ValidatedAtDeviceId = request.ValidatedAtDeviceId;
        ticket.AccessLocation = request.AccessLocation;

        // Create ticket history record
        var ticketHistory = new TicketHistory
        {
            TicketId = ticket.Id,
            FromStatus = previousStatus,
            ToStatus = TicketStatus.used,
            ChangedAt = DateTime.UtcNow,
            ChangedByUserId = request.ValidatedByUserId,
            ChangedAtDeviceId = request.ValidatedAtDeviceId,
            Location = request.AccessLocation
        };

        _dbContext.TicketHistories.Add(ticketHistory);

        var result = await _dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("Ticket", ticket.Id);
        }

        return new Response<UseTicketResult>(new UseTicketResult(true));
    }
}