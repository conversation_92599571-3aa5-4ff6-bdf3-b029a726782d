using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Tickets.Dtos;

public record TicketResponse : TicketDto
{
    public Guid Id { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? TicketTypeName { get; set; }
    public string? StatusName { get; set; }
    public string QrCodeContent => Code;
    public bool IsValid { get; set; }
    public string? AgentName { get; set; }
    public string? AgentCode { get; set; }
}
