using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.Dtos;

/// <summary>
/// DTO for ticket history data
/// </summary>
public record TicketHistoryResponse
{
    /// <summary>
    /// Basic ticket information
    /// </summary>
    public TicketResponse Ticket { get; init; } = null!;
    
    /// <summary>
    /// Collection of status changes with timestamps
    /// </summary>
    public IEnumerable<TicketHistoryDto> StatusChanges { get; init; } = [];
}
