using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.Dtos;

/// <summary>
/// Response DTO for detailed ticket information including ticket info, customer info, and lifecycle
/// </summary>
public record DetailTicketResponse
{
    /// <summary>
    /// 1. Thông tin vé
    /// </summary>
    public TicketInfo Ticket { get; set; } = null!;
    
    /// <summary>
    /// 2. Thông tin khách hàng (theo thứ tự ưu tiên: Agent > Customer > null)
    /// </summary>
    public CustomerInfo? Customer { get; set; }
    
    /// <summary>
    /// 3. Vòng đời vé (theo thứ tự: ngày dùng, ngày xuất, ngày bán)
    /// </summary>
    public TicketLifecycle Lifecycle { get; set; } = null!;
}

/// <summary>
/// Thông tin vé
/// </summary>
public record TicketInfo
{
    /// <summary>
    /// Mã đơn hàng
    /// </summary>
    public Guid? OrderId { get; set; }
    
    /// <summary>
    /// Mã vé
    /// </summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// Tên loại vé
    /// </summary>
    public string TicketTypeName { get; set; } = null!;
    
    /// <summary>
    /// Giá vé
    /// </summary>
    public decimal Price { get; set; }
    
    /// <summary>
    /// Mua tại (tên kênh bán hàng)
    /// </summary>
    public string? PurchaseChannel { get; set; }
    
    /// <summary>
    /// Ngày hiệu lực
    /// </summary>
    public DateTime? ValidFrom { get; set; }
    
    /// <summary>
    /// Ngày hết hạn
    /// </summary>
    public DateTime? ValidUntil { get; set; }
    
    /// <summary>
    /// Trạng thái hiện tại
    /// </summary>
    public TicketStatus Status { get; set; }
    
    /// <summary>
    /// Tên trạng thái
    /// </summary>
    public string StatusName { get; set; } = null!;
}

/// <summary>
/// Thông tin khách hàng (theo thứ tự ưu tiên)
/// </summary>
public record CustomerInfo
{
    /// <summary>
    /// Loại: "agent" hoặc "customer"
    /// </summary>
    public string Type { get; set; } = null!;
    
    /// <summary>
    /// Mã (Agent.Code hoặc Customer.Id)
    /// </summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// Tên (Agent.Name hoặc Customer.FullName)
    /// </summary>
    public string Name { get; set; } = null!;
    
    /// <summary>
    /// Thông tin liên hệ
    /// </summary>
    public string? Contact { get; set; }
}

/// <summary>
/// Vòng đời vé
/// </summary>
public record TicketLifecycle
{
    /// <summary>
    /// Ngày dùng vé (nếu đã dùng)
    /// </summary>
    public DateTime? UsedDate { get; set; }
    
    /// <summary>
    /// Ngày xuất vé (ngày tạo)
    /// </summary>
    public DateTime? IssuedDate { get; set; }
    
    /// <summary>
    /// Ngày bán vé
    /// </summary>
    public DateTime? SoldDate { get; set; }
    
    /// <summary>
    /// Lịch sử chi tiết các thay đổi trạng thái
    /// </summary>
    public List<TicketHistoryItem> History { get; set; } = new();
}

/// <summary>
/// Item trong lịch sử vé
/// </summary>
public record TicketHistoryItem
{
    /// <summary>
    /// Trạng thái trước khi thay đổi
    /// </summary>
    public TicketStatus? FromStatus { get; set; }
    
    /// <summary>
    /// Trạng thái sau khi thay đổi
    /// </summary>
    public TicketStatus ToStatus { get; set; }
    
    /// <summary>
    /// Thời gian thay đổi
    /// </summary>
    public DateTime ChangedAt { get; set; }
    
    /// <summary>
    /// Vị trí thay đổi
    /// </summary>
    public string? Location { get; set; }
    
    /// <summary>
    /// Tên trạng thái từ
    /// </summary>
    public string? FromStatusName { get; set; }
    
    /// <summary>
    /// Tên trạng thái đến
    /// </summary>
    public string ToStatusName { get; set; } = null!;
}
