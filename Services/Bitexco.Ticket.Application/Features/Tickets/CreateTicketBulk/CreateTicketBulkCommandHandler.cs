using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.CreateTicketBulk;

public class CreateTicketBulkCommandHandler(
    IApplicationDbContext dbContext) : IRequestHandler<CreateTicketBulkCommand, Response<List<TicketResponseDto>>>
{
    public async Task<Response<List<TicketResponseDto>>> <PERSON>le(CreateTicketBulkCommand request, CancellationToken cancellationToken)
    {
        // Kiểm tra ticket type có tồn tại và active không
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(tt => tt.Id == request.TicketTypeId && tt.IsActive, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.TicketTypeId);

        // Tạo danh sách tickets
        var tickets = new List<Domain.Models.Ticket>();
        var ticketHistories = new List<TicketHistory>();

        for (int i = 0; i < request.Quantity; i++)
        {
            var ticketId = Guid.NewGuid();
            
            var ticket = new Domain.Models.Ticket
            {
                Id = ticketId,
                OrderItemId = null,
                TicketTypeId = request.TicketTypeId,
                Code = $"{ticketType.TicketCodePrefix}-{Guid.NewGuid().ToString()[..ticketType.TicketCodeLengthAfterPrefix].ToUpper()}",
                Status = TicketStatus.created
            };

            tickets.Add(ticket);
            
            var ticketHistory = new Domain.Models.TicketHistory
            {
                Id = Guid.NewGuid(),
                TicketId = ticketId,
                FromStatus = null,
                ToStatus = TicketStatus.created,
                ChangedAt = DateTime.UtcNow,
                ChangedByUserId = null, 
                ChangedAtDeviceId = null, 
                Location = "SYSTEM" 
            };

            ticketHistories.Add(ticketHistory);
        }
        
        dbContext.Tickets.AddRange(tickets);
        dbContext.TicketHistories.AddRange(ticketHistories);
        
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityCreatedException("Ticket");
        }
        
        var ticketResponses = tickets.Adapt<List<TicketResponseDto>>();

        return new Response<List<TicketResponseDto>>(ticketResponses);
    }
}
