using BuildingBlocks.Abstractions;
using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Tickets.CreateTicketBulk;

/// <summary>
/// Command để tạo danh sách ticket dựa vào Id loại vé và số lượng
/// </summary>
/// <param name="TicketTypeId">ID của loại vé</param>
/// <param name="Quantity">Số lượng ticket muốn tạo</param>
public record CreateTicketBulkCommand(
    Guid TicketTypeId,
    int Quantity
) : IRequest<Response<List<TicketResponseDto>>>;
