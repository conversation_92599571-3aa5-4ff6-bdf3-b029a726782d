using FluentValidation;

namespace Bitexco.Ticket.Application.Features.Tickets.CreateTicketBulk;

/// <summary>
/// Validator cho CreateTicketBulkCommand
/// </summary>
public class CreateTicketBulkCommandValidator : AbstractValidator<CreateTicketBulkCommand>
{
    public CreateTicketBulkCommandValidator()
    {
        RuleFor(x => x.TicketTypeId)
            .NotEmpty().WithMessage("TicketTypeId is required");

        RuleFor(x => x.Quantity)
            .GreaterThan(0).WithMessage("Quantity must be greater than 0")
            .LessThanOrEqualTo(1000).WithMessage("Cannot create more than 1000 tickets at once");
    }
}
