using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Tickets.CreateTicket;

/// <summary>
/// Command for creating a new ticket with a unique code/QR
/// </summary>
public record CreateTicketCommand(TicketDto Ticket) : IRequest<Response<CreateTicketResult>>;

/// <summary>
/// Result object for ticket creation
/// </summary>
public record CreateTicketResult
{
    /// <summary>
    /// ID of the created ticket
    /// </summary>
    public Guid Id { get; init; }

    /// <summary>
    /// QR code of the created ticket
    /// </summary>
    public string Code { get; init; } = null!;

    /// <summary>
    /// Base64 encoded QR code image
    /// </summary>
    public string QrCodeContent { get; init; } = null!;
}
