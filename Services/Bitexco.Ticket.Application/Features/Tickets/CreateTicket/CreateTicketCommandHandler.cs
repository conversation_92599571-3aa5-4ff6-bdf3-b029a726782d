using BuildingBlocks.Utils;

namespace Bitexco.Ticket.Application.Features.Tickets.CreateTicket;

/// <summary>
/// Handler for creating a new ticket
/// </summary>
public class CreateTicketCommandHandler(
    IApplicationDbContext dbContext) : IRequestHandler<CreateTicketCommand, Response<CreateTicketResult>>
{

    public async Task<Response<CreateTicketResult>> Handle(CreateTicketCommand request, CancellationToken cancellationToken)
    {
        // Validate ticket type exists (required)
        var ticketType = await dbContext.TicketTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.Id == request.Ticket.TicketTypeId, cancellationToken)
            ?? throw new TicketTypeNotFoundException(request.Ticket.TicketTypeId);

        // Validate OrderItem exists (if provided)
        if (request.Ticket.OrderItemId.HasValue)
        {
            var orderItemExists = await dbContext.OrderItems
                .AsNoTracking()
                .AnyAsync(oi => oi.Id == request.Ticket.OrderItemId.Value, cancellationToken);
            
            if (!orderItemExists)
            {
                throw new OrderItemNotFoundException(request.Ticket.OrderItemId.Value);
            }
        }

        // Validate Customer exists (if provided)
        if (request.Ticket.CustomerId.HasValue)
        {
            var customerExists = await dbContext.Customers
                .AsNoTracking()
                .AnyAsync(c => c.Id == request.Ticket.CustomerId.Value, cancellationToken);
            
            if (!customerExists)
            {
                throw new CustomerNotFoundException(request.Ticket.CustomerId.Value);
            }
        }

        // Validate PosDevice exists (if provided)
        if (request.Ticket.PosDeviceId.HasValue)
        {
            var posDeviceExists = await dbContext.PosDevices
                .AsNoTracking()
                .AnyAsync(pd => pd.Id == request.Ticket.PosDeviceId.Value, cancellationToken);
            
            if (!posDeviceExists)
            {
                throw new PosDeviceNotFoundException(request.Ticket.PosDeviceId.Value);
            }
        }

        // Validate ValidatedAtDevice exists (if provided)
        if (request.Ticket.ValidatedAtDeviceId.HasValue)
        {
            var validatedAtDeviceExists = await dbContext.PosDevices
                .AsNoTracking()
                .AnyAsync(pd => pd.Id == request.Ticket.ValidatedAtDeviceId.Value, cancellationToken);
            
            if (!validatedAtDeviceExists)
            {
                throw new PosDeviceNotFoundException(request.Ticket.ValidatedAtDeviceId.Value);
            }
        }

        // Validate ValidatedByUser exists (if provided)
        if (request.Ticket.ValidatedByUserId.HasValue)
        {
            var userExists = await dbContext.Users
                .AsNoTracking()
                .AnyAsync(u => u.Id == request.Ticket.ValidatedByUserId.Value, cancellationToken);
            
            if (!userExists)
            {
                throw new UserNotFoundException(request.Ticket.ValidatedByUserId.Value);
            }
        }

        // Generate ticket QR code if not provided
        var ticketCode = request.Ticket.Code;
        if (string.IsNullOrEmpty(ticketCode))
        {
            ticketCode = CommonUtils.GenerateNewCode(10);
        }
        else
        {
            // Check if QR code already exists
            var existingTicket = await dbContext.Tickets
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Code == ticketCode, cancellationToken);

            if (existingTicket != null)
            {
                throw new TicketExistedException(ticketCode);
            }
        }

        // Create new ticket entity with all reference fields
        var ticket = request.Ticket.Adapt<Domain.Models.Ticket>();
        ticket.Code = ticketCode;

        dbContext.Tickets.Add(ticket);

        // Create initial ticket history record
        var ticketHistory = new TicketHistory
        {
            TicketId = ticket.Id,
            FromStatus = null,
            ToStatus = ticket.Status,
            ChangedAt = DateTime.UtcNow
        };
        
        dbContext.TicketHistories.Add(ticketHistory);
        
        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityCreatedException("Ticket");
        }

        var createResult = new CreateTicketResult
        {
            Id = ticket.Id,
            Code = ticket.Code,
            QrCodeContent = ticket.Code
        };

        return new Response<CreateTicketResult>(createResult);
    }
}
