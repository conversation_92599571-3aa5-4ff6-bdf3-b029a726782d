using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Tickets.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.ValidateTicketByCode;

/// <summary>
/// Query to validate a ticket
/// </summary>
public record ValidateTicketByCodeQuery(string Code) : IRequest<Response<TicketValidationResult>>;

/// <summary>
/// DTO for ticket validation result
/// </summary>
public record TicketValidationResult(
    bool IsValid,
    TicketStatus Status,
    bool IsExpired
);

/// <summary>
/// Handler for validating a ticket
/// </summary>
public class ValidateTicketQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<ValidateTicketByCodeQuery, Response<TicketValidationResult>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<TicketValidationResult>> Handle(ValidateTicketByCodeQuery request, CancellationToken cancellationToken)
    {
        var ticket = await _dbContext.Tickets
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.Code == request.Code, cancellationToken)
            ?? throw new TicketNotFoundException(request.Code);

        bool isExpired = ticket.ExpiredAt.HasValue && ticket.ExpiredAt.Value < DateTime.UtcNow;
        bool isValid = (ticket.Status == TicketStatus.created || ticket.Status == TicketStatus.sold) && !isExpired;

        string message = isValid
            ? "Ticket is valid and can be used"
            : isExpired
                ? "Ticket has expired"
                : $"Ticket is not valid. Status: {ticket.Status}";

        var result = new TicketValidationResult(isValid, ticket.Status, isExpired);

        return new Response<TicketValidationResult>
        {
            Data = result,
            Message = message
        };
    }
}
