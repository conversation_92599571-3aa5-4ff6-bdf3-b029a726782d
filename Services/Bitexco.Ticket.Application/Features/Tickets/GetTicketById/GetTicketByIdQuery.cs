using Bitexco.Ticket.Application.Dtos;

namespace Bitexco.Ticket.Application.Features.Tickets.GetTicketById;

/// <summary>
/// Query to retrieve detailed ticket information by ID
/// </summary>
public record GetTicketByIdQuery(Guid Id) : IRequest<Response<GetTicketByIdResponse>>;

public record GetTicketByIdResponse : TicketResponseDto
{
    public TicketTypeResponseDto? TicketType { get; set; }
    public OrderItemResponseDto? OrderItem { get; set; }
    public AgentResponseDto? Agent { get; set; }
    public UserResponseDto? ValidatedByUser { get; set; }
    public RetailChannelResponseDto? RetailChannel { get; set; }
}

/// <summary>
/// Handler for retrieving detailed ticket information by ID
/// </summary>
public class GetTicketByIdQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetTicketByIdQuery, Response<GetTicketByIdResponse>>
{
    public async Task<Response<GetTicketByIdResponse>> Handle(GetTicketByIdQuery request, CancellationToken cancellationToken)
    {
        var ticket = await dbContext.Tickets
            .AsNoTracking()
            .Include(t => t.TicketType)
            .Include(t => t.OrderItem)
            .Include(t => t.Agent)
            .Include(t => t.ValidatedByUser)
            .Include(t => t.RetailChannel)
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken)
            ?? throw new TicketNotFoundException(request.Id);

        return new Response<GetTicketByIdResponse>(ticket.Adapt<GetTicketByIdResponse>());
    }
}
