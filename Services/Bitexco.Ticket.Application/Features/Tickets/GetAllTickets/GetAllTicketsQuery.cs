using Bitexco.Ticket.Application.Features.Tickets.Dtos;

namespace Bitexco.Ticket.Application.Features.Tickets.GetAllTickets;

/// <summary>
/// Query to retrieve tickets
/// </summary>
public record GetAllTicketsQuery : IRequest<Response<List<AllTicketResponse>>>
{
    /// <summary>
    /// The status of the ticket to filter by
    /// </summary>
    public TicketStatus? Status { get; init; }

    /// <summary>
    /// The start date to filter tickets created after this date
    /// </summary>
    public DateOnly? CreatedDateFrom { get; init; }

    /// <summary>
    /// The end date to filter tickets created before this date
    /// </summary>
    public DateOnly? CreatedDateTo { get; init; }
}

/// <summary>
/// Handler for retrieving a ticket by code
/// </summary>
public class GetAllTicketsQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<GetAllTicketsQuery, Response<List<AllTicketResponse>>>
{
    public async Task<Response<List<AllTicketResponse>>> Handle(GetAllTicketsQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Tickets
            .AsNoTracking()
            .AsQueryable();

        if (request.Status.HasValue)
        {
            query = query.Where(t => t.Status == request.Status.Value);
        }

        if (request.CreatedDateFrom.HasValue)
        {
            var startTime = request.CreatedDateFrom.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
            query = query.Where(t => t.CreatedAt >= startTime);
        }

        if (request.CreatedDateTo.HasValue)
        {
            var endTime = request.CreatedDateTo.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);
            query = query.Where(t => t.CreatedAt <= endTime);
        }

        var ticketsData = await query
            .ProjectToType<AllTicketResponse>()
            .ToListAsync(cancellationToken);
        
        return new Response<List<AllTicketResponse>>(ticketsData);
    }
}
