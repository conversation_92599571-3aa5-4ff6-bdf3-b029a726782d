namespace Bitexco.Ticket.Application.Features.Tickets.GetAllTickets;

public record AllTicketResponse
{
    public Guid Id { get; set; }
    public string Code { get; set; } = null!;
    public Guid? TicketTypeId { get; set; }
    public TicketStatus? Status { get; set; }
    public DateTime? SoldAt { get; set; }
    public DateTime? UsedAt { get; set; }
    public DateTime? CancelledAt { get; set; }
    public DateTime? RefundedAt { get; set; }
    public DateTime? ExpiredAt { get; set; }
    public DateTime? CreatedAt { get; set; }
}
