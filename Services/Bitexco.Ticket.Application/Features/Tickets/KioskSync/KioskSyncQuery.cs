using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Tickets.Dtos;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Features.Tickets.KioskSync;

/// <summary>
/// Query for kiosk synchronization
/// </summary>
public record KioskSyncQuery(DateTime? LastSyncTime, Guid? DeviceId) : IRequest<Response<KioskSyncResult>>;

/// <summary>
/// Result object for kiosk synchronization
/// </summary>
public record KioskSyncResult(
    IEnumerable<TicketDto> Tickets,
    DateTime SyncTimestamp
);

/// <summary>
/// Handler for kiosk synchronization
/// </summary>
public class KioskSyncQueryHandler(
    IApplicationDbContext dbContext) : IRequestHandler<KioskSyncQuery, Response<KioskSyncResult>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<KioskSyncResult>> Handle(KioskSyncQuery request, CancellationToken cancellationToken)
    {
        var lastSyncTime = request.LastSyncTime ?? DateTime.UtcNow.AddDays(-1);

        // Get tickets updated since last sync
        var tickets = await _dbContext.Tickets
            .AsNoTracking()
            .Include(t => t.TicketType)
            .Where(t => t.UpdatedAt >= lastSyncTime)
            .OrderBy(t => t.UpdatedAt)
            .Take(1000) // Limit to reasonable batch size
            .ToListAsync(cancellationToken);

        var ticketDtos = new List<TicketResponse>();

        foreach (var ticket in tickets)
        {
            var dto = ticket.Adapt<TicketResponse>();
            dto.TicketTypeName = ticket.TicketType?.Name ?? "Unknown Type";

            // Determine if ticket is valid
            bool isValid = ticket.Status == TicketStatus.created || ticket.Status == TicketStatus.sold;
            if (isValid && ticket.ExpiredAt.HasValue && ticket.ExpiredAt.Value < DateTime.UtcNow)
            {
                isValid = false;
            }
            dto.IsValid = isValid;

            ticketDtos.Add(dto);
        }

        var result = new KioskSyncResult(ticketDtos, DateTime.UtcNow);

        return new Response<KioskSyncResult>(result);
    }
}