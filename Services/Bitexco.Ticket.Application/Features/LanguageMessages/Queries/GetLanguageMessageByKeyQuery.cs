using Bitexco.Ticket.Application.Features.LanguageMessages.Dtos;

namespace Bitexco.Ticket.Application.Features.LanguageMessages.Queries;

/// <summary>
/// Query to retrieve a language message by key
/// </summary>
public record GetLanguageMessageByKeyQuery(string Key) : IQuery<Response<LanguageMessageResponse>>;

/// <summary>
/// Handler for GetLanguageMessageByKeyQuery
/// </summary>
public class GetLanguageMessageByKeyQueryHandler(IApplicationDbContext dbContext)
    : IQueryHandler<GetLanguageMessageByKeyQuery, Response<LanguageMessageResponse>>
{
    public async Task<Response<LanguageMessageResponse>> Handle(
        GetLanguageMessageByKeyQuery request,
        CancellationToken cancellationToken)
    {
        var languageMessage = await dbContext.LanguageMessages
            .AsNoTracking()
            .ProjectToType<LanguageMessageResponse>()
            .FirstOrDefaultAsync(lm => lm.Key == request.Key, cancellationToken);

        if (languageMessage == null)
        {
            throw new LanguageMessageNotFoundException(request.Key);
        }

        return new Response<LanguageMessageResponse>(languageMessage);
    }
}
