using Bitexco.Ticket.Application.Features.LanguageMessages.Dtos;

namespace Bitexco.Ticket.Application.Features.LanguageMessages.Queries;

/// <summary>
/// Query to retrieve all language messages
/// </summary>
public record GetAllLanguageMessagesQuery : IQuery<Response<List<LanguageMessageResponse>>>;

/// <summary>
/// Handler for GetAllLanguageMessagesQuery
/// </summary>
public class GetAllLanguageMessagesQueryHandler(IApplicationDbContext dbContext)
    : IQueryHandler<GetAllLanguageMessagesQuery, Response<List<LanguageMessageResponse>>>
{
    public async Task<Response<List<LanguageMessageResponse>>> Handle(
        GetAllLanguageMessagesQuery request,
        CancellationToken cancellationToken)
    {
        var languageMessages = await dbContext.LanguageMessages
            .AsNoTracking()
            .OrderBy(lm => lm.Key)
            .ProjectToType<LanguageMessageResponse>()
            .ToListAsync(cancellationToken);

        return new Response<List<LanguageMessageResponse>>(languageMessages);
    }
}
