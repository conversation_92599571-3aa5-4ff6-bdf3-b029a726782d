using Bitexco.Ticket.Application.Features.LanguageMessages.Dtos;

namespace Bitexco.Ticket.Application.Features.LanguageMessages.Commands;

/// <summary>
/// Command to update an existing language message
/// </summary>
public record UpdateLanguageMessageCommand(string Key, UpdateLanguageMessageDto LanguageMessage) 
    : ICommand<Response<UpdateLanguageMessageResult>>;

/// <summary>
/// Result model for UpdateLanguageMessageCommand
/// </summary>
public record UpdateLanguageMessageResult(string Key);

/// <summary>
/// Validator for UpdateLanguageMessageCommand
/// </summary>
public class UpdateLanguageMessageCommandValidator : AbstractValidator<UpdateLanguageMessageCommand>
{
    public UpdateLanguageMessageCommandValidator()
    {
        RuleFor(x => x.Key)
            .NotEmpty().WithMessage("Key is required.")
            .MaximumLength(100).WithMessage("Key cannot exceed 100 characters.");

        RuleFor(x => x.LanguageMessage.Message)
            .NotEmpty().WithMessage("Message is required.");
    }
}

/// <summary>
/// Handler for UpdateLanguageMessageCommand
/// </summary>
public class UpdateLanguageMessageCommandHandler(IApplicationDbContext dbContext)
    : ICommandHandler<UpdateLanguageMessageCommand, Response<UpdateLanguageMessageResult>>
{
    public async Task<Response<UpdateLanguageMessageResult>> Handle(
        UpdateLanguageMessageCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await dbContext.LanguageMessages
            .FindAsync([request.Key], cancellationToken);

        if (entity == null)
        {
            throw new LanguageMessageNotFoundException(request.Key);
        }

        entity.Message = request.LanguageMessage.Message;
        dbContext.LanguageMessages.Update(entity);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityUpdatedException("LanguageMessage", entity.Key);
        }

        return new Response<UpdateLanguageMessageResult>(new UpdateLanguageMessageResult(entity.Key));
    }
}
