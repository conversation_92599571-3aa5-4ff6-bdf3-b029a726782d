using Bitexco.Ticket.Application.Features.LanguageMessages.Dtos;

namespace Bitexco.Ticket.Application.Features.LanguageMessages.Commands;

/// <summary>
/// Command to create a new language message
/// </summary>
public record CreateLanguageMessageCommand(CreateLanguageMessageDto LanguageMessage) 
    : ICommand<Response<CreateLanguageMessageResult>>;

/// <summary>
/// Result model for CreateLanguageMessageCommand
/// </summary>
public record CreateLanguageMessageResult(string Key);

/// <summary>
/// Validator for CreateLanguageMessageCommand
/// </summary>
public class CreateLanguageMessageCommandValidator : AbstractValidator<CreateLanguageMessageCommand>
{
    public CreateLanguageMessageCommandValidator()
    {
        RuleFor(x => x.LanguageMessage.Key)
            .NotEmpty().WithMessage("Key is required.")
            .MaximumLength(100).WithMessage("Key cannot exceed 100 characters.")
            .Matches("^[a-zA-Z0-9._-]+$").WithMessage("Key can only contain letters, numbers, dots, underscores, and hyphens.");

        RuleFor(x => x.LanguageMessage.Message)
            .NotEmpty().WithMessage("Message is required.");
    }
}

/// <summary>
/// Handler for CreateLanguageMessageCommand
/// </summary>
public class CreateLanguageMessageCommandHandler(IApplicationDbContext dbContext)
    : ICommandHandler<CreateLanguageMessageCommand, Response<CreateLanguageMessageResult>>
{
    public async Task<Response<CreateLanguageMessageResult>> Handle(
        CreateLanguageMessageCommand request,
        CancellationToken cancellationToken)
    {
        var existingMessage = await dbContext.LanguageMessages
            .AsNoTracking()
            .FirstOrDefaultAsync(lm => lm.Key == request.LanguageMessage.Key, cancellationToken);

        if (existingMessage != null)
        {
            throw new LanguageMessageKeyExistsException(request.LanguageMessage.Key);
        }

        var entity = new LanguageMessage
        {
            Key = request.LanguageMessage.Key,
            Message = request.LanguageMessage.Message
        };

        await dbContext.LanguageMessages.AddAsync(entity, cancellationToken);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityCreatedException("LanguageMessage");
        }

        return new Response<CreateLanguageMessageResult>(new CreateLanguageMessageResult(entity.Key));
    }
}
