namespace Bitexco.Ticket.Application.Features.LanguageMessages.Commands;

/// <summary>
/// Command to delete a language message (hard delete)
/// </summary>
public record DeleteLanguageMessageCommand(string Key) 
    : ICommand<Response<object>>;

/// <summary>
/// Validator for DeleteLanguageMessageCommand
/// </summary>
public class DeleteLanguageMessageCommandValidator : AbstractValidator<DeleteLanguageMessageCommand>
{
    public DeleteLanguageMessageCommandValidator()
    {
        RuleFor(x => x.Key)
            .NotEmpty().WithMessage("Key is required.")
            .MaximumLength(100).WithMessage("Key cannot exceed 100 characters.");
    }
}

/// <summary>
/// Handler for DeleteLanguageMessageCommand
/// </summary>
public class DeleteLanguageMessageCommandHandler(IApplicationDbContext dbContext)
    : ICommandHandler<DeleteLanguageMessageCommand, Response<object>>
{
    public async Task<Response<object>> Handle(
        DeleteLanguageMessageCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await dbContext.LanguageMessages
            .FirstOrDefaultAsync(lm => lm.Key == request.Key, cancellationToken);

        if (entity == null)
        {
            throw new LanguageMessageNotFoundException(request.Key);
        }
        
        dbContext.LanguageMessages.Remove(entity);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result == 0)
        {
            throw new NoEntityDeletedException("LanguageMessage", entity.Key);
        }

        return new Response<object>(new {status = true});
    }
}
