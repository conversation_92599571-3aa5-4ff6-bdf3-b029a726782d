namespace Bitexco.Ticket.Application.Features.OrderRefunds.GetOrderRefunds;

/// <summary>
/// Query to get paginated list of order refunds with optional filtering
/// </summary>
public record GetOrderRefundsQuery
    : IRequest<PaginationResponse<GetOrderRefundsResponse>>, IPaginationRequest, ISortRequest
{
    // Pagination parameters
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool? IsDescending { get; set; } = true;

    // Filter parameters
    public string? SearchTerm { get; set; }
    public DateOnly? RefundDate { get; set; }
    public Guid? RetailChannelId { get; set; }
    public OrderRefundStatus? RefundStatus { get; set; }
}
