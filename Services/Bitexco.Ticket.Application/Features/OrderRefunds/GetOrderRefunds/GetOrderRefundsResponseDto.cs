namespace Bitexco.Ticket.Application.Features.OrderRefunds.GetOrderRefunds;

using Bitexco.Ticket.Application.Dtos;

public record GetOrderRefundsResponse
{
    public Guid Id { get; set; }
    public string? Code { get; set; }
    public string? RefundCode { get; set; }
    public OrderRefundStatus? RefundStatus { get; set; }
    public string? RefundReason { get; set; }
    public RefundPaymentMethod? RefundPaymentMethod { get; set; }
    public string? RefundPaymentProofFilePath { get; set; }
    public DateTime? RefundPaidAt { get; set; }
    public DateTime? RefundedAt { get; set; }
    public RetailChannelDto? RetailChannel { get; set; }
    public string? RefundStatusDisplay => RefundStatus?.ToString();
}
