namespace Bitexco.Ticket.Application.Features.OrderRefunds.GetOrderRefunds;

/// <summary>
/// Hand<PERSON> for getting paginated list of order refunds with optional filtering
/// </summary>
public class GetOrderRefundsQueryHandler(IApplicationDbContext dbContext) : IRequestHandler<GetOrderRefundsQuery, PaginationResponse<GetOrderRefundsResponse>>
{
    public async Task<PaginationResponse<GetOrderRefundsResponse>> Handle(GetOrderRefundsQuery request, CancellationToken cancellationToken)
    {
        // Get paginated orders with applied filters
        var query = dbContext.Orders
            .AsNoTracking()
            .Include(o => o.RetailChannel)
            .Where(o => o.RefundStatus != null) // Only include orders with a refund status
            .AsQueryable();
            
        // Apply search term filter
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query.Where(o => o.Code.Contains(searchTerm));
        }
        
        if (request.RefundDate.HasValue)
        {
            var refundFrom = request.RefundDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
            var refundTo = request.RefundDate.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);
            query = query.Where(o => o.RefundedAt >= refundFrom && o.RefundedAt <= refundTo);
        }

        if (request.RetailChannelId.HasValue)
        {
            query = query.Where(o => o.RetailChannelId == request.RetailChannelId.Value);
        }

        if (request.RefundStatus.HasValue)
        {
            query = query.Where(o => o.RefundStatus == request.RefundStatus.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = request.SortBy?.ToLower() switch
        {
            "refundat" => request.IsDescending == true ? query.OrderByDescending(o => o.RefundedAt) : query.OrderBy(o => o.RefundedAt),
            "retailchannel" => request.IsDescending == true 
                ? query.OrderByDescending(o => o.RetailChannel != null ? o.RetailChannel.Name : string.Empty) 
                : query.OrderBy(o => o.RetailChannel != null ? o.RetailChannel.Name : string.Empty),
            "finalamount" => request.IsDescending == true ? query.OrderByDescending(o => o.FinalAmount) : query.OrderBy(o => o.FinalAmount),
            "refundstatus" => request.IsDescending == true ? query.OrderByDescending(o => o.RefundStatus) : query.OrderBy(o => o.RefundStatus),
            _ => request.IsDescending == true ? query.OrderByDescending(o => o.RefundedAt) : query.OrderBy(o => o.RefundedAt)
        };

        var orders = await query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Create and return the response
        var response = new PaginationResponse<GetOrderRefundsResponse>
        (
            request.PageIndex,
            request.PageSize,
            totalCount,
            orders.Adapt<List<GetOrderRefundsResponse>>()
        );

        return response;
    }
}
