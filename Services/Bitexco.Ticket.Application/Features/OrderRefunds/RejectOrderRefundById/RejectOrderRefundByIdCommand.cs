namespace Bitexco.Ticket.Application.Features.OrderRefunds.RejectOrderRefundById;

/// <summary>
/// Command to reject an order refund
/// </summary>
public class RejectOrderRefundCommand : IRequest<Response<object>>
{
    /// <summary>
    /// ID of the order to cancel
    /// </summary>
    public Guid OrderId { get; set; }
    
    /// <summary>
    /// Reason for rejecting the refund
    /// </summary>
    public string? RefundRejectionReason { get; set; } = null!;
}

public class RejectOrderRefundCommandValidator : AbstractValidator<RejectOrderRefundCommand>
{
    public RejectOrderRefundCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("Order ID is required");

        RuleFor(x => x.RefundRejectionReason)
            .NotEmpty().WithMessage("Refund rejection reason is required")
            .MaximumLength(500).WithMessage("Refund rejection reason cannot exceed 500 characters");
    }
}
