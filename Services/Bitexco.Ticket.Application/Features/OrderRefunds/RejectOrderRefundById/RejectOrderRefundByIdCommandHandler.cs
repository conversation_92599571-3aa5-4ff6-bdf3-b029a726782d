using BuildingBlocks.Exceptions;

namespace Bitexco.Ticket.Application.Features.OrderRefunds.RejectOrderRefundById;

/// <summary>
/// Hand<PERSON> for rejecting an order refund
/// </summary>
public class RejectOrderRefundCommandHandler(
    IApplicationDbContext dbContext) : IRequestHandler<RejectOrderRefundCommand, Response<object>>
{
    public async Task<Response<object>> Handle(RejectOrderRefundCommand request, CancellationToken cancellationToken)
    {
        // Get the order
        var order = await dbContext.Orders
            .AsTracking()
            .FirstOrDefaultAsync(o => o.Id == request.OrderId, cancellationToken)
            ?? throw new OrderNotFoundException(request.OrderId);

        // Check if the order can be refunded (not already refunded or cancelled)
        if (order.Status == OrderStatus.refunded || order.Status == OrderStatus.cancelled)
        {
            throw new BadRequestException($"Cannot process refund for order with status {order.Status}");
        }

        order.RefundStatus = OrderRefundStatus.rejected_refund;
        order.RefundRejectionReason = request.RefundRejectionReason;

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
        {
            throw new NoEntityUpdatedException("Order", request.OrderId);
        }

        return new Response<object>(true);
    }
}
