namespace Bitexco.Ticket.Application.Features.OrderRefunds.GetOrderRefundById;

/// <summary>
/// Handler for getting order details by ID
/// </summary>
public class GetOrderRefundByIdQueryHandler(IApplicationDbContext dbContext) : IRequestHandler<GetOrderRefundByIdQuery, Response<GetOrderRefundByIdResponse>>
{
    public async Task<Response<GetOrderRefundByIdResponse>> Handle(GetOrderRefundByIdQuery request, CancellationToken cancellationToken)
    {
        // Retrieve order with all related data
        var order = await dbContext.Orders
            .AsNoTracking()
            .Include(o => o.Customer)
            .Include(o => o.Agent)
            .Include(o => o.RetailChannel)
            .Include(o => o.PosUser)
            .Include(o => o.OrderItems)
            .FirstOrDefaultAsync(o => o.Id == request.Id, cancellationToken);

        return new Response<GetOrderRefundByIdResponse>(order.Adapt<GetOrderRefundByIdResponse>());
    }
}
