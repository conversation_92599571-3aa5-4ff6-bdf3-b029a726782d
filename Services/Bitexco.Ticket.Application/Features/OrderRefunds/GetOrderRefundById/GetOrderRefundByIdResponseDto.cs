namespace Bitexco.Ticket.Application.Features.OrderRefunds.GetOrderRefundById;

using Bitexco.Ticket.Application.Dtos;
using Bitexco.Ticket.Application.Features.Orders.GetOrderById;

/// <summary>
/// Detailed response DTO for a single order refund
/// </summary>
public record GetOrderRefundByIdResponse: OrderResponseDto
{
    public string StatusDisplay => Status.ToString();
    public string RefundStatusDisplay => RefundStatus?.ToString() ?? "N/A";
    public CustomerResponseDto? Customer { get; set; }
    public AgentResponseDto? Agent { get; set; }
    public RetailChannelResponseDto RetailChannel { get; set; } = null!;
    public UserDetailDto? PosUser { get; set; }
    public List<OrderItemResponseDto> OrderItems { get; set; } = [];
}
