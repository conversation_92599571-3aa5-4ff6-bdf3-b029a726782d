namespace Bitexco.Ticket.Application.Features.OrderRefunds.ApproveOrderRefundById;

/// <summary>
/// Command to process a refund for an order
/// </summary>
public class ApproveOrderRefundByIdCommand : IRequest<Response<object>>
{
    public Guid OrderId { get; set; }
    public DateTime? RefundPaidAt { get; set; }
    public RefundPaymentMethod? RefundPaymentMethod { get; set; }
    public string? RefundPaymentProofFilePath { get; set; }
}

public class ApproveOrderRefundByIdCommandValidator : AbstractValidator<ApproveOrderRefundByIdCommand>
{
    public ApproveOrderRefundByIdCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("Order ID is required");
    }
}