﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class TicketPriceNotFoundException : NotFoundException
{
    public TicketPriceNotFoundException(Guid id)
        : base(ExceptionMessageHelper.GetMessage(nameof(TicketPriceNotFoundException) + "_" + nameof(id), id))
    {
    }

    public TicketPriceNotFoundException(Guid id, Guid ticketTypeId)
        : base(ExceptionMessageHelper.GetMessage(nameof(TicketPriceNotFoundException) + "_" + nameof(id) + "_" + nameof(ticketTypeId), id, ticketTypeId))
    {
    }
}
