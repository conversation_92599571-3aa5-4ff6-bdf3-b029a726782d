using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class SessionCodeNotFoundException : NotFoundException
{
    public SessionCodeNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(SessionCodeNotFoundException))) { }

    public SessionCodeNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(SessionCodeNotFoundException) + "_" + nameof(id), id)) { }

    public SessionCodeNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(SessionCodeNotFoundException) + "_" + nameof(code), code)) { }
}
