using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class PosDeviceHasActiveShiftsException : BadRequestException
{
    public PosDeviceHasActiveShiftsException() : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceHasActiveShiftsException))) { }

    public PosDeviceHasActiveShiftsException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceHasActiveShiftsException) + "_" + nameof(id), id)) { }

    public PosDeviceHasActiveShiftsException(string name) : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceHasActiveShiftsException) + "_" + nameof(name), name)) { }
}
