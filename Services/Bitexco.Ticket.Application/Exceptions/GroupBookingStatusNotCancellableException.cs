﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;
using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Application.Exceptions;

public class GroupBookingStatusNotCancellableException : LogicException
{
    public GroupBookingStatusNotCancellableException() : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingStatusNotCancellableException))) { }

    public GroupBookingStatusNotCancellableException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingStatusNotCancellableException) + "_" + nameof(id), id)) { }

    public GroupBookingStatusNotCancellableException(GroupBookingStatus status) : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingStatusNotCancellableException) + "_" + nameof(status), status)) { }
}
