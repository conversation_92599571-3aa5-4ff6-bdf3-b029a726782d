﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class AgentNotFoundException : NotFoundException
{
    public AgentNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(AgentNotFoundException))) { }

    public AgentNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(AgentNotFoundException) + "_" + nameof(id), id)) { }

    public AgentNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(AgentNotFoundException) + "_" + nameof(code), code)) { }
}
