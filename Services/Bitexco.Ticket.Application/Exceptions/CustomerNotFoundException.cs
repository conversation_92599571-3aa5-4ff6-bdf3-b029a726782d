using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class CustomerNotFoundException : NotFoundException
{
    public CustomerNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(CustomerNotFoundException)))
    {
    }
    
    public CustomerNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(CustomerNotFoundException) + "_" + nameof(id), id))
    {
    }
}
