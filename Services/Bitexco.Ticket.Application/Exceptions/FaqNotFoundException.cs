﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions
{
    public class FaqNotFoundException : NotFoundException
    {
        public FaqNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(FaqNotFoundException))) { }

        public FaqNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(FaqNotFoundException) + "_" + nameof(id), id)) { }

        public FaqNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(FaqNotFoundException) + "_" + nameof(code), code)) { }
    }
}