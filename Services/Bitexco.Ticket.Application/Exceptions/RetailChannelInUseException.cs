using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class RetailChannelInUseException : BadRequestException
{
    public RetailChannelInUseException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(RetailChannelInUseException) + "_" + nameof(id), id)) { }

    public RetailChannelInUseException(string name) : base(ExceptionMessageHelper.GetMessage(nameof(RetailChannelInUseException) + "_" + nameof(name), name)) { }
}