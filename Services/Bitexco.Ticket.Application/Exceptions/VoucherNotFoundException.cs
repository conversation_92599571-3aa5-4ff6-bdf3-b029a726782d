﻿using Bitexco.Ticket.Application.Services;
using BuildingBlocks.Exceptions;

namespace Bitexco.Ticket.Application.Exceptions
{
    public class VoucherNotFoundException : NotFoundException
    {
        public VoucherNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException)))
        {
        }

        public VoucherNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException) + "_" + nameof(id), id))
        {
        }

        public VoucherNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException) + "_" + nameof(code), code))
        {
        }
    }
}