﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class VisitTimeRuleNotFoundException : NotFoundException
{
    public VisitTimeRuleNotFoundException(Guid visitTimeRuleId)
        : base(ExceptionMessageHelper.GetMessage(nameof(VisitTimeRuleNotFoundException) + "_" + nameof(visitTimeRuleId), visitTimeRuleId))
    {
    }

    public VisitTimeRuleNotFoundException(Guid visitTimeRuleId, Guid ticketTypeId)
        : base(ExceptionMessageHelper.GetMessage(nameof(VisitTimeRuleNotFoundException) + "_" + nameof(visitTimeRuleId) + "_" + nameof(ticketTypeId), visitTimeRuleId, ticketTypeId))
    {
    }
}
