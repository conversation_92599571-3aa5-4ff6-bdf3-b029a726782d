﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class TicketExpiredException : BadRequestException
{
    public TicketExpiredException() : base(ExceptionMessageHelper.GetMessage(nameof(TicketExpiredException))) { }

    public TicketExpiredException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(TicketExpiredException) + "_" + nameof(code), code)) { }
}
