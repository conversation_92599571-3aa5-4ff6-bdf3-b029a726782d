﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class AccountLockedException : BadRequestException
{
    public AccountLockedException() : base(ExceptionMessageHelper.GetMessage(nameof(AccountLockedException))) { }

    public AccountLockedException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(AccountLockedException) + "_" + nameof(id), id)) { }

    public AccountLockedException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(AccountLockedException) + "_" + nameof(code), code)) { }
}
