﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class PaymentTransactionNotFoundException : NotFoundException
{
    public PaymentTransactionNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(PaymentTransactionNotFoundException))) { }

    public PaymentTransactionNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(PaymentTransactionNotFoundException) + "_" + nameof(id), id)) { }

    public PaymentTransactionNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(PaymentTransactionNotFoundException) + "_" + nameof(code), code)) { }
}
