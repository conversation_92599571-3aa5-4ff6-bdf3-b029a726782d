﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class ContractNotFoundException : NotFoundException
{
    public ContractNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(ContractNotFoundException))) { }

    public ContractNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(ContractNotFoundException) + "_" + nameof(id), id)) { }

    public ContractNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(ContractNotFoundException) + "_" + nameof(code), code)) { }
}
