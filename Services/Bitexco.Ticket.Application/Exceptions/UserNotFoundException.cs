using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class UserNotFoundException : NotFoundException
{
    public UserNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(UserNotFoundException)))
    {
    }

    public UserNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(UserNotFoundException) + "_" + nameof(id), id))
    {
    }
}
