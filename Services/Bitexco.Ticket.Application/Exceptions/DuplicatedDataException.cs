using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class DuplicatedDataException : BadRequestException
{
    public DuplicatedDataException() : base(ExceptionMessageHelper.GetMessage(nameof(DuplicatedDataException))) { }

    public DuplicatedDataException(string field) : base(ExceptionMessageHelper.GetMessage(nameof(DuplicatedDataException) + "_" + nameof(field), field)) { }

    public DuplicatedDataException(string field, string value) : base(ExceptionMessageHelper.GetMessage(nameof(DuplicatedDataException) + "_" + nameof(field) + "_" + nameof(value), field, value)) { }
}
