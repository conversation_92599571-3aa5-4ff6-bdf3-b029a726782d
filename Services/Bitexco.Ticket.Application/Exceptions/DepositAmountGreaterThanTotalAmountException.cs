﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

/// <summary>
/// Deposit amount greater than total amount exception.
/// This exception is thrown when the deposit amount exceeds the total amount.
/// </summary>
public class DepositAmountGreaterThanTotalAmountException : BadRequestException
{
    public DepositAmountGreaterThanTotalAmountException() : base(ExceptionMessageHelper.GetMessage(nameof(DepositAmountGreaterThanTotalAmountException))) { }

    public DepositAmountGreaterThanTotalAmountException(decimal deposit, decimal total) : base(ExceptionMessageHelper.GetMessage(nameof(DepositAmountGreaterThanTotalAmountException) + "_" + nameof(deposit) + "_" + nameof(total), deposit, total)) { }
}