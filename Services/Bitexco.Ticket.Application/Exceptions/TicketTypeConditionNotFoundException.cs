﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class TicketTypeConditionNotFoundException : NotFoundException
{
    public TicketTypeConditionNotFoundException(string message)
        : base(ExceptionMessageHelper.GetMessage(nameof(TicketTypeConditionNotFoundException) + "_" + nameof(message), message))
    {
    }

    public TicketTypeConditionNotFoundException(Guid id)
        : base(ExceptionMessageHelper.GetMessage(nameof(TicketTypeConditionNotFoundException) + "_" + nameof(id), id))
    {
    }

    public TicketTypeConditionNotFoundException(Guid id, Guid ticketTypeId)
        : base(ExceptionMessageHelper.GetMessage(nameof(TicketTypeConditionNotFoundException) + "_" + nameof(id) + "_" + nameof(ticketTypeId), id, ticketTypeId))
    {
    }
}
