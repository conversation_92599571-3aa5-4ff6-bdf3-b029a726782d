using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class RetailChannelNotFoundException : NotFoundException
{
    public RetailChannelNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(RetailChannelNotFoundException))) { }

    public RetailChannelNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(RetailChannelNotFoundException) + "_" + nameof(id), id)) { }

    public RetailChannelNotFoundException(string name) : base(ExceptionMessageHelper.GetMessage(nameof(RetailChannelNotFoundException) + "_" + nameof(name), name)) { }
}