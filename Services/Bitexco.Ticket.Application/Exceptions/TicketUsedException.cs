﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class TicketUsedException : BadRequestException
{
    public TicketUsedException() : base(ExceptionMessageHelper.GetMessage(nameof(TicketUsedException))) { }

    public TicketUsedException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(TicketUsedException) + "_" + nameof(code), code)) { }
}
