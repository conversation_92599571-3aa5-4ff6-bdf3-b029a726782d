using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class PosDeviceHasActiveOrdersException : BadRequestException
{
    public PosDeviceHasActiveOrdersException() : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceHasActiveOrdersException))) { }

    public PosDeviceHasActiveOrdersException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceHasActiveOrdersException) + "_" + nameof(id), id)) { }

    public PosDeviceHasActiveOrdersException(string name) : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceHasActiveOrdersException) + "_" + nameof(name), name)) { }
}
