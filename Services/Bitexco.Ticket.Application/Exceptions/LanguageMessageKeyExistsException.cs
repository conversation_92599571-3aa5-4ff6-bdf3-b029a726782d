using Bitexco.Ticket.Application.Services;
using BuildingBlocks.Exceptions;

namespace Bitexco.Ticket.Application.Exceptions
{
    public class LanguageMessageKeyExistsException : BadRequestException 
    {
        public LanguageMessageKeyExistsException(string key)
            : base(ExceptionMessageHelper.GetMessage(nameof(LanguageMessageKeyExistsException) + "_" + nameof(key), key))
        {
        }
    }
}