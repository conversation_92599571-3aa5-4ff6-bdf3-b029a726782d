using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class OrderItemNotFoundException : NotFoundException
{
    public OrderItemNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(OrderItemNotFoundException)))
    {
    }

    public OrderItemNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(OrderItemNotFoundException) + "_" + nameof(id), id))
    {
    }
}
