using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

/// <summary>
/// Exception thrown when SystemConfig value is empty or null
/// </summary>
public class SystemConfigEmptyValueException : BadRequestException
{
    public SystemConfigEmptyValueException() : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigEmptyValueException))) { }
    
    public SystemConfigEmptyValueException(string key) 
        : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigEmptyValueException) + "_" + nameof(key), key)) { }
    
    public SystemConfigEmptyValueException(string key, string message) 
        : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigEmptyValueException) + "_" + nameof(key) + "_" + nameof(message), key, message)) { }
}
