using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class TicketNotFoundException : NotFoundException
{
    public TicketNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(TicketNotFoundException))) { }

    public TicketNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(TicketNotFoundException) + "_" + nameof(id), id)) { }

    public TicketNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(TicketNotFoundException) + "_" + nameof(code), code)) { }
}
