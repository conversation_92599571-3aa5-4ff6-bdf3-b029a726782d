﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class GroupBookingNotFoundException : NotFoundException
{
    public GroupBookingNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingNotFoundException))) { }

    public GroupBookingNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingNotFoundException) + "_" + nameof(id), id)) { }

    public GroupBookingNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingNotFoundException) + "_" + nameof(code), code)) { }
}
