﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class AgentDebtNotFoundException : NotFoundException
{
    public AgentDebtNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(AgentDebtNotFoundException))) { }

    public AgentDebtNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(AgentDebtNotFoundException) + "_" + nameof(id), id)) { }

    public AgentDebtNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(AgentDebtNotFoundException) + "_" + nameof(code), code)) { }
}
