using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

/// <summary>
/// Exception thrown when SystemConfig value contains invalid JSON
/// </summary>
public class SystemConfigInvalidJsonException : BadRequestException
{
    public SystemConfigInvalidJsonException() : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigInvalidJsonException))) { }
    
    public SystemConfigInvalidJsonException(string key) 
        : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigInvalidJsonException) + "_" + nameof(key), key)) { }
    
    public SystemConfigInvalidJsonException(string key, string details) 
        : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigInvalidJsonException) + "_" + nameof(key) + "_" + nameof(details), key, details)) { }
    
    public SystemConfigInvalidJsonException(string key, Exception innerException) 
        : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigInvalidJsonException) + "_" + nameof(key) + "_" + nameof(innerException), key, innerException.Message)) { }
}
