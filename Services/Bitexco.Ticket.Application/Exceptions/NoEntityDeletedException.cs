﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class NoEntityDeletedException : SavingEntityException
{
    // Constructor for Guid-based entities
    public NoEntityDeletedException(string entityName, Guid id) 
        : base(ExceptionMessageHelper.GetMessage(nameof(NoEntityDeletedException) + "_" + nameof(entityName) + "_" + nameof(id), entityName, id))
    {
    }

    // Constructor for string-based entities (like LanguageMessage with string Key)
    public NoEntityDeletedException(string entityName, string key) 
        : base(ExceptionMessageHelper.GetMessage(nameof(NoEntityDeletedException) + "_" + nameof(entityName) + "_key", entityName, key))
    {
    }
}