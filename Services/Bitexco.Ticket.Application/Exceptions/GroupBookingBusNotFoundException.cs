﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class GroupBookingBusNotFoundException : NotFoundException
{
    public GroupBookingBusNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingBusNotFoundException))) { }

    public GroupBookingBusNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingBusNotFoundException) + "_" + nameof(id), id)) { }

    public GroupBookingBusNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(GroupBookingBusNotFoundException) + "_" + nameof(code), code)) { }
}
