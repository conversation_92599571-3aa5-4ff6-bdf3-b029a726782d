﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class NoEntityUpdatedException : SavingEntityException
{
    // Constructor for Guid-based entities
    public NoEntityUpdatedException(string entityName, Guid id) 
        : base(ExceptionMessageHelper.GetMessage(nameof(NoEntityUpdatedException) + "_" + nameof(entityName) + "_" + nameof(id), entityName, id))
    {
    }

    // Constructor for string-based entities (like LanguageMessage with string Key)
    public NoEntityUpdatedException(string entityName, string key) 
        : base(ExceptionMessageHelper.GetMessage(nameof(NoEntityUpdatedException) + "_" + nameof(entityName) + "_key", entityName, key))
    {
    }
}