﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class TicketTypeNotFoundException : NotFoundException
{
    public TicketTypeNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(TicketTypeNotFoundException)))
    {
    }

    public TicketTypeNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(TicketTypeNotFoundException) + "_" + nameof(id), id))
    {
    }
}
