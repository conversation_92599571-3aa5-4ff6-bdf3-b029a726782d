using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class PosRoleNotFoundException : NotFoundException
{
    public PosRoleNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(PosRoleNotFoundException)))
    {
    }

    public PosRoleNotFoundException(string posRole) : base(ExceptionMessageHelper.GetMessage(nameof(PosRoleNotFoundException) + "_" + nameof(posRole), posRole))
    {
    }
}