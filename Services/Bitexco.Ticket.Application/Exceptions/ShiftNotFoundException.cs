using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class ShiftNotFoundException : NotFoundException
{
    public ShiftNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(ShiftNotFoundException))) { }

    public ShiftNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(ShiftNotFoundException) + "_" + nameof(id), id)) { }

    public ShiftNotFoundException(string name) : base(ExceptionMessageHelper.GetMessage(nameof(ShiftNotFoundException) + "_" + nameof(name), name)) { }
}
