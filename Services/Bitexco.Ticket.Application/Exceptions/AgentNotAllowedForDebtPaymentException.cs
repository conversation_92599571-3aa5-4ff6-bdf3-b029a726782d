﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class AgentNotAllowedForDebtPaymentException : LogicException
{
    public AgentNotAllowedForDebtPaymentException() : base(ExceptionMessageHelper.GetMessage(nameof(AgentNotAllowedForDebtPaymentException))) { }

    public AgentNotAllowedForDebtPaymentException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(AgentNotAllowedForDebtPaymentException) + "_" + nameof(code), code)) { }
}
