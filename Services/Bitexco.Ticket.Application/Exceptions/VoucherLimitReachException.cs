﻿using Bitexco.Ticket.Application.Services;
using BuildingBlocks.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bitexco.Ticket.Application.Exceptions
{
    public class VoucherLimitReachException : LogicException
    {
        public VoucherLimitReachException(string code) :
            base($" Voucher {code} usage limit has been reached")
        {
        }
    }
}