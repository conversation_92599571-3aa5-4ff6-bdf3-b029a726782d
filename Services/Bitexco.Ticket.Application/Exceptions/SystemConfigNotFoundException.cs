using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

/// <summary>
/// Exception thrown when a SystemConfig is not found
/// </summary>
public class SystemConfigNotFoundException : NotFoundException
{
    public SystemConfigNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigNotFoundException))) { }
    
    public SystemConfigNotFoundException(string key) : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigNotFoundException) + "_" + nameof(key), key)) { }
    
    public SystemConfigNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigNotFoundException) + "_" + nameof(id), id)) { }
    
    public SystemConfigNotFoundException(string key, string message) : base(ExceptionMessageHelper.GetMessage(nameof(SystemConfigNotFoundException) + "_" + nameof(key) + "_" + nameof(message), key, message)) { }
}
