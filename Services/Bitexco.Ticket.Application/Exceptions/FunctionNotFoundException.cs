using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class FunctionNotFoundException : NotFoundException
{
    public FunctionNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(FunctionNotFoundException)))
    {
    }

    public FunctionNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(FunctionNotFoundException) + "_" + nameof(id), id))
    {
    }
    
    public FunctionNotFoundException(string functionIds) : base(ExceptionMessageHelper.GetMessage(nameof(FunctionNotFoundException) + "_" + nameof(functionIds), functionIds))
    {
    }
}