﻿using Bitexco.Ticket.Application.Services;
using BuildingBlocks.Exceptions;

namespace Bitexco.Ticket.Application.Exceptions
{
    public class PromotionCampaignNotFoundException : NotFoundException
    {
        public PromotionCampaignNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException)))
        {
        }

        public PromotionCampaignNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException) + "_" + nameof(id), id))
        {
        }

        public PromotionCampaignNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException) + "_" + nameof(code), code))
        {
        }
    }
}