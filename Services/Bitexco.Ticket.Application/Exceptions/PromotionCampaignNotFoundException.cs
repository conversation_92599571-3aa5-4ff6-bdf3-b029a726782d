﻿using Bitexco.Ticket.Application.Services;
using BuildingBlocks.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bitexco.Ticket.Application.Exceptions
{
    public class VoucherCampaignNotFoundException : NotFoundException
    {
        public VoucherCampaignNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException)))
        {
        }

        public VoucherCampaignNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException) + "_" + nameof(id), id))
        {
        }

        public VoucherCampaignNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(VoucherNotFoundException) + "_" + nameof(code), code))
        {
        }
    }
}