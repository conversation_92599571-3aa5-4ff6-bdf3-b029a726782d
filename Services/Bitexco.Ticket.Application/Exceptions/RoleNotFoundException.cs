using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class RoleNotFoundException : NotFoundException
{
    public RoleNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(RoleNotFoundException)))
    {
    }

    public RoleNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(RoleNotFoundException) + "_" + nameof(id), id))
    {
    }
    
    public RoleNotFoundException(string name) : base(ExceptionMessageHelper.GetMessage(nameof(RoleNotFoundException) + "_" + nameof(name), name))
    {
    }
}