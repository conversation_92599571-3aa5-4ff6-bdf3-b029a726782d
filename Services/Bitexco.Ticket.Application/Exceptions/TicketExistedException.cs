﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class TicketExistedException : ExistedException
{
    public TicketExistedException() : base(ExceptionMessageHelper.GetMessage(nameof(TicketExistedException))) { }

    public TicketExistedException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(TicketExistedException) + "_" + nameof(id), id)) { }

    public TicketExistedException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(TicketExistedException) + "_" + nameof(code), code)) { }
}
