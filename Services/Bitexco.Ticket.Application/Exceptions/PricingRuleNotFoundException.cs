﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class PricingRuleNotFoundException : NotFoundException
{
    public PricingRuleNotFoundException(string message)
        : base(ExceptionMessageHelper.GetMessage(nameof(PricingRuleNotFoundException) + "_" + nameof(message), message))
    {
    }

    public PricingRuleNotFoundException(Guid id)
        : base(ExceptionMessageHelper.GetMessage(nameof(PricingRuleNotFoundException) + "_" + nameof(id), id))
    {
    }

    public PricingRuleNotFoundException(Guid id, Guid ticketTypeId)
        : base(ExceptionMessageHelper.GetMessage(nameof(PricingRuleNotFoundException) + "_" + nameof(id) + "_" + nameof(ticketTypeId), id, ticketTypeId))
    {
    }
}
