﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class OrderNotFoundException : NotFoundException
{
    public OrderNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(OrderNotFoundException))) { }

    public OrderNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(OrderNotFoundException) + "_" + nameof(id), id)) { }

    public OrderNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(OrderNotFoundException) + "_" + nameof(code), code)) { }
}
