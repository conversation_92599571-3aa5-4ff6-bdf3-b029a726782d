﻿using BuildingBlocks.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bitexco.Ticket.Application.Exceptions
{
    public class VoucherExpiredException : LogicException
    {
        public VoucherExpiredException(string code) : base($"Voucher with code {code} " +
            $"has expired or is not valid at this time.")
        {

        }


    }
}
