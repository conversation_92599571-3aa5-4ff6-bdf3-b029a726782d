using Bitexco.Ticket.Application.Services;
using BuildingBlocks.Exceptions;

namespace Bitexco.Ticket.Application.Exceptions;

/// <summary>
/// Exception thrown when some commission order history records are not found
/// </summary>
public class CommissionOrderHistoryNotFoundException : NotFoundException
{
    public CommissionOrderHistoryNotFoundException(Guid id) :
       base(ExceptionMessageHelper.GetMessage(
           nameof(CommissionOrderHistoryNotFoundException) + "_" + nameof(id),
           id.ToString()))
    { }

    public CommissionOrderHistoryNotFoundException(string missingIds) :
        base(ExceptionMessageHelper.GetMessage(
            nameof(CommissionOrderHistoryNotFoundException) + "_" + nameof(missingIds),
            missingIds))
    { }
}

/// <summary>
/// Exception thrown when trying to process payment for commission order histories that are already paid
/// </summary>
public class CommissionOrderHistoryAlreadyPaidException : LogicException
{
    public CommissionOrderHistoryAlreadyPaidException(string paidRecords) :
        base(ExceptionMessageHelper.GetMessage(
            nameof(CommissionOrderHistoryAlreadyPaidException) + "_" + nameof(paidRecords),
            paidRecords))
    { }
}

/// <summary>
/// Exception thrown when no pending commission order history records found to process payment
/// </summary>
public class NoPendingCommissionOrderHistoryException : LogicException
{
    public NoPendingCommissionOrderHistoryException() :
        base(ExceptionMessageHelper.GetMessage(
            nameof(NoPendingCommissionOrderHistoryException)))
    { }
}

/// <summary>
/// Exception thrown when failed to save commission order history payment confirmations to database
/// </summary>
public class CommissionOrderHistoryPaymentSaveFailedException : LogicException
{
    public CommissionOrderHistoryPaymentSaveFailedException() :
        base(ExceptionMessageHelper.GetMessage(
            nameof(CommissionOrderHistoryPaymentSaveFailedException)))
    { }
}
