using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class PosDeviceNotFoundException : NotFoundException
{
    public PosDeviceNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceNotFoundException))) { }

    public PosDeviceNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceNotFoundException) + "_" + nameof(id), id)) { }

    public PosDeviceNotFoundException(string name) : base(ExceptionMessageHelper.GetMessage(nameof(PosDeviceNotFoundException) + "_" + nameof(name), name)) { }
}
