﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class AgentFoundException : NotFoundException
{
    public AgentFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(AgentFoundException))) { }

    public AgentFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(AgentFoundException) + "_" + nameof(id), id)) { }

    public AgentFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(AgentFoundException) + "_" + nameof(code), code)) { }
}
