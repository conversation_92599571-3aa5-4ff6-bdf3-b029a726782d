﻿using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions
{
    public class InvoiceNotFoundException : NotFoundException
    {
        public InvoiceNotFoundException() : base(ExceptionMessageHelper.GetMessage(nameof(InvoiceNotFoundException))) { }

        public InvoiceNotFoundException(Guid id) : base(ExceptionMessageHelper.GetMessage(nameof(InvoiceNotFoundException) + "_" + nameof(id), id)) { }

        public InvoiceNotFoundException(string code) : base(ExceptionMessageHelper.GetMessage(nameof(InvoiceNotFoundException) + "_" + nameof(code), code)) { }
    }
}