using BuildingBlocks.Exceptions;
using Bitexco.Ticket.Application.Services;

namespace Bitexco.Ticket.Application.Exceptions;

public class EntityNotFoundException : NotFoundException
{
    public EntityNotFoundException(string entityName, object id) : 
        base(ExceptionMessageHelper.GetMessage(
            nameof(EntityNotFoundException) + "_" + nameof(entityName) + "_" + nameof(id), 
            entityName, id))
    {
    }
}
