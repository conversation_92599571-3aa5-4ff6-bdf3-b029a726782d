using AutoFixture;
using Bitexco.Ticket.Application.Features.TicketTypes.Commands;
using Bitexco.Ticket.Domain.Models;
using BuildingBlocks;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Moq.EntityFrameworkCore;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Bitexco.Ticket.Application.UnitTests.Features.TicketTypes.Commands
{
    [TestFixture]
    public class CreateVisitTimeRuleCommandTests
    {
        private Mock<IApplicationDbContext> _mockContext;
        private CreateVisitTimeRuleCommandHandler _handler;
        private Fixture _fixture;
        private List<TicketType> _ticketTypes;
        private List<VisitTimeRule> _visitTimeRules;

        [SetUp]
        public void Setup()
        {
            _fixture = new Fixture();
            _mockContext = new Mock<IApplicationDbContext>();
            
            // Setup mock data
            _ticketTypes = new List<TicketType>
            {
                new TicketType
                {
                    Id = Guid.NewGuid(),
                    Name = "Regular Ticket",
                    Code = "REG",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-10),
                    ModifiedDate = DateTime.Now.AddDays(-5)
                }
            };
            
            _visitTimeRules = new List<VisitTimeRule>();
            
            // Setup mock DbSets
            _mockContext.Setup(c => c.TicketTypes).ReturnsDbSet(_ticketTypes);
            _mockContext.Setup(c => c.VisitTimeRules).ReturnsDbSet(_visitTimeRules);
            
            // Setup Add method to add entities to our list
            _mockContext.Setup(c => c.VisitTimeRules.Add(It.IsAny<VisitTimeRule>()))
                .Callback<VisitTimeRule>(rule => _visitTimeRules.Add(rule));
                
            _handler = new CreateVisitTimeRuleCommandHandler(_mockContext.Object);
        }

        [Test]
        public async Task Handle_WhenTicketTypeExists_ShouldCreateVisitTimeRule()
        {
            // Arrange
            var ticketTypeId = _ticketTypes.First().Id;
            var command = new CreateVisitTimeRuleCommand
            {
                TicketTypeId = ticketTypeId,
                DayOfWeek = DayOfWeek.Monday,
                StartTime = TimeSpan.FromHours(9),
                EndTime = TimeSpan.FromHours(17),
                MaximumEntries = 100
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeEmpty();
            _visitTimeRules.Should().HaveCount(1);
            _visitTimeRules.First().TicketTypeId.Should().Be(ticketTypeId);
            _visitTimeRules.First().DayOfWeek.Should().Be(DayOfWeek.Monday);
            _visitTimeRules.First().StartTime.Should().Be(TimeSpan.FromHours(9));
            _visitTimeRules.First().EndTime.Should().Be(TimeSpan.FromHours(17));
            _visitTimeRules.First().MaximumEntries.Should().Be(100);
            
            _mockContext.Verify(c => c.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Test]
        public async Task Handle_WhenTicketTypeDoesNotExist_ShouldReturnFailure()
        {
            // Arrange
            var nonExistentTicketTypeId = Guid.NewGuid();
            var command = new CreateVisitTimeRuleCommand
            {
                TicketTypeId = nonExistentTicketTypeId,
                DayOfWeek = DayOfWeek.Monday,
                StartTime = TimeSpan.FromHours(9),
                EndTime = TimeSpan.FromHours(17),
                MaximumEntries = 100
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Error.Should().Contain("Ticket type not found");
            _visitTimeRules.Should().BeEmpty();
            
            _mockContext.Verify(c => c.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Test]
        public async Task Handle_WhenEndTimeBeforeStartTime_ShouldReturnFailure()
        {
            // Arrange
            var ticketTypeId = _ticketTypes.First().Id;
            var command = new CreateVisitTimeRuleCommand
            {
                TicketTypeId = ticketTypeId,
                DayOfWeek = DayOfWeek.Monday,
                StartTime = TimeSpan.FromHours(17),
                EndTime = TimeSpan.FromHours(9), // End time before start time
                MaximumEntries = 100
            };

            // Setup validator to run before handler
            var validator = new CreateVisitTimeRuleCommandValidator();
            var validationResult = await validator.ValidateAsync(command);

            // Assert validation fails
            validationResult.IsValid.Should().BeFalse();
            validationResult.Errors.Should().Contain(e => e.ErrorMessage.Contains("End time must be after start time"));
        }
    }
}
