﻿namespace Bitexco.Ticket.Domain.Models;

using Bitexco.Ticket.Domain.Abstractions;
using System;

/// <summary>
/// Represents API authentication credentials for external agent systems
/// Used for secure integration between agent systems and the ticket platform
/// </summary>
public class AgentApiKey : Entity<Guid>
{
    /// <summary>
    /// ID of the agent this API key belongs to
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// The unique API key string used for authentication
    /// Should be AES-256 encrypted in accordance with security requirements
    /// </summary>
    public string ApiKey { get; set; } = null!;

    /// <summary>
    /// The API secret used with the key for authentication
    /// Should be securely hashed in the database
    /// </summary>
    public string ApiSecret { get; set; } = null!; // Should be hashed/encrypted at application level

    /// <summary>
    /// Optional expiration date for the API key
    /// Null indicates no expiration
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Flag indicating if this API key is currently active
    /// </summary>
    public bool IsActive { get; set; } = true; // Default: true

    // Navigation properties
    /// <summary>
    /// Reference to the agent who owns this API key
    /// </summary>
    public virtual Agent Agent { get; set; } = null!;
}
