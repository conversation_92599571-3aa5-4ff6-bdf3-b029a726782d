﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a customer order for tickets
/// Central entity for sales transactions in the system
/// </summary>
public class Order : Entity<Guid>
{
    /// <summary>
    /// Code for this order
    /// Used for quick lookup and reference
    /// </summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// Reference to the customer who placed this order
    /// Can be null for anonymous walk-in customers
    /// </summary>
    public Guid? CustomerId { get; set; }

    /// <summary>
    /// Reference to the agent who created this order
    /// Null if order was placed directly by customer or counter staff
    /// </summary>
    public Guid? AgentId { get; set; }

    /// <summary>
    /// Sales channel through which this order was placed (online, POS, agent, etc.)
    /// </summary>
    public Guid? RetailChannelId { get; set; }

    /// <summary>
    /// Staff member who processed this order at POS counter
    /// </summary>
    public Guid? PosUserId { get; set; }

    /// <summary>
    /// Device used to process this order (if at POS counter)
    /// </summary>
    public Guid? PosDeviceId { get; set; }

    /// <summary>
    /// Voucher applied to this order for discounts
    /// </summary>
    public Guid? VoucherId { get; set; }

    /// <summary>
    /// Reference to the group booking this order is associated with (if any)
    /// </summary>
    public Guid? GroupBookingId { get; set; }

    /// <summary>
    /// Customer type for this order
    /// 'individual', 'corporate', 'ship'
    /// </summary>
    public CustomerType CustomerType { get; set; }

    /// <summary>
    /// Date when the order was placed
    /// </summary>
    public DateOnly OrderDate { get; set; }

    /// <summary>
    /// Time when the order was placed
    /// </summary>
    public TimeOnly OrderTime { get; set; }

    /// <summary>
    /// Total amount before any discounts
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Discount amount (discount by agent)
    /// </summary>
    public decimal DiscountAmount { get; set; } = 0;

    /// <summary>
    ///  Coupon amount applied to this order (voucher)
    /// </summary>
    public decimal CouponAmount { get; set; } = 0;

    /// <summary>
    /// Total deposit amount paid for this order
    ///  Used for orders that require a deposit before full payment
    ///  </summary>
    public decimal DepositAmount { get; set; } = 0;

    /// <summary>
    /// Final amount after discounts
    /// FinalAmount = TotalAmount - DiscountAmount - CouponAmount - DepositAmount
    /// </summary>
    public decimal FinalAmount { get; set; }

    /// <summary>
    /// Current payment status of the order
    /// 'pending_payment', 'paid', 'cancelled', 'refunded'
    /// </summary>
    public OrderStatus Status { get; set; } = OrderStatus.pending_payment;

    /// <summary>
    /// Is payment with debt method
    /// true if payment is done with debt method
    /// </summary>
    public bool IsDebtPayment { get; set; } = false;

    /// <summary>
    /// Current refund status of the order
    /// 'pending_refund', 'refunded', 'rejected_refund'
    /// </summary>
    public OrderRefundStatus? RefundStatus { get; set; }

    /// <summary>
    /// Refund code for this order
    /// This is used to track refunds separately from original order code
    /// </summary>
    public string? RefundCode { get; set; }

    /// <summary>
    /// Refund payment method used for this order
    /// </summary>
    public RefundPaymentMethod? RefundPaymentMethod { get; set; }

     /// <summary>
    /// Timestamp when the refund was paid
    /// </summary>
    public DateTime? RefundPaidAt { get; set; }

    /// <summary>
    /// Refund rejection reason
    /// This is used to store the reason for rejecting a refund request
    /// </summary>
    public string? RefundRejectionReason { get; set; }

    /// <summary>
    /// Refund payment proof file path
    /// This is used to store the proof of payment for refunds
    /// </summary>
    public string? RefundPaymentProofFilePath { get; set; }

    /// <summary>
    /// Reason provided for customer refund request
    /// </summary>
    public string? RefundReason { get; set; }

    /// <summary>
    /// Timestamp when the ticket was refunded
    /// </summary>
    public DateTime? RefundedAt { get; set; }

    /// <summary>
    /// ShiftId associated with this order
    /// This is used to track which shift processed this order
    /// </summary>
    public Guid? ShiftId { get; set; }

    /// <summary>
    /// Reference to the customer who placed this order
    /// </summary>
    public virtual Customer? Customer { get; set; }

    /// <summary>
    /// Reference to the agent who created this order
    /// </summary>
    public virtual Agent? Agent { get; set; }

    /// <summary>
    /// Reference to the retail channel through which this order was placed
    /// </summary>
    public virtual RetailChannel? RetailChannel { get; set; }

    /// <summary>
    /// Reference to the POS staff member who processed this order
    /// </summary>
    public virtual User? PosUser { get; set; }

    /// <summary>
    /// Reference to the POS device used to process this order
    /// </summary>
    public virtual PosDevice? PosDevice { get; set; }

    /// <summary>
    /// Reference to the voucher applied to this order
    /// </summary>
    public virtual Voucher? Voucher { get; set; }

    /// <summary>
    /// Reference to the group booking this order is associated with (if any)
    /// </summary>
    public virtual GroupBooking? GroupBooking { get; set; }

    /// <summary>
    /// Collection of individual items in this order
    /// </summary>
    public virtual ICollection<OrderItem> OrderItems { get; set; } = null!;

    /// <summary>
    /// Collection of payment transactions for this order
    /// </summary>
    public virtual ICollection<PaymentTransaction>? PaymentTransactions { get; set; }

    /// <summary>
    /// Last payment method used for this order
    /// This is used to track the last payment method used for this order
    /// </summary>
    public PaymentTransactionMethod? LastPaymentMethod { get; set; }

    /// <summary>
    /// Invoice generated for this order
    /// </summary>
    public virtual Invoice? Invoice { get; set; }
    
    /// <summary>
    /// Shift associated with this order
    /// This is used to track which shift processed this order
    /// </summary>
    public virtual Shift? Shift { get; set; }
}
