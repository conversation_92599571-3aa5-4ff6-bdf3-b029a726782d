﻿using Bitexco.Ticket.Domain.Enums;

namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a user role in the role-based access control system
/// Roles are assigned to users and contain sets of permissions
/// </summary>
public class Role : Entity<Guid>
{
    /// <summary>
    /// Name of the role
    /// Should be descriptive of the role's responsibilities (e.g., 'Admin', 'Manager', 'Operator')
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Detailed description of the role's purpose and responsibilities
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Type of the role - pos or admin
    /// </summary>
    public RoleType Type { get; set; }

    /// <summary>
    /// Navigation property to users assigned to this role
    /// </summary>
    public virtual ICollection<User>? Users { get; set; }
    
    /// <summary>
    /// Navigation property to permissions assigned to this role
    /// Defines the set of actions users with this role can perform
    /// </summary>
    public virtual ICollection<RolePermission>? RolePermissions { get; set; }
    
    /// <summary>
    /// Navigation property to functions assigned to this role
    /// Defines the set of functions users with this role can access
    /// </summary>
    public virtual ICollection<RoleFunction>? RoleFunctions { get; set; }
}
