﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a sales channel for ticket distribution
/// Defines different ways tickets can be sold to customers
/// </summary>
public class RetailChannel : Entity<Guid>
{
    /// <summary>
    /// Name of the retail channel (e.g., 'Online', 'POS', 'CTV', 'Agent')
    /// Used to categorize sales channels for reporting and analysis
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Indicates whether this retail channel is currently active
    /// Default is true
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property to voucher channels associated with this retail channel
    /// Used to determine which vouchers are valid for this sales channel
    /// </summary>
    public virtual ICollection<VoucherChannel>? VoucherChannels { get; set; }
    
    /// <summary>
    /// Navigation property to orders placed through this retail channel
    /// </summary>
    public virtual ICollection<Order>? Orders { get; set; }
}