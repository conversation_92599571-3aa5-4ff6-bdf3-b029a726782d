﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents an individual ticket in the system
/// Tracks the complete lifecycle of a ticket from creation through usage
/// </summary>
public class Ticket : Entity<Guid>
{
    /// <summary>
    /// Unique code for this ticket
    /// Used for quick lookup and validation
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// Reference to the order item this ticket belongs to
    /// </summary>
    public Guid? OrderItemId { get; set; }

    /// <summary>
    /// Agent who sold this ticket
    /// Denormalized for quick lookup without joining to order
    /// </summary>
    public Guid? AgentId { get; set; }

    /// <summary>
    /// Type of ticket (e.g., adult, child, senior)
    /// </summary>
    public Guid TicketTypeId { get; set; }

    /// <summary>
    /// Reference to the customer who owns this ticket
    /// Denormalized for quick lookup without joining to order
    /// </summary>
    public Guid? CustomerId { get; set; }

    /// <summary>
    /// Current status of the ticket in its lifecycle
    /// </summary>
    public TicketStatus Status { get; set; } = TicketStatus.created;

    /// <summary>
    /// Timestamp when the ticket was sold
    /// </summary>
    public DateTime? SoldAt { get; set; }

    /// <summary>
    /// Timestamp when the ticket was used at the venue
    /// </summary>
    public DateTime? UsedAt { get; set; }

    /// <summary>
    /// Timestamp when the ticket was cancelled
    /// </summary>
    public DateTime? CancelledAt { get; set; }

    /// <summary>
    /// Timestamp when the ticket was refunded
    /// </summary>
    public DateTime? RefundedAt { get; set; }

    /// <summary>
    /// Timestamp when the ticket expired
    /// </summary>
    public DateTime? ExpiredAt { get; set; }

    /// <summary>
    /// Scheduled date for customer visit
    /// </summary>
    public DateOnly? PlannedVisitDate { get; set; }

    /// <summary>
    /// Scheduled time for customer visit
    /// </summary>
    public TimeOnly? PlannedVisitTime { get; set; }

    /// <summary>
    /// Reference to the staff member who validated this ticket
    /// </summary>
    public Guid? ValidatedByUserId { get; set; }

    /// <summary>
    /// Reference to the device used to validate this ticket
    /// </summary>
    public Guid? ValidatedAtDeviceId { get; set; }

    /// <summary>
    /// Number of times this ticket has been reprinted
    /// Used for security monitoring
    /// </summary>
    public int ReprintCount { get; set; } = 0;

    /// <summary>
    /// Timestamp of the most recent reprint
    /// </summary>
    public DateTime? LastReprintedAt { get; set; }

    /// <summary>
    /// Location where this ticket was used for access
    /// </summary>
    public string? AccessLocation { get; set; }

    /// <summary>
    /// JSON-formatted audit trail recording all status changes and operations
    /// Includes timestamps, users, and reasons for changes
    /// </summary>
    public string? AuditInfo { get; set; }

    /// <summary>
    /// PosDevice id where this ticket was validated
    /// </summary>
    public Guid? PosDeviceId { get; set; }

    /// <summary>
    /// Retail channel where this ticket was sold
    /// Used for reporting and analytics
    /// </summary>
    public Guid? RetailChannelId { get; set; }

    /// <summary>
    /// Reference to the retail channel where this ticket was sold
    /// </summary>
    public virtual RetailChannel? RetailChannel { get; set; }
    
    // Navigation properties
    /// <summary>
    /// Reference to the order item this ticket belongs to
    /// </summary>
    public virtual OrderItem? OrderItem { get; set; }

    /// <summary>
    /// Reference to the ticket type (product)
    /// </summary>
    public virtual TicketType TicketType { get; set; } = null!;

    /// <summary>
    /// Reference to the customer who owns this ticket
    /// </summary>
    public virtual Customer? Customer { get; set; }

    /// <summary>
    /// Reference to the user who validated this ticket
    /// </summary>
    public virtual User? ValidatedByUser { get; set; }

    /// <summary>
    /// Reference to the device where this ticket was validated
    /// </summary>
    public virtual PosDevice? ValidatedAtDevice { get; set; }

    /// <summary>
    /// Reference to the agent who sold this ticket
    /// </summary>
    public virtual Agent? Agent { get; set; }

    /// <summary>
    ///  Additional information about the action performed
    /// </summary>
    public virtual ICollection<TicketHistory>? TicketHistories { get; set; } = [];
}