namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents an advance booking for groups (tour groups, cruise ship passengers, etc.)
/// Allows for scheduling visits with deposits and credit-based payments
/// </summary>
public class GroupBooking : Entity<Guid>
{
    /// <summary>
    /// Unique identifier for the group booking
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Reference to the agent who made this booking
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// Type of group booking (e.g., ship, corporate)
    /// </summary>
    public GroupBookingType Type { get; set; }

    /// <summary>
    /// Date when the visit is scheduled
    /// </summary>
    public DateOnly VisitDate { get; set; }

    /// <summary>
    /// Time when the visit is scheduled to begin
    /// </summary>
    public TimeOnly VisitTime { get; set; }

    /// <summary>
    /// Deposit amount paid for this booking
    /// </summary>
    public decimal DepositAmount { get; set; } = 0;

    /// <summary>
    /// Current status of the booking
    /// </summary>
    public GroupBookingStatus Status { get; set; } = GroupBookingStatus.draft;

    /// <summary>
    /// Payment method used for this booking
    /// </summary>
    public PaymentTransactionMethod PaymentMethod { get; set; } 

    /// <summary>
    /// Date when the group checked in
    /// </summary>
    public DateTime? CheckedInAt { get; set; }

    /// <summary>
    /// Reference to the agent who made this booking
    /// </summary>
    public virtual Agent Agent { get; set; } = null!;

    /// <summary>
    /// Orders generated for this group booking
    /// </summary>
    public virtual ICollection<Order>? Orders { get; set; }

    /// <summary>
    /// Collection of buses for this booking
    /// </summary>
    public virtual ICollection<GroupBookingBus> GroupBookingBuses { get; set; } = [];
}
