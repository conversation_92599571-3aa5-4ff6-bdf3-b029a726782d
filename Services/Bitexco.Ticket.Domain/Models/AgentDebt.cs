namespace Bitexco.Ticket.Domain.Models;

using Bitexco.Ticket.Domain.Abstractions;
using Bitexco.Ticket.Domain.Enums;

/// <summary>
/// Represents agent debt management in the system
/// Tracks outstanding debts for agents from their orders for later payment
/// </summary>
public class AgentDebt : Entity<Guid>
{
    /// <summary>
    /// Reference to the agent who owes this debt
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// Reference to the order that generated this debt
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Original debt amount from the order
    /// </summary>
    public decimal OriginalAmount { get; set; }

    /// <summary>
    /// Amount that has been paid towards this debt
    /// </summary>
    public decimal PaidAmount { get; set; } = 0;

    /// <summary>
    /// Current status of the debt
    /// </summary>
    public AgentDebtStatus Status { get; set; } = AgentDebtStatus.outstanding;

    /// <summary>
    /// Payment method used for debt settlement
    /// </summary>
    public AgentDebtPaymentMethod? PaymentMethod { get; set; }

    /// <summary>
    /// Date when the debt was fully paid
    /// </summary>
    public DateTime? PaidAt { get; set; }

    /// <summary>
    /// User who confirmed/processed the debt payment
    /// </summary>
    public Guid? ApprovedByUserId { get; set; }

    /// <summary>
    /// Notes or comments about this debt
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Payment proof file path or reference
    /// </summary>
    public string? PaymentProofFilePath { get; set; }

    /// <summary>
    /// Invoice file path or reference
    /// </summary>
    public string? InvoiceFilePath { get; set; }

    // Navigation properties
    /// <summary>
    /// Reference to the agent who owes this debt
    /// </summary>
    public virtual Agent Agent { get; set; } = null!;

    /// <summary>
    /// Reference to the order that generated this debt
    /// </summary>
    public virtual Order Order { get; set; } = null!;

    /// <summary>
    /// Reference to the user who approved the debt payment
    /// </summary>
    public virtual User? ApprovedByUser { get; set; }
}
