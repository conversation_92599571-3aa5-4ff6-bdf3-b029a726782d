using System;

namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents an automated task scheduled to run periodically
/// Manages background jobs like report generation, data cleanup, and system maintenance
/// </summary>
public class ScheduledTask : Entity<Guid>
{
    /// <summary>
    /// Name of the scheduled task
    /// Used to identify the task in logs and interfaces
    /// </summary>
    public string TaskName { get; set; } = null!;
    
    /// <summary>
    /// Detailed description of what the task does
    /// </summary>
    public string TaskDescription { get; set; } = null!;
    
    /// <summary>
    /// Category or type of task (e.g., report generation, system maintenance)
    /// Used for filtering and grouping tasks
    /// </summary>
    public string TaskType { get; set; } = null!;
    
    /// <summary>
    /// Cron expression defining when the task should run
    /// Follows standard cron syntax (e.g., "0 0 * * *" for daily at midnight)
    /// </summary>
    public string Schedule { get; set; } = null!;
    
    /// <summary>
    /// Indicates whether the task is currently active and will be executed according to schedule
    /// Default is true
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// Date and time when the task was last executed
    /// Null if the task has never run
    /// </summary>
    public DateTime? LastRunTime { get; set; }
    
    /// <summary>
    /// Status of the last execution (Success, Failed, Partial)
    /// Null if the task has never run
    /// </summary>
    public string? LastRunStatus { get; set; }
    
    /// <summary>
    /// Detailed results or error messages from the last execution
    /// Used for troubleshooting issues with the task
    /// </summary>
    public string? LastRunResult { get; set; }
    
    /// <summary>
    /// Calculated next execution time based on the schedule
    /// Updated after each execution or when the schedule changes
    /// </summary>
    public DateTime? NextRunTime { get; set; }
    
    /// <summary>
    /// JSON serialized parameters for task execution
    /// Contains task-specific configuration settings
    /// </summary>
    public string? Parameters { get; set; }
}
