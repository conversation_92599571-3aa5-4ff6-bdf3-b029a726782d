﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a category or type of ticket that can be sold
/// Examples include Adult, Child, Senior, or Special Offer tickets
/// </summary>
public class TicketType : Entity<Guid>
{
    /// <summary>
    /// Name of the ticket type
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Code or identifier for this ticket type
    /// This is used for quick reference and may be displayed to users
    ///  </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of this ticket type
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Icon or image representing this ticket type
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Default price for this ticket type
    /// This is used when no specific price points are defined
    ///  </summary>
    public decimal DefaultPrice { get; set; } = 0;

    /// <summary>
    /// Ticket code prefix for this ticket type
    /// This is used to generate unique ticket codes
    /// </summary>
    public string TicketCodePrefix { get; set; } = string.Empty;

    /// <summary>
    /// Number character length of the ticket code after the prefix
    /// </summary>
    public int TicketCodeLengthAfterPrefix { get; set; } = 8;

    /// <summary>
    /// Ticket expiration time in days
    /// This defines how long tickets of this type remain valid after purchase
    /// </summary>
    public int? TicketExpirationAfterDurations { get; set; }

    /// <summary>
    /// Duration unit for ticket expiration
    /// This defines the unit of time for ticket expiration (e.g., days, hours)
    /// </summary>
    public TicketExpirationDurationUnit? TicketExpirationDurationUnit { get; set; } 

    /// <summary>
    /// Ticket expiration from specific date
    /// This defines how long tickets of this type remain valid after a specific date
    ///  </summary>
    public DateOnly? TicketExpirationFromDate { get; set; }

    /// <summary>
    /// Ticket expiration to specific date
    /// This defines how long tickets of this type remain valid after a specific date
    /// </summary>
    public DateOnly? TicketExpirationToDate { get; set; }

    /// <summary>
    /// Flag indicating if this ticket type is currently available for sale
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Collection of price points for this ticket type
    /// Multiple prices may exist for different time periods or rules
    /// </summary>
    public virtual ICollection<TicketPrice>? TicketPrices { get; set; }
    
    /// <summary>
    /// Collection of order items that have purchased this ticket type
    /// </summary>
    public virtual ICollection<OrderItem>? OrderItems { get; set; }
    
    /// <summary>
    /// Collection of individual tickets of this type
    /// </summary>
    public virtual ICollection<Ticket>? Tickets { get; set; }
    
    /// <summary>
    /// Collection of specific conditions for this ticket type
    /// </summary>
    public virtual ICollection<TicketTypeCondition>? Conditions { get; set; }
    
    /// <summary>
    /// Collection of time rules for this ticket type
    /// </summary>
    public virtual ICollection<VisitTimeRule>? VisitTimeRules { get; set; }
    
    /// <summary>
    /// Collection of price approval requests for this ticket type
    /// </summary>
    public virtual ICollection<PriceApproval>? PriceApprovals { get; set; }
}