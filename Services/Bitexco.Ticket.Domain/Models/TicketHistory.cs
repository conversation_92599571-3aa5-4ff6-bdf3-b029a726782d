namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Entity for tracking ticket status changes
/// </summary>
public class TicketHistory : Entity<Guid>
{
    /// <summary>
    /// ID of the ticket this history record belongs to
    /// </summary>
    public Guid TicketId { get; set; }
    
    /// <summary>
    /// Reference to the ticket
    /// </summary>
    public Ticket Ticket { get; set; } = null!;
    
    /// <summary>
    /// Status before the change (null for initial creation)
    /// </summary>
    public TicketStatus? FromStatus { get; set; }
    
    /// <summary>
    /// Status after the change
    /// </summary>
    public TicketStatus ToStatus { get; set; }
    
    /// <summary>
    /// When the change occurred
    /// </summary>
    public DateTime ChangedAt { get; set; }
    
    /// <summary>
    /// User ID that made the change (if applicable)
    /// </summary>
    public Guid? ChangedByUserId { get; set; }
    
    /// <summary>
    /// Device ID where the change occurred (if applicable)
    /// </summary>
    public Guid? ChangedAtDeviceId { get; set; }
    
    /// <summary>
    /// Location where the change occurred (if applicable)
    /// </summary>
    public string? Location { get; set; }
}
