﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents the relationship between Role and Function with permission type
/// This entity does not inherit from Entity<T> as it's a junction table
/// </summary>
public class RoleFunction
{
    /// <summary>
    /// Foreign key to Role
    /// </summary>
    public Guid RoleId { get; set; }
    
    /// <summary>
    /// Foreign key to Function
    /// </summary>
    public Guid FunctionId { get; set; }
    
    /// <summary>
    /// When this role-function assignment was created
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// Navigation property to Role
    /// </summary>
    public virtual Role Role { get; set; } = null!;
    
    /// <summary>
    /// Navigation property to Function
    /// </summary>
    public virtual Function Function { get; set; } = null!;
    
    /// <summary>
    /// Type of permission for this role-function relationship
    /// </summary>
    public string? PermissionType { get; set; } = null!;
}
