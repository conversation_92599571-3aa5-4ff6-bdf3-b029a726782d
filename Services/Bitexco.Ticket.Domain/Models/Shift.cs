﻿using System;

namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a staff work shift at a point of sale (POS) device
/// Tracks cash handling, sales activities, and shift timing
/// </summary>
public class Shift : Entity<Guid>
{
    /// <summary>
    /// Reference to the user (staff member) who is working this shift
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Reference to the POS device being used during this shift
    /// </summary>
    public Guid PosDeviceId { get; set; }

    /// <summary>
    /// Date and time when the shift started
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Date and time when the shift ended
    /// Null for ongoing shifts
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// Cash balance at the beginning of the shift
    /// Used for cash drawer reconciliation
    /// Default is 0
    /// </summary>
    public decimal StartCashBalance { get; set; } = 0;

    /// <summary>
    /// Cash balance at the end of the shift
    /// Used for cash drawer reconciliation
    /// Null for ongoing shifts
    /// </summary>
    public decimal? EndCashBalance { get; set; }

    /// <summary>
    /// Total sales amount paid in cash during this shift
    /// Default is 0
    /// </summary>
    public decimal TotalCashSales { get; set; } = 0;

    /// <summary>
    /// Total sales amount paid by card/POS during this shift
    /// Default is 0
    /// </summary>
    public decimal TotalPosSales { get; set; } = 0;

    /// <summary>
    /// Total sales amount paid by bank transfer during this shift
    /// Default is 0
    /// </summary>
    public decimal TotalTransferSales { get; set; } = 0;

    /// <summary>
    /// Current status of the shift (open or closed)
    /// Default is open
    /// </summary>
    public ShiftStatus Status { get; set; } = ShiftStatus.open;

    /// <summary>
    /// Notes or comments from shift auditing
    /// Used to document discrepancies or issues
    /// </summary>
    public string? AuditDetails { get; set; }

    /// <summary>
    /// Navigation property to the user working this shift
    /// </summary>
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// Navigation property to the POS device used during this shift
    /// </summary>
    public virtual PosDevice PosDevice { get; set; } = null!;

    /// <summary>
    /// Navigation property to all orders processed during this shift
    /// </summary>
    public virtual ICollection<Order> Orders { get; set; } = [];
}
