namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents time-based rules for ticket usage and validation
/// Defines when tickets can be used and any time-based restrictions
/// </summary>
public class VisitTimeRule : Entity<Guid>
{
    /// <summary>
    /// Reference to the ticket type this time rule applies to
    /// </summary>
    public Guid TicketTypeId { get; set; }

    /// <summary>
    /// Name/title of the time rule (e.g., "Morning Slot", "Weekend")
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Earliest time when visit can start
    /// </summary>
    public TimeOnly? EarliestEntryTime { get; set; }

    /// <summary>
    /// Latest time when visit must start
    /// </summary>
    public TimeOnly? LatestEntryTime { get; set; }

    /// <summary>
    /// Maximum duration of visit in minutes
    /// null means no time limit
    /// </summary>
    public int? MaxDurationMinutes { get; set; }

    /// <summary>
    /// Days of the week when this rule applies (0=Sunday, 1=Monday, etc.)
    /// Empty list indicates the rule applies to all days
    /// </summary>
    public List<int> DaysOfWeek { get; set; } = [];

    /// <summary>
    /// Flag indicating if this time rule applies to holidays
    /// </summary>
    public bool AppliesToHolidays { get; set; } = false;
    
    /// <summary>
    /// Flag indicating if this time rule is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Reference to the associated ticket type
    /// </summary>
    public virtual TicketType TicketType { get; set; } = null!;
}
