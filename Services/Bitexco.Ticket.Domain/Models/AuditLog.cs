﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Records all user actions in the system for security and compliance purposes
/// Provides a comprehensive audit trail for data changes and access
/// </summary>
public class AuditLog : Entity<Guid>
{
    /// <summary>
    /// Reference to the user who performed the action
    /// Can be null for system-generated actions
    /// </summary>
    public Guid? UserId { get; set; } // User performing the action

    /// <summary>
    /// Type of action performed (create, update, delete, login, logout)
    /// </summary>
    public AuditLogActionType ActionType { get; set; } = AuditLogActionType.create;// 'create', 'update', 'delete', 'login', 'logout'

    /// <summary>
    /// Name of the entity or table that was affected
    /// </summary>
    public string EntityType { get; set; } = string.Empty;// Table or entity name affected

    /// <summary>
    /// ID of the record that was affected by this action
    /// </summary>
    public Guid? EntityId { get; set; } // ID of the affected entity

    /// <summary>
    /// JSON representation of the entity's state before the change
    /// Used for comparing before/after values
    /// </summary>
    public string? OldValue { get; set; }

    /// <summary>
    /// JSON representation of the entity's state after the change
    /// </summary>
    public string? NewValue { get; set; }

    /// <summary>
    /// IP address from which the action was performed
    /// Used for security tracking
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// Exact timestamp when the action occurred
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Reference to the user who performed the action
    /// </summary>
    public virtual User? User { get; set; }
}
