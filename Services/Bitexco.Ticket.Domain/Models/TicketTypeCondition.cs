namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a specific condition or restriction for a ticket type
/// Examples include age restrictions, audience types, or special requirements
/// </summary>
public class TicketTypeCondition : Entity<Guid>
{
    /// <summary>
    /// Reference to the ticket type this condition applies to
    /// </summary>
    public Guid TicketTypeId { get; set; }

    /// <summary>
    /// Name/title of the condition (e.g., "Age Restriction", "Group Size")
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Detailed description of the condition
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///  Type of condition being applied
    ///  /// This could be based on age, time range, group size, etc.
    /// </summary>
    public TicketTypeConditionType Type { get; set; } = TicketTypeConditionType.TimeRange;

    /// <summary>
    /// Optional minimum value for numerical conditions (like minimum age)
    /// </summary>
    public string? FromValue { get; set; }

    /// <summary>
    /// Optional maximum value for numerical conditions (like maximum age)
    /// </summary>
    public string? ToValue { get; set; }

    /// <summary>
    /// Display order for the condition when showing multiple conditions
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Flag indicating if this condition is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Reference to the associated ticket type
    /// </summary>
    public virtual TicketType TicketType { get; set; } = null!;
}
