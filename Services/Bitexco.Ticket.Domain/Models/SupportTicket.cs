﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a customer support ticket/case
/// Tracks customer inquiries, issues, and their resolution process
/// </summary>
public class SupportTicket : Entity<Guid>
{
    /// <summary>
    /// Reference to the customer who created this support ticket
    /// Null for anonymous tickets
    /// </summary>
    public Guid? CustomerId { get; set; }

    /// <summary>
    /// Brief summary of the support issue
    /// </summary>
    public string Subject { get; set; } = null!;

    /// <summary>
    /// Detailed description of the support issue
    /// Provided by the customer when creating the ticket
    /// </summary>
    public string Description { get; set; } = null!;

    /// <summary>
    /// Current status of the support ticket (open, in_progress, resolved, closed)
    /// Default is open
    /// </summary>
    public SupportTicketStatus Status { get; set; } = SupportTicketStatus.open;

    /// <summary>
    /// Priority level of this support ticket (low, medium, high, urgent)
    /// Determines how quickly the ticket should be addressed
    /// Default is low
    /// </summary>
    public SupportTicketPriority Priority { get; set; } = SupportTicketPriority.low;

    /// <summary>
    /// Reference to the support staff user assigned to handle this ticket
    /// Null when unassigned
    /// </summary>
    public Guid? AssignedToUserId { get; set; }

    /// <summary>
    /// Date and time when the issue was resolved
    /// Null for unresolved tickets
    /// </summary>
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// Navigation property to the customer who created this ticket
    /// </summary>
    public virtual Customer? Customer { get; set; }

    /// <summary>
    /// Navigation property to the user assigned to handle this ticket
    /// </summary>
    public virtual User? AssignedToUser { get; set; }

    /// <summary>
    /// Navigation property to messages exchanged in this support ticket
    /// </summary>
    public virtual ICollection<SupportTicketMessage>? SupportTicketMessages { get; set; }
}
