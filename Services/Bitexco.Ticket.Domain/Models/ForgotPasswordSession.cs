﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a forgot password session for secure password reset process
/// Each session has a limited lifetime and can only be used once
/// </summary>
public class ForgotPasswordSession : Entity<Guid>
{
    /// <summary>
    /// Reference to the user who requested password reset
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Date and time when the session expires
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Date and time when the session was successfully verified/used
    /// Null means the session hasn't been used yet
    /// </summary>
    public DateTime? VerifiedAt { get; set; }

    // Navigation properties
    /// <summary>
    /// Reference to the user entity
    /// </summary>
    public virtual User? User { get; set; }
}
