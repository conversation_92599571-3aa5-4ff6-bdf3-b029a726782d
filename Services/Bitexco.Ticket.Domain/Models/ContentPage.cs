﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a content page for the website
/// Used for managing static content like About Us, Terms and Conditions, Privacy Policy, etc.
/// </summary>
public class ContentPage : Entity<Guid>
{
    /// <summary>
    /// Title of the content page
    /// Displayed as the page heading
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// URL-friendly version of the title
    /// Used in the page URL for SEO optimization
    /// </summary>
    public string Slug { get; set; } = string.Empty;

    /// <summary>
    /// The main content of the page in HTML format
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Current publication status of the page (draft, published, archived)
    /// Default is draft
    /// </summary>
    public ContentPageStatus Status { get; set; } = ContentPageStatus.draft;
}
