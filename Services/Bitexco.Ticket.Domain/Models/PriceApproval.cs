namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents the approval workflow for ticket price changes
/// Tracks price change proposals and their approval status
/// </summary>
public class PriceApproval : Entity<Guid>
{
    /// <summary>
    /// Reference to the ticket type this price approval is for
    /// </summary>
    public Guid TicketTypeId { get; set; }

    /// <summary>
    /// Current status of the approval request
    /// </summary>
    public PriceApprovalStatus Status { get; set; } = PriceApprovalStatus.pending;

    /// <summary>
    /// JSON representation of the proposed price changes
    /// Stores both old and new values for comparison
    /// </summary>
    public string ProposedChanges { get; set; } = null!;

    /// <summary>
    /// Optional comments provided with the approval request
    /// </summary>
    public string? RequestComments { get; set; }

    /// <summary>
    /// User who requested the price change
    /// </summary>
    public Guid RequestedByUserId { get; set; }

    /// <summary>
    /// Date and time when the price change was requested
    /// </summary>
    public DateTime RequestedAt { get; set; }

    /// <summary>
    /// User who approved or rejected the price change
    /// </summary>
    public Guid? ApprovedByUserId { get; set; }

    /// <summary>
    /// Date and time when the price change was approved or rejected
    /// </summary>
    public DateTime? ApprovedAt { get; set; }

    /// <summary>
    /// Comments provided by the approver
    /// </summary>
    public string? ApprovalComments { get; set; }

    /// <summary>
    /// Scheduled date for when the approved prices should take effect
    /// </summary>
    public DateOnly? ScheduledEffectiveDate { get; set; }

    // Navigation properties
    /// <summary>
    /// Reference to the associated ticket type
    /// </summary>
    public virtual TicketType TicketType { get; set; } = null!;
    
    /// <summary>
    /// Reference to the user who requested the price change
    /// </summary>
    public virtual User RequestedByUser { get; set; } = null!;
    
    /// <summary>
    /// Reference to the user who approved or rejected the price change
    /// </summary>
    public virtual User? ApprovedByUser { get; set; }
}
