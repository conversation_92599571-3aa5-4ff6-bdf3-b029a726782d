﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a line item in an order
/// Each item corresponds to a specific ticket type and quantity
/// </summary>
public class OrderItem : Entity<Guid>
{
    /// <summary>
    /// Reference to the parent order
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Type of ticket being purchased
    /// </summary>
    public Guid TicketTypeId { get; set; }

    /// <summary>
    /// Name of the ticket type being purchased
    /// This is the product or service associated with this order item
    /// </summary>
    public string TicketTypeName { get; set; } = null!;

    /// <summary>
    /// Number of tickets of this type being purchased
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Price per ticket at time of purchase
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Total price for this line item (Quantity * UnitPrice)
    /// </summary>
    public decimal SubTotal { get; set; }

    /// <summary>
    /// Planned date for the visit
    /// </summary>
    public DateOnly VisitDate { get; set; }

    /// <summary>
    /// Planned time for the visit
    /// </summary>
    public TimeOnly VisitTime { get; set; }

    // Navigation properties
    /// <summary>
    /// Reference to the parent order
    /// </summary>
    public virtual Order Order { get; set; } = null!;

    /// <summary>
    /// Reference to the ticket type being purchased
    /// </summary>
    public virtual TicketType TicketType { get; set; } = null!;

    /// <summary>
    /// Collection of individual tickets generated for this line item
    /// </summary>
    public virtual ICollection<Ticket>? Tickets { get; set; }
}
