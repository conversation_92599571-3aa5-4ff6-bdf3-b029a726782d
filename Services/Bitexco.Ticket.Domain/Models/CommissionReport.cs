﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a periodic commission report for an agent
/// Contains calculated commission amounts, approval status, and payment details for a specified date range
/// </summary>
public class CommissionReport : Entity<Guid>
{
    /// <summary>
    /// Reference to the agent this commission report applies to
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// Start date of the reporting period
    /// </summary>
    public DateOnly StartDate { get; set; } // .NET 6+ DateOnly

    /// <summary>
    /// End date of the reporting period
    /// </summary>
    public DateOnly EndDate { get; set; } // .NET 6+ DateOnly

    /// <summary>
    /// Total sales amount for this reporting period
    /// Used to calculate the commission
    /// </summary>
    public decimal TotalSalesAmount { get; set; }

    /// <summary>
    /// Calculated commission amount based on the agent's commission policy
    /// </summary>
    public decimal CalculatedCommissionAmount { get; set; }

    /// <summary>
    /// Current status of this commission report (generated, approved, reject, paid)
    /// Default is generated
    /// </summary>
    public CommissionReportStatus Status { get; set; } = CommissionReportStatus.generated; // 'generated', 'approved', 'reject', 'paid'

    /// <summary>
    /// Date and time when this report was approved
    /// Null if the report has not been approved yet
    /// </summary>
    public DateTime? ApprovedAt { get; set; }

    /// <summary>
    /// Reference to the user who approved this commission report
    /// Null if the report has not been approved yet
    /// </summary>
    public Guid? ApprovedByUserId { get; set; }

    /// <summary>
    /// Navigation property to the agent this report applies to
    /// </summary>
    public virtual Agent Agent { get; set; } = null!;

    /// <summary>
    /// Navigation property to the user who approved this report
    /// </summary>
    public virtual User ApprovedByUser { get; set; } = null!;

    /// <summary>
    /// Collection of commission payments associated with this report
    /// </summary>
    public virtual ICollection<CommissionPayment>? CommissionPayments { get; set; }
}
