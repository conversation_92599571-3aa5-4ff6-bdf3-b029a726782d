﻿namespace Bitexco.Ticket.Domain.Models;

using Bitexco.Ticket.Domain.Abstractions;
using System;
using System.Collections.Generic;

/// <summary>
/// Represents a ticket sales agent or agency in the system
/// Supports both individual sellers and multi-level agent hierarchies
/// </summary>
public class Agent : Entity<Guid>
{
    /// <summary>
    /// Unique identifier for the agent
    /// summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// The agent's business name or full name for individuals
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Primary contact person name for this agent
    /// </summary>
    public string? Contact<PERSON>erson { get; set; }

    /// <summary>
    /// Tax identification code for financial reporting
    /// </summary>
    public string? TaxCode { get; set; }

    /// <summary>
    /// Contact phone number for the agent
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Contact email address for the agent
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Primary contact person position
    /// </summary>
    public string? ContactPosition { get; set; }

    /// <summary>
    /// Có cho phép thanh toán bằng hình thức công nợ
    /// </summary>
    public bool IsDebtAllowed { get; set; }

    /// <summary>
    /// Có chiếc khấu thẳng vào giá vé thay vì hoa hồng
    /// </summary>
    public bool IsDirectDiscount { get; set; } = false;

    /// <summary>
    /// Commission rate as a percentage of sales
    /// Used for percentage-based commission calculations
    /// </summary>
    public decimal? CommissionRatePercentage { get; set; }

    /// <summary>
    /// Fixed commission amount per sale or transaction
    /// Used for fixed-amount commission calculations
    /// </summary>
    public decimal? FixedCommissionAmount { get; set; }

    /// <summary>
    /// SAP code
    /// </summary>
    public string? SapCode { get; set; }

    /// <summary>
    /// Physical address for the agent
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Maximum number of tickets this agent can sell in total (null = unlimited)
    /// </summary>
    public int? TicketSaleLimit { get; set; }
    
    /// <summary>
    /// Maximum number of tickets this agent can sell per month (null = unlimited)
    /// </summary>
    public int? MonthlyTicketLimit { get; set; }
    
    /// <summary>
    /// Number of tickets sold in the current month for limit tracking
    /// </summary>
    public int CurrentMonthTicketsSold { get; set; } = 0;
    
    /// <summary>
    /// Date when the monthly limit was last reset (typically first day of month)
    /// </summary>
    public DateOnly? LastLimitResetDate { get; set; }

    /// <summary>
    /// Agent type
    /// </summary>
    public AgentType Type { get; set; } = AgentType.collaborator; // "individual", "agency"
    
    /// <summary>
    /// Current approval status of the agent
    /// </summary>
    public AgentApproveStatus ApprovalStatus { get; set; } = AgentApproveStatus.pending; // "pending", "approved", "suspended"
    
    /// <summary>
    /// Reason for suspension if agent has been suspended
    /// </summary>
    public string? SuspensionReason { get; set; }

    /// <summary>
    /// ID of the parent agent in a multi-level agency structure
    /// </summary>
    public Guid? ParentAgentId { get; set; } // Supports multi-level agents

    /// <summary>
    /// Bank name
    /// </summary>
    public string? BankName { get; set; }

    /// <summary>
    /// Bank account name
    /// </summary>
    public string? BankAccountName { get; set; }

    /// <summary>
    /// Bank account number
    /// </summary>
    public string? BankAccountNumber { get; set; }

    /// <summary>
    /// Flag indicating if this agent account is active
    /// </summary>
    public bool IsActive { get; set; } = true; // Default: true

    // Navigation properties
    /// <summary>
    /// Reference to the parent agent in a multi-level hierarchy
    /// </summary>
    public virtual Agent? ParentAgent { get; set; }
    
    /// <summary>
    /// Collection of child agents under this agent in a multi-level hierarchy
    /// </summary>
    public virtual ICollection<Agent>? ChildAgents { get; set; } // For ParentAgentId relationship

    /// <summary>
    /// Collection of orders created by this agent
    /// </summary>
    public virtual ICollection<Order>? Orders { get; set; }
    
    /// <summary>
    /// Collection of API keys assigned to this agent for system integration
    /// </summary>
    public virtual ICollection<AgentApiKey>? AgentApiKeys { get; set; }
    
    /// <summary>
    /// Collection of commission policies applicable to this agent
    /// </summary>
    public virtual ICollection<CommissionPolicy>? CommissionPolicies { get; set; }
    
    /// <summary>
    /// Collection of commission reports generated for this agent
    /// </summary>
    public virtual ICollection<CommissionReport>? CommissionReports { get; set; }
    
    /// <summary>
    /// Collection of commission payments made to this agent
    /// </summary>
    public virtual ICollection<CommissionPayment>? CommissionPayments { get; set; }
    
    /// <summary>
    /// Collection of debts owed by this agent
    /// </summary>
    public virtual ICollection<AgentDebt>? AgentDebts { get; set; }
    
    /// <summary>
    /// Collection of contracts associated with this agent
    /// </summary>
    public virtual ICollection<Contract>? Contracts { get; set; }
}
