﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a configuration setting for the system
/// Provides flexible, database-stored configuration values that can be modified without application redeployment
/// </summary>
public class SystemConfig : Entity<Guid>
{
    /// <summary>
    /// Unique identifier key for the configuration setting
    /// Used to look up the configuration value programmatically
    /// </summary>
    public string Key { get; set; } = null!;

    /// <summary>
    /// The configuration value
    /// Can be a simple value, JSON string, or other structured data
    /// </summary>
    public string Value { get; set; } = null!;

    /// <summary>
    /// Human-readable description of this configuration setting
    /// Explains its purpose and possible values
    /// </summary>
    public string? Description { get; set; }
}
