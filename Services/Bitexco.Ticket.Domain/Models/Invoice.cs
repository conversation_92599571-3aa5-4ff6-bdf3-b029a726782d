﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a financial invoice issued for an order
/// Tracks invoicing status and revisions for compliance
/// </summary>
public class Invoice : Entity<Guid>
{
    /// <summary>
    /// Unique invoice code/number for reference
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// Reference to the order this invoice is for
    /// One-to-one relationship between order and invoice
    /// </summary>
    public Guid OrderId { get; set; } // One invoice per order

    /// <summary>
    /// Customer ID for whom the invoice is issued
    /// Denormalized from order for quick lookup
    /// </summary>
    public Guid? CustomerId { get; set; }

    /// <summary>
    /// Date when the invoice was issued
    /// </summary>
    public DateOnly IssueDate { get; set; }

    /// <summary>
    /// Total invoice amount
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Current status of the invoice
    /// </summary>
    public InvoiceStatus Status { get; set; }

    /// <summary>
    /// Email status of the invoice
    /// Indicates if the invoice has been emailed to the customer
    /// unsent = 0, sent = 1, failed = 2
    /// </summary>
    public SendMailStatus SendMailStatus { get; set; }

    /// <summary>
    /// Date and time when the email was sent
    /// Nullable if not sent yet
    /// </summary>
    public DateTime? EmailSentAt { get; set; } // Nullable if not sent

    /// <summary>
    /// the receiver's email address
    /// This is the email to which the invoice was sent
    /// </summary>
    public string? EmailAddress { get; set; }

    /// <summary>
    /// Reference to the user who issued this invoice
    /// </summary>
    public Guid? IssuedByUserId { get; set; } // User who created the invoice

    /// <summary>
    /// Reason provided if the invoice was cancelled
    /// </summary>
    public string? CancellationReason { get; set; }

    /// <summary>
    /// Details of any revisions made to this invoice
    /// </summary>
    public string? RevisionDetails { get; set; }

    /// <summary>
    /// Link to the pdf file of the invoice
    /// This can be a URL or file path depending on storage
    /// </summary>
    public string? PdfFilePath { get; set; }

    // Navigation properties
    /// <summary>
    /// Reference to the order this invoice belongs to
    /// </summary>
    public virtual Order Order { get; set; } = null!;

    /// <summary>
    /// Reference to the customer for whom this invoice was issued
    /// </summary>
    public virtual Customer? Customer { get; set; }

    /// <summary>
    /// Reference to the user who issued this invoice
    /// </summary>
    public virtual User? IssuedByUser { get; set; }
}
