namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a notification sent to users or customers
/// Supports multiple delivery channels (email, SMS, app, system) and tracks delivery status
/// </summary>
public class Notification : Entity<Guid>
{
    /// <summary>
    /// Subject or title of the notification
    /// </summary>
    public string Subject { get; set; } = null!;

    /// <summary>
    /// Main content of the notification
    /// May include placeholders that are replaced during sending
    /// </summary>
    public string Content { get; set; } = null!;

    /// <summary>
    /// Delivery channel for the notification (email, sms, app, system)
    /// Default is email
    /// </summary>
    public NotificationType Type { get; set; } = NotificationType.email;

    /// <summary>
    /// Current status of the notification (pending, sent, failed, delivered)
    /// Default is pending
    /// </summary>
    public NotificationStatus Status { get; set; } = NotificationStatus.pending;

    /// <summary>
    /// Recipient address or identifier
    /// Can be an email address, phone number, or user ID depending on Type
    /// </summary>
    public string Recipient { get; set; } = null!;

    /// <summary>
    /// Optional reference to the user receiving this notification
    /// Null for notifications not tied to a specific user
    /// </summary>
    public Guid? UserId { get; set; }
    
    /// <summary>
    /// Optional reference to the customer receiving this notification
    /// Null for notifications not tied to a specific customer
    /// </summary>
    public Guid? CustomerId { get; set; }

    /// <summary>
    /// Optional reference to a related entity (order, ticket, etc.)
    /// </summary>
    public Guid? RelatedEntityId { get; set; }
    
    /// <summary>
    /// Type name of the related entity
    /// Used to determine how to load the related entity
    /// </summary>
    public string? RelatedEntityType { get; set; }

    /// <summary>
    /// When the notification is scheduled to be sent
    /// Null indicates immediate delivery
    /// </summary>
    public DateTime? ScheduledAt { get; set; }
    
    /// <summary>
    /// When the notification was sent
    /// Used for tracking and reporting
    /// </summary>
    public DateTime? SentAt { get; set; }
    
    /// <summary>
    /// When delivery of the notification was confirmed
    /// Only applicable to channels that support delivery confirmation
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Error details if notification delivery failed
    /// Used for troubleshooting and retry logic
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// Navigation property to the user receiving this notification
    /// </summary>
    public virtual User? User { get; set; }
}
