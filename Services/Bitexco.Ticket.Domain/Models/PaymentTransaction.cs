﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a payment transaction for an order
/// Tracks payment method, status, and reconciliation
/// </summary>
public class PaymentTransaction : Entity<Guid>
{
    /// <summary>
    /// Unique identifier for the payment transaction
    /// summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// Reference to the order being paid for
    /// </summary>
    public Guid? OrderId { get; set; }

    /// <summary>
    /// Method of payment used for this transaction
    /// </summary>
    public PaymentTransactionMethod PaymentMethod { get; set; } = PaymentTransactionMethod.pos; // 'cash', 'pos', 'bank_transfer', 'vnpay', 'zalopay', 'momo'

    /// <summary>
    /// Total amount of the transaction
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Type of transaction
    /// Indicates whether this is an incoming payment (income) or outgoing payment (expense)
    /// </summary>
    public PaymentTransactionType TransactionType { get; set; } = PaymentTransactionType.income; // 'income' for incoming payments, 'expense' for outgoing payments

    /// <summary>
    /// Amount actually received, may be less than Amount in case of partial payments
    /// </summary>
    public decimal PaidAmount { get; set; } = 0; // Amount actually paid, can be less than Amount in case of partial payments

    /// <summary>
    /// Reference code from external payment provider/gateway
    /// Used for reconciliation and tracking
    /// </summary>
    public string? TransactionCode { get; set; } // Transaction code from payment gateway/bank

    /// <summary>
    /// Sales channel through which this order was placed (online, POS, agent, etc.)
    /// </summary>
    public Guid? RetailChannelId { get; set; }

    /// <summary>
    /// Current status of the transaction
    /// </summary>
    public PaymentTransactionStatus TransactionStatus { get; set; } = PaymentTransactionStatus.pending; // 'pending', 'success', 'partial', 'failed', 'refunded', 'partial_refunded'

    /// <summary>
    /// Status of reconciliation with external payment systems
    /// </summary>
    public PaymentReconciliationStatus ReconciliationStatus { get; set; } = PaymentReconciliationStatus.pending; // Default: 'pending'

    /// <summary>
    /// Timestamp when the transaction was initiated
    /// </summary>
    public DateTime TransactionAt { get; set; } // Date when the transaction was created

    /// <summary>
    /// Timestamp when payment was completed (null if pending)
    /// </summary>
    public DateTime? PaidAt { get; set; } // Nullable, can be null if not yet paid

    // Navigation properties
    /// <summary>
    /// Reference to the associated order
    /// </summary>
    public virtual Order? Order { get; set; }

    /// <summary>
    /// Retail channel through which the transaction was made
    /// </summary>
    public virtual RetailChannel? RetailChannel { get; set; }
}
