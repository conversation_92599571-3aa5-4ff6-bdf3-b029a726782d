using System;

namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a one-time password (OTP) verification record
/// Used for securing sensitive operations like password reset, email verification, or two-factor authentication
/// </summary>
public class OtpLog : Entity<Guid>
{
    /// <summary>
    /// Reference to the user this OTP was generated for
    /// </summary>
    public Guid UserId { get; set; }
    
    /// <summary>
    /// The OTP code (typically stored as a hash rather than plaintext)
    /// </summary>
    public string OtpCode { get; set; } = null!;
    
    /// <summary>
    /// Date and time when this OTP expires
    /// Typically set to 5 minutes after generation
    /// </summary>
    public DateTime ExpiresAt { get; set; }
    
    /// <summary>
    /// Delivery method used to send the OTP (email or sms)
    /// Default is email
    /// </summary>
    public string Method { get; set; } = "email";
    
    /// <summary>
    /// Email address or phone number where the OTP was sent
    /// </summary>
    public string SentTo { get; set; } = null!;
    
    /// <summary>
    /// Current status of the OTP (sent, verified, expired, failed)
    /// Default is sent
    /// </summary>
    public string Status { get; set; } = "sent";
    
    /// <summary>
    /// Date and time when the OTP was successfully verified
    /// Null if not yet verified
    /// </summary>
    public DateTime? VerifiedAt { get; set; }
    
    /// <summary>
    /// Number of failed verification attempts
    /// Used to limit brute force attacks
    /// Default is 0
    /// </summary>
    public int FailedAttempts { get; set; } = 0;
    
    /// <summary>
    /// IP address of the user who requested the OTP
    /// Used for security auditing
    /// </summary>
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// User agent string of the browser or device that requested the OTP
    /// Used for security auditing
    /// </summary>
    public string? UserAgent { get; set; }
    
    /// <summary>
    /// Security validation hash combining OTP, user ID, and application secret
    /// Used to validate the OTP without relying solely on the OTP code
    /// </summary>
    public string? ValidationHash { get; set; }
    
    /// <summary>
    /// Navigation property to the user this OTP was generated for
    /// </summary>
    public virtual User User { get; set; } = null!;
}
