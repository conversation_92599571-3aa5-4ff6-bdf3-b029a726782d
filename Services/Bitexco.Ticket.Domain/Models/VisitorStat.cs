namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Aggregates visitor statistics for reporting and analytics
/// Used for historical reporting and business intelligence
/// </summary>
public class VisitorStat : Entity<Guid>
{
    /// <summary>
    /// Date for which these statistics are recorded
    /// </summary>
    public DateOnly StatDate { get; set; } // Date of the statistics
    
    /// <summary>
    /// Specific hour for hourly statistics breakdown (null for daily totals)
    /// </summary>
    public TimeOnly? StatHour { get; set; } // Optional hour for hourly stats
    
    /// <summary>
    /// Optional filter by ticket type name
    /// </summary>
    public string? TicketTypeName { get; set; } // Name of ticket type for filtering

    /// <summary>
    /// Total number of visitors during this time period
    /// </summary>
    public int VisitorCount { get; set; } = 0; // Number of visitors
    
    /// <summary>
    /// Number of visitors who purchased tickets online
    /// </summary>
    public int OnlineTicketCount { get; set; } = 0; // Number of online tickets
    
    /// <summary>
    /// Number of visitors who purchased tickets at physical points of sale
    /// </summary>
    public int OfflineTicketCount { get; set; } = 0; // Number of offline tickets
    
    /// <summary>
    /// Number of visitors who purchased tickets through agents
    /// </summary>
    public int AgentTicketCount { get; set; } = 0; // Number of agent tickets
    
    /// <summary>
    /// Total revenue from all tickets during this time period
    /// </summary>
    public decimal TotalRevenue { get; set; } = 0; // Total revenue
    
    /// <summary>
    /// Number of adult visitors during this time period
    /// </summary>
    public int AdultCount { get; set; } = 0;
    
    /// <summary>
    /// Number of child visitors during this time period
    /// </summary>
    public int ChildCount { get; set; } = 0;
    
    /// <summary>
    /// Number of senior visitors during this time period
    /// </summary>
    public int SeniorCount { get; set; } = 0;
    
    /// <summary>
    /// Number of cash payments during this time period
    /// </summary>
    public int CashPaymentCount { get; set; } = 0;
    
    /// <summary>
    /// Number of card payments through POS terminals during this time period
    /// </summary>
    public int PosPaymentCount { get; set; } = 0;
    
    /// <summary>
    /// Number of bank transfer payments during this time period
    /// </summary>
    public int TransferPaymentCount { get; set; } = 0;
    
    /// <summary>
    /// Number of e-wallet payments during this time period
    /// </summary>
    public int EWalletPaymentCount { get; set; } = 0;
    
    /// <summary>
    /// Hour with the highest visitor count during this day
    /// </summary>
    public TimeOnly? PeakHour { get; set; } // Hour with most visitors
    
    /// <summary>
    /// Number of visitors during the peak hour
    /// </summary>
    public int PeakHourCount { get; set; } = 0; // Number of visitors during peak hour
}