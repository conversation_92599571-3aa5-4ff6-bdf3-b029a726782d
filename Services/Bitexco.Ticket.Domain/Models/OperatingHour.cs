﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Defines the operating hours for the venue or attraction facilities
/// Used to determine when tickets are valid and when customers can visit
/// </summary>
public class OperatingHour : Entity<Guid>
{
    /// <summary>
    /// Day of the week (0=Sunday, 1=Monday, ..., 6=Saturday)
    /// Used for recurring weekly schedules
    /// </summary>
    public int DayOfWeek { get; set; } // 0=Sunday, 1=Monday, ...

    /// <summary>
    /// Time when the venue opens for this schedule entry
    /// </summary>
    public TimeOnly OpenTime { get; set; } // .NET 6+ TimeOnly

    /// <summary>
    /// Time when the venue closes for this schedule entry
    /// </summary>
    public TimeOnly CloseTime { get; set; } // .NET 6+ TimeOnly

    /// <summary>
    /// Indicates whether this schedule represents a special operating day
    /// Used for holidays, special events, or other deviations from regular schedules
    /// Default is false
    /// </summary>
    public bool IsSpecialDay { get; set; } = false; // Default: false

    /// <summary>
    /// Specific date for special operating hours
    /// Only applicable when IsSpecialDay is true
    /// </summary>
    public DateOnly? SpecialDate { get; set; } // Special date (e.g., holiday)

    /// <summary>
    /// Additional information about this operating schedule
    /// Can include details about special days or other important notes
    /// </summary>
    public string? Description { get; set; }
}
