﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a reusable notification template for messages sent to users or customers
/// Contains predefined content with placeholder variables that can be replaced dynamically
/// </summary>
public class NotificationTemplate : Entity<Guid>
{
    /// <summary>
    /// Unique identifier name for the template
    /// Used to reference the template programmatically
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Type of notification channel this template is designed for (email, sms)
    /// Default is email
    /// </summary>
    public NotificationTemplateType Type { get; set; } = NotificationTemplateType.email;

    /// <summary>
    /// Subject line for email notifications
    /// Not used for SMS templates
    /// </summary>
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// Main content of the notification template
    /// Can include placeholder variables in format {{variableName}}
    /// </summary>
    public string Body { get; set; } = string.Empty;
    
    /// <summary>
    /// ISO language code for this template (e.g., 'en', 'vi')
    /// Default is 'vi' for Vietnamese
    /// </summary>
    public string LanguageCode { get; set; } = "vi";
    
    /// <summary>
    /// Human-readable description of template purpose and usage
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// JSON list of variable names and descriptions available for this template
    /// Used for documentation and UI template editors
    /// </summary>
    public string? AvailableVariables { get; set; }
    
    /// <summary>
    /// Indicates whether this template is currently in use
    /// Default is true
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// System event that triggers this notification
    /// Used for automatic notification sending on specific events
    /// </summary>
    public string? EventType { get; set; }
}
