namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents the status of synchronization between ticket system and kiosk terminals
/// Tracks the last sync time and any sync errors
/// </summary>
public class KioskSyncStatus : Entity<Guid>
{
    /// <summary>
    /// The kiosk device that was synced
    /// </summary>
    public Guid PosDeviceId { get; set; }
    
    /// <summary>
    /// Current status of the synchronization (e.g., 'success', 'failed', 'in_progress')
    /// </summary>
    public string Status { get; set; } = null!;
    
    /// <summary>
    /// Date and time when the last sync was started
    /// </summary>
    public DateTime StartedAt { get; set; }
    
    /// <summary>
    /// Date and time when the last sync was completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }
    
    /// <summary>
    /// Any error message from the last sync attempt
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// JSON record of which data was synced and results
    /// </summary>
    public string? SyncDetails { get; set; }
    
    // Navigation properties
    /// <summary>
    /// Reference to the POS device that was synced
    /// </summary>
    public virtual PosDevice PosDevice { get; set; } = null!;
}
