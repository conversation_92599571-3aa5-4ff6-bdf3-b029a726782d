﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Defines the commission calculation rules for agents
/// Supports both percentage-based and fixed-amount commission structures
/// </summary>
public class CommissionPolicy : Entity<Guid>
{
    /// <summary>
    /// Reference to the agent this commission policy applies to
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// Name of the commission policy for identification purposes
    /// </summary>
    public string PolicyName { get; set; } = null!;

    /// <summary>
    /// Commission rate as a percentage of sales
    /// Used for percentage-based commission calculations
    /// </summary>
    public decimal? CommissionRatePercentage { get; set; }

    /// <summary>
    /// Fixed commission amount per sale or transaction
    /// Used for fixed-amount commission calculations
    /// </summary>
    public decimal? FixedCommissionAmount { get; set; }

    /// <summary>
    /// Commission tier level for tiered commission structures
    /// Allows for different commission rates based on sales volume or agent level
    /// </summary>
    public int? TierLevel { get; set; }

    /// <summary>
    /// Date from which this commission policy becomes effective
    /// </summary>
    public DateOnly EffectiveFromDate { get; set; }

    /// <summary>
    /// Date until which this commission policy remains effective
    /// Null indicates the policy does not expire
    /// </summary>
    public DateOnly? EffectiveToDate { get; set; }

    /// <summary>
    /// Indicates whether this commission policy is currently active
    /// Default is true
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property to the agent this policy applies to
    /// </summary>
    public virtual Agent Agent { get; set; } = null!;
}
