namespace Bitexco.Ticket.Domain.Models;

using Bitexco.Ticket.Domain.Abstractions;
using Bitexco.Ticket.Domain.Enums;

/// <summary>
/// Hợp đồng của đại lý
/// </summary>
public class Contract : Entity<Guid>
{
    /// <summary>
    /// Mã hợp đồng từ hệ thống SAP
    /// </summary>
    public string? SapContractCode { get; set; }
    
    /// <summary>
    /// Tên hợp đồng
    /// </summary>
    public string? ContractName { get; set; }
    
    /// <summary>
    /// Ngày ký hợp đồng
    /// </summary>
    public DateOnly? SignedDate { get; set; }
    
    /// <summary>
    /// Ngày hết hạn hợp đồng
    /// </summary>
    public DateOnly? ExpiryDate { get; set; }
    
    /// <summary>
    /// Link tới file hợp đồng
    /// </summary>
    public string? ContractFileUrl { get; set; }
    
    /// <summary>
    /// Trạng thái hợp đồng
    /// </summary>
    public ContractStatus Status { get; set; } = ContractStatus.Pending;
    
    /// <summary>
    /// ID của đại lý sở hữu hợp đồng này
    /// </summary>
    public Guid AgentId { get; set; }
    
    /// <summary>
    /// Ghi chú về hợp đồng
    /// </summary>
    public string? Notes { get; set; }
    
    // Navigation properties
    /// <summary>
    /// Đại lý sở hữu hợp đồng này
    /// </summary>
    public virtual Agent Agent { get; set; } = null!;
}
