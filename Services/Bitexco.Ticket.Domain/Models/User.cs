﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a system user with authentication and authorization information
/// Users include administrators, counter staff, and support personnel
/// </summary>
public class User : Entity<Guid>
{
    /// <summary>
    /// Unique identifier for the user
    /// </summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// Unique username for system login
    /// </summary>
    public string UserName { get; set; } = null!;

    /// <summary>
    /// Securely hashed password
    /// </summary>
    public string PasswordHash { get; set; } = null!;

    /// <summary>
    /// Email address for notifications and password recovery
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// User's full name for display purposes
    /// </summary>
    public string? FullName { get; set; }

    /// <summary>    /// Phone number for SMS notifications and 2FA
    /// </summary>
    public string? PhoneNumber { get; set; }
    
    /// <summary>
    /// Token used for refreshing the JWT access token
    /// </summary>
    public string? RefreshToken { get; set; }
    
    /// <summary>
    /// Expiration time for the current refresh token
    /// </summary>
    public DateTime? RefreshTokenExpiryTime { get; set; }

    /// <summary>
    /// Reference to the user's role for permission management
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Flag indicating if the user account is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Timestamp of the last successful login
    /// </summary>
    public DateTime? LastLoginAt { get; set; }
    
    /// <summary>
    /// Flag indicating if two-factor authentication is enabled for this user
    /// </summary>
    public bool TwoFactorEnabled { get; set; } = false; // Default: false
    
    /// <summary>    /// Secret key for time-based one-time password generation
    /// Should be encrypted in the database
    /// </summary>
    public string? TwoFactorSecret { get; set; } // For TOTP (Time-based One-Time Password)
    
    /// <summary>
    /// Expiration timestamp for the most recently generated OTP
    /// </summary>
    public DateTime? OtpExpiresAt { get; set; } // OTP expiration time
    
    /// <summary>
    /// Most recently sent OTP code (stored as hash for verification)
    /// </summary>
    public string? LastOtpCode { get; set; } // Last sent OTP code (hashed)
    
    /// <summary>
    /// User's preferred method for receiving OTP codes
    /// </summary>
    public PreferredOtpMethod PreferredOtpMethod { get; set; } = PreferredOtpMethod.email; // "email" or "sms"

    // Navigation properties
    /// <summary>    /// Reference to the user's assigned role
    /// </summary>
    public virtual Role? Role { get; set; }

    /// <summary>
    /// Collection of orders processed by this user
    /// </summary>
    public virtual ICollection<Order>? Orders { get; set; }
    
    /// <summary>
    /// Collection of work shifts operated by this user
    /// </summary>
    public virtual ICollection<Shift>? Shifts { get; set; }
    
    /// <summary>
    /// Collection of audit logs created by this user's actions
    /// </summary>
    public virtual ICollection<AuditLog>? AuditLogs { get; set; }
    
    /// <summary>
    /// Collection of system configurations modified by this user
    /// </summary>
    public virtual ICollection<SystemConfig>? SystemConfigs { get; set; }
    
    /// <summary>
    /// Collection of content pages created or modified by this user
    /// </summary>
    public virtual ICollection<ContentPage>? ContentPages { get; set; }
    
    /// <summary>
    /// Collection of support tickets assigned to this user for handling
    /// </summary>
    public virtual ICollection<SupportTicket>? AssignedSupportTickets { get; set; } // For AssignedToUserId
                                                                                   // SupportTicketMessages.SenderId is polymorphic, handled at application level
    
    /// <summary>
    /// Collection of commission reports approved by this user
    /// </summary>
    public virtual ICollection<CommissionReport>? ApprovedCommissionReports { get; set; }
    
    /// <summary>
    /// Collection of commission payments approved by this user
    /// </summary>
    public virtual ICollection<CommissionPayment>? ApprovedCommissionPayments { get; set; }
    
    /// <summary>
    /// Collection of invoices issued by this user
    /// </summary>
    public virtual ICollection<Invoice>? IssuedInvoices { get; set; }
}
