﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a physical point-of-sale device or kiosk in the system
/// Tracks device status, location, and maintenance information
/// </summary>
public class PosDevice : Entity<Guid>
{
    /// <summary>
    /// Unique code for the device, used for identification
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// Friendly name for the device
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Physical location description of the device
    /// </summary>
    public string? Location { get; set; } // Location (e.g., 'Counter 1', 'Kiosk Floor 1')

    /// <summary>
    /// Connected to Backend system
    /// </summary>
    public bool IsConnected { get; set; }
    
    /// <summary>
    /// Geographic latitude for map display
    /// </summary>
    public decimal? Latitude { get; set; }
    
    /// <summary>
    /// Geographic longitude for map display
    /// </summary>
    public decimal? Longitude { get; set; }
    
    /// <summary>
    /// Floor or level in the building
    /// </summary>
    public string? Floor { get; set; }

    /// <summary>
    /// Type of device (POS terminal, self-service kiosk, etc.)
    /// </summary>
    public PosDeviceType Type { get; set; } = PosDeviceType.POS; // 'POS', 'Kiosk'

    /// <summary>
    /// Current firmware version installed on the device
    /// </summary>
    public string? FirmwareVersion { get; set; }

    /// <summary>
    /// Current software version installed on the device
    /// </summary>
    public string? SoftwareVersion { get; set; }
    
    /// <summary>
    /// Operating system details
    /// </summary>
    public string? OperatingSystem { get; set; }
    
    /// <summary>
    /// Hardware model information
    /// </summary>
    public string? HardwareModel { get; set; }
    
    /// <summary>
    /// Device serial number for asset tracking
    /// </summary>
    public string? SerialNumber { get; set; }
    
    /// <summary>
    /// IP address for remote management and monitoring
    /// </summary>
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// MAC address for network identification
    /// </summary>
    public string? MacAddress { get; set; }

    /// <summary>
    /// Current operational status of the device
    /// </summary>
    public PosDeviceStatus Status { get; set; } = PosDeviceStatus.online; // 'online', 'offline', 'error'
    
    /// <summary>
    /// Detailed error information if device is in error state
    /// </summary>
    public string? ErrorDetails { get; set; }
    
    /// <summary>
    /// Timestamp of the last error occurrence
    /// </summary>
    public DateTime? LastErrorAt { get; set; }
    
    /// <summary>
    /// Count of how many times device has gone offline
    /// Used for reliability tracking
    /// </summary>
    public int OfflineCount { get; set; } = 0;
    
    /// <summary>
    /// Timestamp of the last maintenance service
    /// </summary>
    public DateTime? LastMaintenanceAt { get; set; }

    /// <summary>
    /// Timestamp of the last heartbeat received from the device
    /// Used to determine if device is online
    /// </summary>
    public DateTime? LastHeartbeatAt { get; set; }
    
    /// <summary>
    /// Current status of the connected printer
    /// </summary>
    public string? PrinterStatus { get; set; } = "operational"; // 'operational', 'error', 'disconnected', 'paper_low'
    
    /// <summary>
    /// Timestamp of the last software/firmware update
    /// </summary>
    public DateTime? LastUpdateAt { get; set; }
    
    /// <summary>
    /// Current update status of the device
    /// </summary>
    public string? UpdateStatus { get; set; } = "up_to_date"; // 'up_to_date', 'update_available', 'updating'

    // Navigation properties
    /// <summary>
    /// Collection of orders processed through this device
    /// </summary>
    public virtual ICollection<Order>? Orders { get; set; }
    
    /// <summary>
    /// Collection of work shifts operated on this device
    /// </summary>
    public virtual ICollection<Shift>? Shifts { get; set; }
    
    /// <summary>
    /// Collection of tickets validated through this device
    /// </summary>
    public virtual ICollection<Ticket>? ValidatedTickets { get; set; }
}
