﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a specific permission that can be assigned to roles
/// Used in the role-based access control system to grant specific capabilities to users
/// </summary>
public class Permission : Entity<Guid>
{
    /// <summary>
    /// Unique name identifying the permission
    /// Typically follows a pattern like 'resource:action' (e.g., 'tickets:create')
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Human-readable description of what this permission allows
    /// Used for administrative interfaces
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Navigation property to role-permission mappings
    /// Represents which roles have been granted this permission
    /// </summary>
    public virtual ICollection<RolePermission>? RolePermissions { get; set; }
}