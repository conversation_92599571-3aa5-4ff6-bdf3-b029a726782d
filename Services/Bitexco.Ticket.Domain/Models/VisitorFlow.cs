namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Tracks real-time visitor flow through different entry and exit points
/// Used for capacity management and analytics
/// </summary>
public class VisitorFlow : Entity<Guid>
{
    /// <summary>
    /// Timestamp when this flow record was created
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Name of the entry point where this flow was recorded
    /// </summary>
    public string EntryPoint { get; set; } = "main";

    /// <summary>
    /// Number of visitors who entered during this time period
    /// </summary>
    public int EntryCount { get; set; } = 0;

    /// <summary>
    /// Number of visitors who exited during this time period
    /// </summary>
    public int ExitCount { get; set; } = 0;

    /// <summary>
    /// Current number of visitors in the venue
    /// </summary>
    public int CurrentOccupancy { get; set; } = 0;
}
