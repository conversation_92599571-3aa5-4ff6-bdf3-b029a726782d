﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents the many-to-many relationship between Roles and Permissions
/// Junction table linking roles to their assigned permissions
/// </summary>
public class RolePermission
{
    /// <summary>
    /// Foreign key to the Role entity
    /// Part of composite primary key
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// Foreign key to the Permission entity
    /// Part of composite primary key
    /// </summary>
    public Guid PermissionId { get; set; }

    /// <summary>
    /// Date and time when this permission was assigned to the role
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Navigation property to the associated role
    /// </summary>
    public virtual Role Role { get; set; } = null!;

    /// <summary>
    /// Navigation property to the associated permission
    /// </summary>
    public virtual Permission Permission { get; set; } = null!;
}
