﻿namespace Bitexco.Ticket.Domain.Models;

using System;

/// <summary>
/// Represents the many-to-many relationship between Vouchers and RetailChannels
/// Used to restrict vouchers to specific sales channels
/// </summary>
public class VoucherChannel
{
    /// <summary>
    /// Foreign key to the Voucher entity
    /// Part of composite primary key
    /// </summary>
    public Guid VoucherId { get; set; }

    /// <summary>
    /// Foreign key to the RetailChannel entity
    /// Part of composite primary key
    /// </summary>
    public Guid ChannelId { get; set; }

    /// <summary>
    /// Date and time when this voucher-channel association was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Navigation property to the associated voucher
    /// </summary>
    public virtual Voucher Voucher { get; set; } = null!;

    /// <summary>
    /// Navigation property to the associated retail channel
    /// </summary>
    public virtual RetailChannel RetailChannel { get; set; } = null!;
}