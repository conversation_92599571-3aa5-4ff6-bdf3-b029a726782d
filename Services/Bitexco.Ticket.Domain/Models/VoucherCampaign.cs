﻿namespace Bitexco.Ticket.Domain.Models;

using Bitexco.Ticket.Domain.Abstractions;
using System;
using System.Collections.Generic;

/// <summary>
///  Represents a voucher campaign that can be applied to orders
///  Supports both percentage and fixed amount discount types with usage tracking
/// </summary>
public class VoucherCampaign : Entity<Guid>
{
    /// <summary>
    /// Name of the voucher campaign
    /// This is used for marketing and identification purposes
    ///  </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Type of discount calculation (percentage or fixed_amount)
    /// Determines how the DiscountValue is applied
    /// Default is percentage
    /// </summary>
    public VoucherType Type { get; set; } = VoucherType.percentage;

    /// <summary>
    /// Value of the discount
    /// For percentage type: percentage value (e.g., 10 for 10%)
    /// For fixed_amount type: monetary value
    /// </summary>
    public decimal DiscountValue { get; set; }

    /// <summary>
    /// Maximum discount amount that can be applied
    /// This is used to limit the discount for fixed amount vouchers
    /// Null means no maximum limit
    /// </summary>
    public decimal? MaxDiscountAmount { get; set; }

    /// <summary>
    /// Maximum number of times this voucher can be used
    /// Null means unlimited usage
    /// </summary>
    public int? UsageLimit { get; set; }

    /// <summary>
    /// Áp dụng cho loại khách hàng nào
    /// </summary>
    public VoucherApplyType? VoucherApplyType { get; set; }

    /// <summary>
    /// Danh sách Agents được áp dụng voucher này
    /// </summary>
    public List<Guid>? AgentIds { get; set; }

    /// <summary>
    /// Number of times this voucher has been used
    /// Default is 0
    /// </summary>
    public int CurrentUsage { get; set; } = 0;

    /// <summary>
    /// Date and time when this voucher becomes valid
    /// </summary>
    public DateTime ValidFrom { get; set; }

    /// <summary>
    /// Date and time when this voucher expires
    /// </summary>
    public DateTime ValidTo { get; set; }

    /// <summary>
    /// Indicates whether this voucher is currently active
    /// Default is true
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Type of ticket this voucher campaign applies to
    /// </summary>
    public Guid? TicketTypeId { get; set; }

    /// <summary>
    /// Type of ticket this voucher campaign applies to
    /// </summary>
    public TicketType? TicketType { get; set; } 


    /// <summary>
    ///  Navigation property to all vouchers in this campaign
    ///  Used to group vouchers for marketing or promotional purposes
    /// </summary>
    public virtual List<Voucher>? Vouchers { get; set; }
}