﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents the pricing information for a specific ticket type
/// Contains base prices and time-specific pricing for different ticket categories
/// </summary>
public class TicketPrice : Entity<Guid>
{
    /// <summary>
    /// Reference to the ticket type this price applies to
    /// </summary>
    public Guid TicketTypeId { get; set; }

    /// <summary>
    /// Optional reference to a pricing rule that affects this price
    /// Can be null for standard base prices that don't follow special rules
    /// </summary>
    public Guid? PricingRuleId { get; set; }

    /// <summary>
    /// The actual price amount for this ticket type
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// Date from which this price becomes effective
    /// Used for price changes scheduled in advance
    /// </summary>
    public DateOnly EffectiveDateFrom { get; set; } // .NET 6+ DateOnly

    /// <summary>
    /// Optional end date for this price's validity
    /// Null indicates the price has no expiration
    /// </summary>
    public DateOnly? EffectiveDateTo { get; set; } // .NET 6+ DateOnly

    // Navigation properties
    /// <summary>
    /// Reference to the associated ticket type
    /// </summary>
    public virtual TicketType TicketType { get; set; } = null!;

    /// <summary>
    /// Reference to any associated pricing rule
    /// Might define special conditions like peak hours, holidays, or weekend pricing
    /// </summary>
    public virtual PricingRule? PricingRule { get; set; }
}
