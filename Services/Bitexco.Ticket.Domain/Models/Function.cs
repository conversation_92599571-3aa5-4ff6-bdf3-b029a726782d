﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a function/feature in the system that can be assigned to roles
/// Functions define specific features or modules that users can access
/// </summary>
public class Function : Entity<Guid>
{
    /// <summary>
    /// Name of the function
    /// </summary>
    public string Name { get; set; } = null!;
    
    /// <summary>
    /// Code của chức năng để phân quyền
    /// </summary>
    public string? Code { get; set; }
    
    /// <summary>
    /// Description of the function's purpose
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// Path/URL of the function
    /// </summary>
    public string? Path { get; set; }
    
    /// <summary>
    /// Icon path for the function
    /// </summary>
    public string? IconPath { get; set; }
    
    // Navigation properties
    /// <summary>
    /// Navigation property to role functions
    /// </summary>
    public virtual ICollection<RoleFunction>? RoleFunctions { get; set; }
}
