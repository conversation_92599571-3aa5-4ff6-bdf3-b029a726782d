namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Booking group bus
/// </summary>
public class GroupBookingBus : Entity<Guid>
{
    /// <summary>
    /// The bus number
    /// </summary>
    public string Number { get; set; } = string.Empty;

    /// <summary>
    /// Reference to the agents who made this booking
    /// </summary>
    public List<Guid> AgentIds { get; set; } = [];

    /// <summary>
    /// Reference to the group booking
    /// </summary>
    public Guid GroupBookingId { get; set; }

    public virtual GroupBooking GroupBooking { get; set; } = null!;
}