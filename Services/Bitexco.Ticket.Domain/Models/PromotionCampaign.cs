﻿namespace Bitexco.Ticket.Domain.Models
{
    public class PromotionCampaign : Entity<Guid>
    {
        /// <summary>
        /// Name of the Promotion Campaign
        /// This is used for marketing and identification purposes
        ///  </summary>
        public string Name { get; set; } = null!;

        /// <summary>
        /// Type of discount calculation (percentage or fixed_amount)
        /// Determines how the DiscountValue is applied
        /// Default is percentage
        /// </summary>
        public PromotionType Type { get; set; } = PromotionType.percentage;

        /// <summary>
        /// Value of the discount
        /// For percentage type: percentage value (e.g., 10 for 10%)
        /// For fixed_amount type: monetary value
        /// </summary>
        public decimal DiscountValue { get; set; }

        /// <summary>
        /// Maximum discount amount that can be applied
        /// This is used to limit the discount for fixed amount promotion
        /// Null means no maximum limit
        /// </summary>
        public decimal? MaxDiscountAmount { get; set; }

        /// <summary>
        /// Type of ticket this promotion campaign applies to
        /// </summary>
        public Guid? TicketTypeId { get; set; }

        /// <summary>
        /// Áp dụng cho loại khách hàng nào
        /// </summary>
        public PromotionApplyType? PromotionApplyType { get; set; }

        /// <summary>
        /// Danh sách Agents được áp dụng promotion này
        /// </summary>
        public List<Guid>? AgentIds { get; set; }

        /// <summary>
        /// Date and time when this promotion becomes valid
        /// </summary>
        public DateTime ValidFrom { get; set; }

        /// <summary>
        /// Date and time when this promotion expires
        /// </summary>
        public DateTime ValidTo { get; set; }

        /// <summary>
        /// Indicates whether this promotion is currently active
        /// Default is true
        /// </summary>
        public bool IsActive { get; set; } = true;

        public virtual TicketType? TicketType { get; set; }
    }
}