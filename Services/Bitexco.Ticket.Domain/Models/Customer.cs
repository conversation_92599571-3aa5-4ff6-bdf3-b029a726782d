﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a customer who purchases tickets or services
/// Supports both individual and corporate customer types with appropriate data fields
/// </summary>
public class Customer : Entity<Guid>
{
    /// <summary>
    /// Code for the customer
    /// Unique identifier used for quick reference in transactions
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// Full name of the customer
    /// </summary>
    public string FullName { get; set; } = null!;

    /// <summary>
    /// Contact phone number of the customer
    /// Primary method of contact for bookings and notifications
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Email address of the customer
    /// Used for sending digital tickets, receipts, and notifications
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Used for localization and potential tax implications
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// Retail channel identifier
    /// Indicates the channel through which the customer was acquired (e.g., online, in-person)
    /// </summary>
    public Guid? RetailChannelId { get; set; }

    /// <summary>
    /// Type of customer (individual or corporate)
    /// Determines tax calculation and available discount options
    /// Default is individual
    /// </summary>
    public CustomerType Type { get; set; } = CustomerType.individual;

    /// <summary>
    /// Name of the company for corporate customers
    /// Only applicable when Type is corporate
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// Tax identification code for corporate customers
    /// Required for corporate invoicing
    /// </summary>
    public string? TaxCode { get; set; }

    /// <summary>
    /// Physical address of the customer
    /// Used for invoicing and delivery
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Indicates whether the customer account is currently active
    /// Default is true
    /// </summary>
    public bool? IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property to all orders placed by this customer
    /// </summary>
    public virtual ICollection<Order>? Orders { get; set; }
    
    /// <summary>
    /// Navigation property to all tickets owned by this customer
    /// </summary>
    public virtual ICollection<Ticket>? Tickets { get; set; }
    
    /// <summary>
    /// Navigation property to all invoices issued to this customer
    /// </summary>
    public virtual ICollection<Invoice>? Invoices { get; set; }

    /// <summary>
    /// Navigation property to retail channel this customer belongs to
    /// </summary>
    public virtual RetailChannel? RetailChannel { get; set; }

    /// <summary>
    /// Navigation property to all support tickets submitted by this customer
    /// </summary>
    public virtual ICollection<SupportTicket>? SupportTickets { get; set; }
    // SupportTicketMessages.SenderId is polymorphic, handled at application level
}
