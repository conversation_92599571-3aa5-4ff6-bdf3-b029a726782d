﻿using System;
using System.Collections.Generic;

namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Defines dynamic pricing rules that modify ticket prices based on various conditions
/// Supports time-based, day-based, and holiday-based price adjustments
/// </summary>
public class PricingRule : Entity<Guid>
{
    /// <summary>
    /// Name of the pricing rule for identification purposes
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Start time when this pricing rule becomes effective
    /// Null indicates the rule applies all day
    /// </summary>
    public TimeOnly? StartTime { get; set; }
    
    /// <summary>
    /// End time when this pricing rule stops being effective
    /// Null indicates the rule applies until the end of the day
    /// </summary>
    public TimeOnly? EndTime { get; set; }

    /// <summary>
    /// Days of the week when this pricing rule applies
    /// Array of integers (0=Sunday, 1=Monday, ..., 6=Saturday)
    /// Empty list indicates the rule applies to all days of the week
    /// </summary>
    public List<int> DayOfWeek { get; set; } = [];

    /// <summary>
    /// Indicates whether this pricing rule applies to holidays
    /// Default is false
    /// </summary>
    public bool IsHoliday { get; set; } = false;

    /// <summary>
    /// Multiplier applied to the base ticket price
    /// E.g., 1.2 means 20% increase during peak hours
    /// </summary>
    public decimal? PriceMultiplier { get; set; }

    /// <summary>
    /// Fixed amount added to the base ticket price
    /// Used for surcharges during special periods
    /// </summary>
    public decimal? FixedSurcharge { get; set; }

    /// <summary>
    /// Indicates whether this pricing rule is currently in effect
    /// Default is true
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property to ticket prices that use this pricing rule
    /// </summary>
    public virtual ICollection<TicketPrice>? TicketPrices { get; set; }
}
