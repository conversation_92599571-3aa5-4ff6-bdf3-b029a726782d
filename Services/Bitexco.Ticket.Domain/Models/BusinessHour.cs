namespace Bitexco.Ticket.Domain.Models;

using Bitexco.Ticket.Domain.Abstractions;
using System;

/// <summary>
/// Defines the operating hours for the venue or attraction
/// Used to manage scheduling, availability, and special operating conditions
/// </summary>
public class BusinessHour : Entity<Guid>
{
    /// <summary>
    /// Day of the week (0=Sunday, 1=Monday, ..., 6=Saturday)
    /// Used to determine regular weekly schedule
    /// </summary>
    public int DayOfWeek { get; set; }
    
    /// <summary>
    /// Indicates whether the venue is open on this day or time slot
    /// Default is true (open for business)
    /// </summary>
    public bool IsOpen { get; set; } = true;
    
    /// <summary>
    /// The time when the venue opens for this schedule entry
    /// </summary>
    public TimeOnly OpenTime { get; set; }
    
    /// <summary>
    /// The time when the venue closes for this schedule entry
    /// </summary>
    public TimeOnly CloseTime { get; set; }
    
    /// <summary>
    /// Special notes or instructions about this schedule entry
    /// Can include information about last entry times or special conditions
    /// </summary>
    public string? Notes { get; set; }
    
    /// <summary>
    /// Indicates whether this schedule entry represents a holiday
    /// Default is false (regular operating schedule)
    /// </summary>
    public bool IsHoliday { get; set; } = false;
    
    /// <summary>
    /// For one-time special dates such as holidays or special events
    /// When specified, this schedule entry applies only to this specific date
    /// </summary>
    public DateOnly? SpecificDate { get; set; }
}
