﻿namespace Bitexco.Ticket.Domain.Models;

using Bitexco.Ticket.Domain.Abstractions;
using System;

/// <summary>
/// Represents a commission payment made to an agent
/// Tracks the payment details, status, and approval workflow
/// </summary>
public class CommissionPayment : Entity<Guid>
{
    /// <summary>
    /// Reference to the agent receiving this commission payment
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// Reference to the commission report this payment is based on
    /// </summary>
    public Guid CommissionReportId { get; set; }

    /// <summary>
    /// The amount being paid to the agent
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Date when the payment was or will be made
    /// </summary>
    public DateTime PaymentDate { get; set; }

    /// <summary>
    /// Method used to make the payment (bank_transfer, cash)
    /// Default is bank_transfer
    /// </summary>
    public CommissionPaymentMethod PaymentMethod { get; set; } = CommissionPaymentMethod.bank_transfer;

    /// <summary>
    /// Current status of the payment (pending, paid, approved, rejected)
    /// Default is pending
    /// </summary>
    public CommissionPaymentStatus Status { get; set; } = CommissionPaymentStatus.pending;

    /// <summary>
    /// Reference to the user who approved this payment
    /// Null if the payment has not been approved yet
    /// </summary>
    public Guid? ApprovedByUserId { get; set; }

    /// <summary>
    /// Navigation property to the agent receiving this commission payment
    /// </summary>
    public virtual Agent Agent { get; set; } = null!;

    /// <summary>
    /// Navigation property to the commission report this payment is based on
    /// </summary>
    public virtual CommissionReport CommissionReport { get; set; } = null!;

    /// <summary>
    /// Navigation property to the user who approved this payment
    /// </summary>
    public virtual User? ApprovedByUser { get; set; }
}
