namespace Bitexco.Ticket.Domain.Models;

using Bitexco.Ticket.Domain.Abstractions;
using System;

/// <summary>
/// Represents a commission order history
///  Tracks the commission payments made to agents based on commission reports
///  Includes details like payment amount, date, method, and status
/// </summary>
public class CommissionOrderHistory : Entity<Guid>
{
    /// <summary>
    /// Code for the commission order history
    /// This is used for easy reference and tracking
    /// </summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// Reference to the agent receiving this commission payment
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// Reference to the order id this commission payment is based on
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Commission rate percentage applied to the order
    /// This is used to calculate the commission amount
    /// </summary>
    public decimal CommissionRatePercentage { get; set; } = 0;

    /// <summary>
    /// Commission amount being paid to the agent
    /// </summary>
    public decimal CommissionAmount { get; set; } = 0;

    /// <summary>
    /// Paid amount for this commission payment
    /// This may be less than the commission amount if only a partial payment is made
    /// </summary>
    public decimal PaidAmount { get; set; } = 0;

    /// <summary>
    /// Date when the payment was or will be made
    /// </summary>
    public DateTime? PaymentDate { get; set; }

    /// <summary>
    /// Method used to make the payment (bank_transfer, cash)
    /// Default is bank_transfer
    /// </summary>
    public CommissionPaymentMethod PaymentMethod { get; set; } = CommissionPaymentMethod.bank_transfer;

    /// <summary>
    /// Payment proof or transaction reference
    /// This can be a transaction ID, receipt number, or any other identifier
    /// </summary>
    public string? PaymentProofUrl { get; set; }

    /// <summary>
    /// Current status of the payment (pending, paid, approved, rejected)
    /// Default is pending
    /// </summary>
    public CommissionPaymentStatus Status { get; set; } = CommissionPaymentStatus.pending;

    /// <summary>
    /// Navigation property to the agent receiving this commission payment
    /// </summary>
    public virtual Agent Agent { get; set; } = null!;

    /// <summary>
    /// Navigation property to the order this payment is based on
    /// </summary>
    public virtual Order Order { get; set; } = null!;

}