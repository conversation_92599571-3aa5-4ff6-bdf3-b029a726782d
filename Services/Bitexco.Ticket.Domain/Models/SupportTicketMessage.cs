﻿namespace Bitexco.Ticket.Domain.Models;

/// <summary>
/// Represents a message in a customer support ticket conversation
/// Tracks the communication history between customers and support staff
/// </summary>
public class SupportTicketMessage : Entity<Guid>
{
    /// <summary>
    /// Reference to the support ticket this message belongs to
    /// </summary>
    public Guid SupportTicketId { get; set; }

    /// <summary>
    /// Type of entity that sent this message (customer, user, agent, admin, system)
    /// Used to determine how to interpret the SenderId
    /// Default is customer
    /// </summary>
    public SupportTicketMessageSenderType SenderType { get; set; } = SupportTicketMessageSenderType.customer;

    /// <summary>
    /// ID of the entity that sent this message
    /// Polymorphic reference - can be a UserId or CustomerId depending on SenderType
    /// </summary>
    public Guid SenderId { get; set; }

    /// <summary>
    /// Content of the support ticket message
    /// </summary>
    public string MessageContent { get; set; } = string.Empty;

    /// <summary>
    /// Date and time when this message was sent
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Navigation property to the support ticket this message belongs to
    /// </summary>
    public virtual SupportTicket SupportTicket { get; set; } = null!;

    // Note: For SenderId, a polymorphic relationship is handled at the application level
    // as direct EF Core navigation properties for polymorphic associations require custom configuration.
    // The application logic resolves the appropriate sender entity based on SenderType.
    // public virtual User SenderUser { get; set; } // If SenderType is 'user'
    // public virtual Customer SenderCustomer { get; set; } // If SenderType is 'customer'
}
