﻿// filepath: d:\Projects\Bitexco.Ticket.API1\Services\Bitexco.Ticket.Domain\Abstractions\Entity.cs
namespace Bitexco.Ticket.Domain.Abstractions;

/// <summary>
/// Base abstract class for all domain entities with common auditing properties
/// </summary>
/// <typeparam name="T">Type of the primary key (typically Guid)</typeparam>
public abstract class Entity<T> : IEntity<T>
{
    /// <summary>
    /// Primary key identifier for the entity
    /// </summary>
    public T Id { get; set; } = default!; // Default value should be set by the derived class or during instantiation
    
    /// <summary>
    /// Soft delete flag - when true, the record is considered deleted but remains in the database
    /// </summary>
    public bool IsDeleted { get; set; } = false; // Default: false
    
    /// <summary>
    /// Date and time when the entity was created
    /// </summary>
    public DateTime? CreatedAt { get; set; }
    
    /// <summary>
    /// Identifier of the user who created the entity
    /// </summary>
    public string? CreatedBy { get; set; }
    
    /// <summary>
    /// Date and time when the entity was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
    
    /// <summary>
    /// Identifier of the user who last updated the entity
    /// </summary>
    public string? UpdatedBy { get; set; }
}
