﻿namespace Bitexco.Ticket.Domain.Enums;

public enum TicketStatus
{
    created = 0, // the ticket has been created but not yet sold
    sold = 1,   // the ticket has been sold to a customer
    used = 2,  // the ticket has been used by the customer
    cancelled = 3, // the ticket has been cancelled by the customer or system
    refunded = 4, // the ticket has been refunded to the customer
    expired = 5 // the ticket has expired and can no longer be used
}
