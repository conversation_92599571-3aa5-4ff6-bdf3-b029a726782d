namespace Bitexco.Ticket.Domain.Enums;

/// <summary>
/// Represents the possible states in the price approval process
/// </summary>
public enum PriceApprovalStatus
{
    pending = 0,      // Price change is awaiting approval
    approved = 1,     // Price change has been approved
    rejected = 2,     // Price change has been rejected
    implemented = 3,  // Approved price change has been implemented
    cancelled = 4     // Price change request was cancelled
}
