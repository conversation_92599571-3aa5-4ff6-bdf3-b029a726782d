﻿namespace Bitexco.Ticket.Domain.Enums;

/// <summary>
/// Defines the possible notification delivery statuses
/// </summary>
public enum NotificationStatus
{
    /// <summary>Awaiting sending</summary>
    pending = 0,
    /// <summary>Successfully sent</summary>
    sent = 1,
    /// <summary>Failed to send</summary>
    failed = 2,
    /// <summary>Successfully delivered</summary>
    delivered = 3
}