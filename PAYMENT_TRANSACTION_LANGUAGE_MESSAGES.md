# PaymentTransaction Language Messages

## SQL INSERT Statements for PaymentTransaction Module

Để thêm hỗ trợ đa ngôn ngữ cho PaymentTransaction module, thực thi các câu lệnh SQL sau vào database:

```sql
-- PaymentTransaction Exception Messages
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES 
('DuplicatedDataException_field_value', 'Field ''{0}'' already exists: {1}'),
('DuplicatedDataException_field_value_vi', 'Trường ''{0}'' đã tồn tại: {1}'),
('NoEntityCreatedException_entityName', 'Failed to create {0}'),
('NoEntityCreatedException_entityName_vi', 'Không thể tạo {0}'),
('OrderNotFoundException_orderId', 'Order with ID {0} not found'),
('OrderNotFoundException_orderId_vi', 'Không tìm thấy đơn hàng với ID {0}');

-- PaymentTransaction Success Messages  
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('PaymentTransactionCreatedSuccessfully', 'Payment transaction created successfully'),
('PaymentTransactionCreatedSuccessfully_vi', 'Đã tạo giao dịch thanh toán thành công');
```

## Message Keys Usage

### Exception Messages:
1. **DuplicatedDataException_field_value**: 
   - EN: "Field '{0}' already exists: {1}"
   - VI: "Trường '{0}' đã tồn tại: {1}"
   - Usage: `DuplicatedDataException("PaymentTransaction", "Order already has a successful payment")`

2. **NoEntityCreatedException_entityName**:
   - EN: "Failed to create {0}"
   - VI: "Không thể tạo {0}"
   - Usage: `NoEntityCreatedException("PaymentTransaction")`

3. **OrderNotFoundException_orderId**:
   - EN: "Order with ID {0} not found"
   - VI: "Không tìm thấy đơn hàng với ID {0}"
   - Usage: `OrderNotFoundException(orderId)`

### Success Messages:
1. **PaymentTransactionCreatedSuccessfully**:
   - EN: "Payment transaction created successfully"
   - VI: "Đã tạo giao dịch thanh toán thành công"

## Total Messages Added: 8 (4 EN + 4 VI)

⚠️ **CRITICAL**: Execute these SQL statements directly in database. 
❌ **DO NOT use migrations** - this violates Boss Rules.
