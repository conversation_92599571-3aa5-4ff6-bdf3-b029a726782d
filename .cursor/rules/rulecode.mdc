---
description: 
globs: 
alwaysApply: true
---
# 📋 Bitexco.Ticket.API - Project Context & Rules

## 🎯 **Project Overview**
- **Project:** Bitexco.Ticket.API - Ticketing system for Bitexco Tower
- **Architecture:** Clean Architecture with CQRS + MediatR
- **Path:** `/Users/<USER>/Workspace/Working/OutSrc/Bitexco.Ticket.API`
- **Framework:** .NET 8, EF Core, PostgreSQL
- **Language:** Ti<PERSON><PERSON> (Vietnamese responses preferred)

## 🔥 **BOSS RULES (2024) - ABSOLUTE PRIORITY**
1. **DTO Split Pattern:** BaseDto (no Id) + ResponseDto (with Id) - follow RoleDto example
2. **Endpoint Format:** ALWAYS snake-case (`/forgot-password-by-session` NOT `/forgotPasswordBySession`)
3. **DTO Organization:** Feature DTOs in feature folders, shared DTOs in /Dtos/
4. **DTO Inheritance:** Use inheritance to avoid code duplication - feature DTOs inherit from base DTOs
5. **Migration Ban:** NO MIGRATIONS under any circumstances - SQL INSERTs only

## 🏗️ **Project Structure**
```
Bitexco.Ticket.API/
├── Services/
│   ├── Bitexco.Ticket.API/           # Web API layer
│   ├── Bitexco.Ticket.Application/   # Application logic (CQRS)
│   ├── Bitexco.Ticket.Domain/        # Domain models
│   └── Bitexco.Ticket.Infrastructure/ # Data access
└── BuildingBlocks/                   # Shared components
```

## 📖 **Coding Conventions & Rules**

### **1. MediTrack Pattern Compliance**
- **ALWAYS follow MediTrack conventions** for consistency
- **Singleton services** for helpers (like `LanguageServiceHelper`)
- **Static cache** for performance (like `LanguageDataStorage`)
- **Service provider pattern** for safe DI access
- **No constructor DB access** in singleton services

### **2. Language & Localization Rules**
- **Startup loading:** All language messages loaded at app startup
- **Header detection:** Use `lang: vi` or `lang: en` headers
- **Fallback logic:** `vi → en → key → formatted fallback`
- **Exception messages:** All exceptions use `ExceptionMessageHelper.GetMessage()`
- **Key pattern:** `ExceptionName_param1_param2_...` based on constructor parameters
- **API Language Support:** All 53 API endpoints have `LanguageClientAttribute`

### **3. File Operations**
- **Always use absolute paths** (starting with `/` or drive letter)
- **Chunk large files** into ≤30 lines for write operations
- **Use `edit_block`** for precise edits with exact text matching
- **Read files first** before making changes to understand structure

### **4. Exception Handling Pattern**
```csharp
// Pattern: ExceptionName_parameterName1_parameterName2
public SystemConfigNotFoundException(string key, string message) : 
    base(ExceptionMessageHelper.GetMessage(
        nameof(SystemConfigNotFoundException) + "_" + nameof(key) + "_" + nameof(message), 
        key, message)) { }
```

### **5. Database Seeding Pattern**
```csharp
// Always include both languages with parameter placeholders
{ "ExceptionName_params_en", "Message with {0} and {1} parameters" },
{ "ExceptionName_params_vi", "Thông báo với tham số {0} và {1}" },
```

## 🔧 **Implementation Status**

### **✅ Completed Features**
1. **Language Service System**
   - ✅ Startup loading from database
   - ✅ Static cache with `LanguageDataStorage`
   - ✅ HTTP header language detection
   - ✅ Parameter support with string.Format
   - ✅ Extension method for clean initialization

2. **Exception Localization**
   - ✅ All 25 custom exceptions converted (21 original + 4 PosDevice)
   - ✅ Unique keys per constructor: `ExceptionName_param1_param2`
   - ✅ 94 database message keys (47 EN + 47 VI) including success messages
   - ✅ `ExceptionMessageHelper` + `SuccessMessageHelper` static helpers

3. **API Language Features** 🆕
   - ✅ All 58 API endpoints have `LanguageClientAttribute`
   - ✅ Swagger language header filter
   - ✅ Automatic multilingual error responses
   - ✅ Complete API modules coverage (including PosDevice)

4. **Success Message System** 🆕
   - ✅ SuccessMessageHelper for multilingual success responses
   - ✅ Integrated with LanguageExtensions initialization
   - ✅ Same pattern as ExceptionMessageHelper
   - ✅ Used in API responses for user feedback

5. **PosDevice CRUD API** 🆕
   - ✅ Complete CQRS implementation with 5 endpoints
   - ✅ FluentValidation for all Commands
   - ✅ Soft delete with IsDeleted filtering
   - ✅ Custom exceptions with multilingual support
   - ✅ Success messages for user feedback

### **🎯 Key Files & Services**
- **Language Services:**
  - `ICurrentLanguageService` / `CurrentLanguageService` - Request-scoped
  - `ILanguageServiceHelper` / `LanguageServiceHelper` - Singleton
  - `ExceptionMessageHelper` - Static helper for exceptions
  - `SuccessMessageHelper` - Static helper for success messages
  - `LanguageDataStorage` - Static cache (MediTrack pattern)
  - `LanguageExtensions` - Extension methods for clean initialization

- **Exception Files:** All 21 files in `/Services/Bitexco.Ticket.Application/Exceptions/`
- **Seeding:** `LanguageMessageSeeder.cs` with 68 message keys
- **Initialization:** `Program.cs` with optimized startup language loading

## 📊 **API Endpoints Coverage**

| **Module** | **Endpoints** | **LanguageClientAttribute** |
|------------|---------------|------------------------------|
| AuthModule | 3 | ✅ Complete |
| TicketPriceModule | 1 | ✅ Complete |
| SystemConfigModule | 2 | ✅ Complete |
| OrderModule | 6 | ✅ Complete |
| AgentModule | 7 | ✅ Complete |
| FaqModule | 3 | ✅ Complete |
| VoucherModule | 5 | ✅ Complete |
| TicketTypeModule | 20 | ✅ Complete |
| TicketModule | 6 | ✅ Complete |
| **PosDeviceModule** | **5** | **✅ Complete** |
| **TOTAL** | **58** | **✅ 100% Complete** |

## 🚀 **Language Services Architecture**

### **Flow hoạt động:**
```
HTTP Request (lang: vi) 
→ CurrentLanguageService (request-scoped)
→ LanguageServiceHelper (singleton) 
→ LanguageDataStorage (static cache)
→ Returns: "Localized message"

Exception thrown
→ ExceptionMessageHelper (static)
→ Reads lang from HttpContext
→ LanguageDataStorage (static cache)
→ Returns: "Formatted exception message"

Success response
→ SuccessMessageHelper (static)
→ Reads lang from HttpContext
→ LanguageDataStorage (static cache)
→ Returns: "Formatted success message"
```

### **Initialization in Program.cs:**
```csharp
// Optimized initialization
await app.InitializeLanguageServicesAsync();
```

## 🧪 **Testing & Verification**

### **Language Testing:**
```bash
# Test Vietnamese
curl -H "lang: vi" https://localhost:5001/system-configs/regions

# Test English  
curl -H "lang: en" https://localhost:5001/system-configs/regions
```

### **Exception Testing:**
```csharp
// These automatically use correct multilingual messages:
throw new SystemConfigNotFoundException("Regions", "Database error");
throw new NoEntityCreatedException("User");
throw new TicketNotFoundException(guid);
```

## 🚀 **Development Guidelines**

### **When Adding New Features:**
1. **Follow MediTrack patterns** for service architecture
2. **Use absolute paths** for all file operations
3. **Add language support** if user-facing messages needed
4. **Test both languages** (vi/en) for any new exceptions
5. **Chunk large file writes** into ≤30 line pieces
6. **Add LanguageClientAttribute** to new API endpoints

### **When Modifying Exceptions:**
1. **Use parameter-based keys:** `ExceptionName_param1_param2`
2. **Add both EN/VI messages** to seeder
3. **Use `ExceptionMessageHelper.GetMessage()`** in constructors
4. **Test with different parameter combinations**

### **When Working with Files:**
1. **Read existing structure** before modifying
2. **Use `edit_block` with exact text matching**
3. **Always verify with `dotnet build`** after changes
4. **Check for absolute vs relative path usage**

### **When Adding New API Endpoints:**
1. **Always add `.WithMetadata(new LanguageClientAttribute());`**
2. **Follow consistent naming patterns**
3. **Include proper response types and problem details**
4. **Test with both language headers**

## 📝 **Boss Requirements Compliance**
- ✅ **Startup loading:** Language messages loaded when app starts
- ✅ **Parameter support:** Unlimited parameters with string.Format
- ✅ **Key differentiation:** Unique keys per constructor using parameter names
- ✅ **Multilingual:** Vietnamese/English with header detection
- ✅ **Convention compliance:** Follows MediTrack patterns exactly
- ✅ **Performance:** Static cache for ultra-fast access
- ✅ **API Language Support:** All 53 endpoints support multilingual headers

## 🎯 **Quick Reference Commands**

### **Build & Test:**
```bash
cd /Users/<USER>/Workspace/Working/OutSrc/Bitexco.Ticket.API
dotnet build
dotnet run --project Services/Bitexco.Ticket.API
```

### **Key Directories:**
- **Exceptions:** `/Services/Bitexco.Ticket.Application/Exceptions/`
- **Services:** `/Services/Bitexco.Ticket.Application/Services/`
- **Features:** `/Services/Bitexco.Ticket.Application/Features/`
- **API:** `/Services/Bitexco.Ticket.API/`
- **Endpoints:** `/Services/Bitexco.Ticket.API/Endpoints/`
- **Extensions:** `/Services/Bitexco.Ticket.API/Extensions/`

### **Important Files Created/Modified:**
- **PosDevice CRUD:** Complete feature in `/Features/PosDevices/`
- **SuccessMessageHelper:** `/Services/.../Services/SuccessMessageHelper.cs`
- **PosDevice DTOs:** `/Services/.../Dtos/PosDeviceDto.cs`
- **PosDevice Endpoints:** `/Services/.../Endpoints/PosDeviceModule.cs`
- **New Exceptions:** 4 files in `/Exceptions/` folder
- **Language Messages:** `POSDEVICE_LANGUAGE_MESSAGES.md` (26 messages)
- **Updated Extensions:** `/Extensions/LanguageExtensions.cs`

---

## 💡 **For New Chat Sessions:**

### **Context Restoration:**
1. **Reference this document** at the start of new chat
2. **Point to project path:** `/Users/<USER>/Workspace/Working/OutSrc/Bitexco.Ticket.API`
3. **Mention specific patterns:** "Follow MediTrack conventions and exception key pattern"
4. **Reference implementation status** from this document
5. **Note language preference:** Respond in Vietnamese

### **Quick Context Prompt:**
```
"Working on Bitexco.Ticket.API project at path /Users/<USER>/Workspace/Working/OutSrc/Bitexco.Ticket.API. 

Please read the PROJECT_CONTEXT_RULES.md file in the project root for complete context and follow:
1. MediTrack patterns for service architecture  
2. Exception key pattern: ExceptionName_param1_param2
3. Startup loading for language messages
4. Absolute paths for file operations
5. 30-line chunking for large file writes

Current implementation includes complete language service system with 25 localized exceptions, 94 database message keys, and complete PosDevice CRUD API. All 58 API endpoints have LanguageClientAttribute for multilingual support with parameter formatting.

Recent additions: PosDevice CRUD API, FluentValidation, Soft Delete, SuccessMessageHelper, and 26 new language messages.

p/s: từ nay trả lời tiếng Việt nhé."
```

## 🔄 **Recent Updates (Latest Session - Boss Rules Implementation)**
- ✅ **Added Boss Rules for DTOs** - Split pattern with base DTO + ResponseDto
- ✅ **Added Boss Rules for Endpoints** - snake-case mandatory 
- ✅ **Added Boss Rules for DTO Organization** - Feature-specific in feature folders
- ✅ **Added DTO Inheritance Best Practices** - Use inheritance to avoid code duplication 🆕
- ✅ **Implemented ShiftSummaryDto refactor** - Moved to feature folder and uses inheritance 🆕
- ✅ **Reinforced Migration Ban** - Absolute no migration policy
- 🔄 **PENDING:** Need to refactor `ForgotPasswordDto` to follow new DTO rules
- 🔄 **PENDING:** Need to verify all endpoints use snake-case format

## 🎯 **TODO: Apply Boss Rules to Current Code**

### **🚨 URGENT FIXES NEEDED:**
1. **Fix Endpoint URLs in AuthModule.cs:**
   - ❌ Current: `/forgotPasswordBySession` (camelCase - WRONG)
   - ✅ Should be: `/forgot-password-by-session` (snake-case - BOSS RULE)

2. **Move ForgotPasswordDto:** 
   - ❌ Current: `/Services/Bitexco.Ticket.Application/Dtos/ForgotPasswordDto.cs`
   - ✅ Should be: Move to feature folders:
     - `Auth/ForgotPasswordBySession/ForgotPasswordRequestDto.cs`
     - `Auth/ForgotPasswordBySession/ForgotPasswordResponseDto.cs`
     - `Auth/ResetPasswordBySession/ResetPasswordBySessionRequestDto.cs`

3. **Split ForgotPasswordDto:** Follow RoleDto pattern:
   - Create base DTO (no Id) + ResponseDto (with Id) pattern
   - Apply inheritance structure like RoleDto/RoleResponseDto

4. **Verify All Endpoints:** Check all modules for snake-case compliance
   - PosDeviceModule: Should be `/pos-devices` ✅
   - TicketTypeModule: Should be `/ticket-types` 
   - SystemConfigModule: Should be `/system-configs`

## 🎯 **DTO Inheritance Implementation Examples** 🆕

### **✅ Completed: ShiftSummaryDto Refactor**
**Problem:** ShiftSummaryDto was duplicating fields from ShiftResponseDto
```csharp
// ❌ BEFORE - Code duplication (in /Dtos/ShiftDto.cs)
public record ShiftSummaryDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid PosDeviceId { get; set; }
    // ... 10+ duplicate fields
    public decimal TotalSales { get; set; }  // Only custom field
    public string? UserName { get; set; }    // Only custom field
    public string? PosDeviceName { get; set; } // Only custom field
}
```

**Solution:** Use inheritance and move to feature folder
```csharp
// ✅ AFTER - Clean inheritance (in /Features/Shifts/GetShifts/ShiftSummaryDto.cs)
public record ShiftSummaryDto : ShiftResponseDto
{
    public decimal TotalSales { get; set; }  // Only custom field needed
}

// Enhanced ShiftResponseDto to include common display fields
public record ShiftResponseDto : ShiftDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UserName { get; set; }      // Shared display field
    public string? PosDeviceName { get; set; }  // Shared display field
}
```

**Benefits:**
- ✅ **Eliminated 10+ duplicate fields**
- ✅ **Moved to correct location** (feature folder)
- ✅ **Enhanced shared DTO** for reusability
- ✅ **Easier maintenance** and consistency

## 🆕 **PosDevice CRUD API - Complete Implementation**

### **📁 Feature Structure Pattern:**
```
/Features/PosDevices/
├── Commands/
│   ├── CreatePosDevice/
│   │   ├── CreatePosDeviceCommand.cs
│   │   └── CreatePosDeviceCommandValidator.cs
│   ├── UpdatePosDevice/
│   │   ├── UpdatePosDeviceCommand.cs  
│   │   └── UpdatePosDeviceCommandValidator.cs
│   └── DeletePosDevice/
│       └── DeletePosDeviceCommand.cs
├── Queries/
│   ├── GetPosDevices/
│   │   └── GetPosDevicesQuery.cs
│   └── GetPosDeviceById/
│       └── GetPosDeviceByIdQuery.cs
└── Dtos/
    └── (use shared DTOs in /Dtos/)
```

### **🎯 API Endpoints Implemented:**
1. **GET** `/pos-devices` → Get all devices (no filtering, no pagination)
2. **GET** `/pos-devices/{id}` → Get device by ID (basic info only)
3. **POST** `/pos-devices` → Create new device + validation
4. **PUT** `/pos-devices/{id}` → Update device + validation  
5. **DELETE** `/pos-devices/{id}` → Soft delete (IsDeleted = true)

### **🔧 Implementation Patterns Applied:**

#### **1. FluentValidation Pattern:**
```csharp
public class CreatePosDeviceCommandValidator : AbstractValidator<CreatePosDeviceCommand>
{
    public CreatePosDeviceCommandValidator()
    {
        RuleFor(x => x.PosDevice.Name)
            .NotEmpty().WithMessage("POS device name is required")
            .MaximumLength(200).WithMessage("POS device name cannot exceed 200 characters");

        RuleFor(x => x.PosDevice.Type)
            .IsInEnum().WithMessage("Invalid POS device type");
    }
}
```

#### **2. Database Operation Pattern:**
```csharp
// CREATE Pattern:
context.PosDevices.Add(posDevice);
var result = await context.SaveChangesAsync(cancellationToken);
if (result == 0)
{
    throw new NoEntityCreatedException("PosDevice");
}

// UPDATE Pattern:
context.PosDevices.Update(existingDevice);
var result = await context.SaveChangesAsync(cancellationToken);
if (result == 0)
{
    throw new NoEntityUpdatedException("PosDevice", request.Id);
}

// SOFT DELETE Pattern:
existingDevice.IsDeleted = true;
context.PosDevices.Update(existingDevice);
var result = await context.SaveChangesAsync(cancellationToken);
if (result == 0)
{
    throw new NoEntityDeletedException("PosDevice", request.Id);
}
```

#### **3. Query Pattern with Soft Delete:**
```csharp
// Always filter IsDeleted = false
var devices = await context.PosDevices
    .Where(d => !d.IsDeleted)
    .AsNoTracking()
    .OrderBy(d => d.Name)
    .ProjectToType<PosDeviceResponseDto>()
    .ToListAsync(cancellationToken);
```

#### **4. Success Message Pattern:**
```csharp
// Using SuccessMessageHelper for multilingual success messages
return new Response<object>(new { 
    message = SuccessMessageHelper.GetMessage("PosDeviceDeletedSuccessfully") 
});
```

### **🎯 Custom Exceptions Created:**
1. **PosDeviceNotFoundException** - When device not found
2. **DuplicatedDataException** - For SerialNumber/MacAddress duplicates  
3. **PosDeviceHasActiveOrdersException** - Cannot delete with active orders
4. **PosDeviceHasActiveShiftsException** - Cannot delete with active shifts

### **🌐 Language Messages Added (26 total):**
- **Exception Messages:** 24 messages (12 EN + 12 VI)
- **Success Messages:** 2 messages (1 EN + 1 VI)
- **Pattern:** `ExceptionName_param1_param2` for exceptions
- **Pattern:** `{Entity}{Action}Successfully` for success messages

## 🆕 **Success Message Helper System**

### **📋 SuccessMessageHelper Usage:**
```csharp
// Static helper for multilingual success messages
return new Response<object>(new { 
    message = SuccessMessageHelper.GetMessage("KeyName", parameters) 
});
```

### **🔧 Initialization Pattern:**
```csharp
// In LanguageExtensions.cs
public static async Task InitializeLanguageServicesAsync(this WebApplication app)
{
    // Initialize Language Cache
    using (var scope = app.Services.CreateScope())
    {
        var languageServiceHelper = scope.ServiceProvider.GetRequiredService<ILanguageServiceHelper>();
        await languageServiceHelper.InitializeAsync(app.Services);
    }

    // Initialize both helpers
    ExceptionMessageHelper.Initialize(app.Services);
    SuccessMessageHelper.Initialize(app.Services);
}
```

## 🔧 **Updated Coding Conventions**

### **6. Validation Rules (NEW)**
- **Always create FluentValidation validators** for Commands
- **Validate required fields:** NotEmpty(), NotNull()
- **Validate enums:** IsInEnum() for all enum properties
- **Validate lengths:** MaximumLength() for string fields
- **File naming:** `{CommandName}Validator.cs` in same folder as command

### **7. Database Operations (NEW)**
- **CREATE:** Use `Add()` (not `AddAsync()`), then check SaveChanges result
- **UPDATE:** Use `Update()` explicitly, then check SaveChanges result  
- **DELETE:** Soft delete with `IsDeleted = true`, then Update()
- **QUERIES:** Always filter `!d.IsDeleted` for soft delete support
- **Duplicate checks:** Include `!d.IsDeleted` in existence checks

### **8. Soft Delete Pattern (NEW)**
- **Base Entity:** All entities inherit `IsDeleted` from `Entity<T>`
- **Queries:** Always filter `Where(x => !x.IsDeleted)`
- **Delete operations:** Set `IsDeleted = true` instead of Remove()
- **Duplicate checks:** Exclude deleted records: `&& !d.IsDeleted`

### **9. Success Messages (NEW)**
- **Helper:** Use `SuccessMessageHelper.GetMessage("KeyName")`
- **Pattern:** Key format `{Entity}{Action}Successfully`
- **Languages:** Always provide both EN and VI versions
- **Database:** Store in same LanguageMessages table as exceptions

### **10. API Response Patterns (NEW)**
- **GET All:** No filtering, no pagination unless specifically requested
- **GET By ID:** Return basic entity data, no complex includes unless needed
- **POST/PUT:** Return created/updated entity as response
- **DELETE:** Return success message using SuccessMessageHelper
- **All endpoints:** Must have `LanguageClientAttribute`

### **11. DTO Architecture Rules (BOSS RULES - 2024)** 🔥
- **Split DTOs into 2 classes:**
  - **CreateDto/UpdateDto:** NO Id field - for create/update operations  
  - **ResponseDto:** WITH Id field - for get/response operations
  - **Pattern:** ResponseDto inherits from base DTO
- **Example from RoleDto.cs:**
  ```csharp
  // Base DTO - no Id, for create/update
  public record RoleDto
  {
      public string Name { get; set; } = null!;
      public string? Description { get; set; }
  }
  
  // Response DTO - with Id, inherits base DTO
  public record RoleResponseDto : RoleDto
  {
      public Guid Id { get; set; }
      public DateTime CreatedAt { get; set; }
      public DateTime? UpdatedAt { get; set; }
  }
  ```
- **DTO Organization:**
  - **Feature-specific DTOs:** Place inside feature folder (e.g., `Auth/Login/LoginResponseDto.cs`)
  - **Shared DTOs only:** Place in main `/Dtos/` folder
  - **Rule:** If DTO is used by only one feature → put in feature folder

- **DTO Inheritance Best Practices (NEW RULE):** 🆕
  - **ALWAYS use inheritance** to avoid code duplication
  - **Feature DTOs inherit from shared DTOs** instead of duplicating fields
  - **Example pattern:**
    ```csharp
    // ❌ WRONG - Code duplication
    public record ShiftSummaryDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        // ... duplicate all fields from ShiftResponseDto
        public decimal TotalSales { get; set; }  // Only this is custom
    }
    
    // ✅ CORRECT - Use inheritance
    public record ShiftSummaryDto : ShiftResponseDto
    {
        public decimal TotalSales { get; set; }  // Only add custom fields
    }
    ```
  - **Rule:** If a feature DTO needs most fields from base DTO + custom fields → inherit instead of duplicate
  - **Benefits:** Less code, easier maintenance, consistent structure

### **12. API Endpoint Naming Convention (BOSS RULES - 2024)** 🔥
- **ALWAYS use snake-case** for endpoint URLs
- **NOT camelCase** - this is critical
- **Examples:**
  - ✅ Correct: `/forgot-password-by-session`
  - ✅ Correct: `/pos-devices`
  - ❌ Wrong: `/forgotPasswordBySession`
  - ❌ Wrong: `/posDevices`

### **13. Database Migration Policy (ABSOLUTE RULE)** 🚨
- **❌ NO MIGRATIONS under ANY circumstances**
- **❌ NO `dotnet ef migrations add`**
- **❌ NO `dotnet ef database update`**
- **❌ NO schema changes whatsoever**
- **✅ Only SQL INSERT statements for LanguageMessages**
- **This rule overrides ALL other considerations**

## 📋 **Database Messages to Add**

### **For PosDevice Implementation:**
```sql
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
-- Exception Messages (24 messages)
('PosDeviceNotFoundException', 'POS device not found'),
('PosDeviceNotFoundException_vi', 'Không tìm thấy thiết bị POS'),
('DuplicatedDataException_field', 'Field ''{0}'' already exists'),
('DuplicatedDataException_field_vi', 'Trường ''{0}'' đã tồn tại'),
-- ... (see POSDEVICE_LANGUAGE_MESSAGES.md for complete list)

-- Success Messages (2 messages)
('PosDeviceDeletedSuccessfully', 'POS device deleted successfully'),
('PosDeviceDeletedSuccessfully_vi', 'Đã xóa thiết bị POS thành công');
```

## 🎯 **CRITICAL RULES - NEVER VIOLATE**

### **Database Rules:**
- ❌ **NO MIGRATIONS or database schema changes** - Only INSERT language messages
- ❌ **NO direct database access** - Always provide SQL INSERT statements
- ❌ **NO `dotnet ef migrations add` EVER** - Boss rule absolute
- ❌ **NO `dotnet ef database update` EVER** - Boss rule absolute
- ✅ **Only add language messages** to existing LanguageMessages table

### **DTO Rules (BOSS REQUIREMENTS):**
- ✅ **Split DTOs into 2 classes:** CreateDto (no Id) + ResponseDto (with Id)
- ✅ **Feature-specific DTOs** must go in feature folder, not main /Dtos/
- ✅ **Follow RoleDto pattern** for all new DTOs

### **API Endpoint Rules (BOSS REQUIREMENTS):**
- ✅ **ALWAYS snake-case endpoints** (e.g., `/forgot-password-by-session`)
- ❌ **NEVER camelCase endpoints** (e.g., `/forgotPasswordBySession`)

### **File Operation Rules:**
- ✅ **Always use absolute paths** starting with `/` or drive letter
- ✅ **Chunk large files** into ≤30 lines maximum per write operation
- ✅ **Use edit_block** for precise edits with exact text matching

### **Language Rules:**
- ✅ **All user-facing messages** must support multilingual (EN/VI)
- ✅ **Exception messages** use ExceptionMessageHelper.GetMessage()
- ✅ **Success messages** use SuccessMessageHelper.GetMessage()
- ✅ **All API endpoints** must have LanguageClientAttribute

### **Architecture Rules:**
- ✅ **Follow CQRS** - Commands for write, Queries for read
- ✅ **Use MediatR** for all handlers  
- ✅ **FluentValidation** for all Command validators
- ✅ **Soft delete** with IsDeleted for all delete operations
- ✅ **Check SaveChanges** result and throw appropriate exceptions

**🎯 Save this document and reference it in your next chat for seamless continuation!** 📋
