﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BuildingBlocks.BKAVInvoiceServices.Models.Response
{
    public class PushDataResponse
    {
        public string? d { get; set; }
    }
    public class PartnerResponseData<T>
    {
        public int Status { get; set; }
        public T? Object { get; set; }
        public object? Permission { get; set; }
        public string? Code { get; set; }
        public bool isOk { get; set; }
        public bool isError { get; set; }
    }
}
