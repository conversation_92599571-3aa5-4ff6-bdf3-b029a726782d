﻿namespace BuildingBlocks.BKAVInvoiceServices.Models.Response
{
    public class CreateInvoiceDataResponse
    {
        public long PartnerInvoiceID { get; set; }
        public string? PartnerInvoiceStringID { get; set; }
        public string? InvoiceGUID { get; set; }
        public string? InvoiceForm { get; set; }
        public string? InvoiceSerial { get; set; }
        public int InvoiceNo { get; set; }
        public string? MTC { get; set; }
        public string? MaCuaCQT { get; set; }
        public int Status { get; set; }
        public string? MessLog { get; set; }
    }
}