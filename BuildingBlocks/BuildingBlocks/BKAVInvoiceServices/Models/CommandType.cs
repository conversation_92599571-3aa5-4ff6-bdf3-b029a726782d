﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BuildingBlocks.BKAVInvoiceServices.Models
{
   public static  class CommandType
    {
        public const int CreateInvoiceWithFormSerial = 110; // T<PERSON><PERSON>, Client tự cấp InvoiceForm, InvoiceSerial; InvoiceNo = 0 (tạo Hóa đơn mới)        public const int GetInvoiceDataFilePDF = 816;//Lấy File XML Base 64 theo InvoiceGUID
        public const int GetInvoiceDataFilePDF = 816;//Lấy File XML Base 64 theo InvoiceGUID

    }
}
