﻿using BuildingBlocks.BKAVInvoiceServices.Helper;
using BuildingBlocks.BKAVInvoiceServices.Models.Request;
using BuildingBlocks.BKAVInvoiceServices.Models.Response;
using Newtonsoft.Json;
using System.Text;

namespace BuildingBlocks.BKAVInvoiceServices.Models
{
    public static class BkavInvoiceService
    {
        public static async Task<long> CreateInvoiceDemo(string host,
            string base64Key, string base64IV, string partnerGUID,
            CreateInvoiceBkavDataRequest data)
        {
            byte[] key = Convert.FromBase64String(base64Key);
            byte[] iv = Convert.FromBase64String(base64IV);
            var client = new HttpClient();

            var encryptCmd = new CommmandData<CreateInvoiceBkavDataRequest>
            {
                CmdType = CommandType.CreateInvoiceWithFormSerial,
                CommandObject = new List<CreateInvoiceBkavDataRequest>
                {
                    data
                }
            };
            string jsonDataInvoice = JsonConvert.SerializeObject(encryptCmd);
            var gZipData = BKAVInvoiceServiceHelper.EncryptGzip(jsonDataInvoice, base64Key, base64IV);
            string dataEncrypt = BKAVInvoiceServiceHelper.EncryptData(gZipData, base64Key, base64IV);

            var dataPush = new PushDataRequest
            {
                partnerGUID = partnerGUID,
                CommandData = dataEncrypt
            };
            var dataJson = JsonConvert.SerializeObject(dataPush);

            var content = new StringContent(dataJson, Encoding.UTF8, "application/json");

            var send = await client.PostAsync(host, content);
            if (!send.IsSuccessStatusCode)
            {
                Console.WriteLine($"Error: {send.StatusCode}, {await send.Content.ReadAsStringAsync()}");
                throw new Exception($"Error: {send.StatusCode}, {await send.Content.ReadAsStringAsync()}");
            }
            var responseContent = await send.Content.ReadAsStringAsync();

            Console.WriteLine("Response: " + responseContent);
            var response = JsonConvert.DeserializeObject<PushDataResponse>(responseContent);
            Console.WriteLine("Response Data: " + response!.d);
            if (response.d == null)
            {
                throw new Exception("Response data is null");
            }

            var dataByte = BKAVInvoiceServiceHelper.DecryptData(response.d, base64Key, base64IV);
            string plainText = BKAVInvoiceServiceHelper.ExtractFirstFileContent(dataByte);
            var decryptData = JsonConvert.DeserializeObject<PartnerResponseData<string>>(plainText);
            Console.WriteLine("Decrypted data: " + decryptData);
            if (!decryptData!.isOk)
            {
                throw new Exception($"Error:  StatusCode: {decryptData.Status}");
            }

            var result = JsonConvert.DeserializeObject<CreateInvoiceDataResponse>(decryptData.Object!);
            if (result == null)
            {
                throw new Exception("Result is null");
            }
            return result.PartnerInvoiceID!;
        }

        public static async Task<string> FilePDFDemo(string host,
            GetInvoicePdfRequest partnerInvoice,
             string base64Key, string base64IV, string partnerGUI)
        {
            var encryptPdfCmd = new CommmandData<GetInvoicePdfRequest>
            {
                CmdType = CommandType.GetInvoiceDataFilePDF,
                CommandObject = new List<GetInvoicePdfRequest>
                {
                    new GetInvoicePdfRequest
                    {
                        PartnerInvoiceID = partnerInvoice.PartnerInvoiceID,
                        PartnerInvoiceStringID = partnerInvoice.PartnerInvoiceStringID,
                }}
            };
            string jsonDataPdf = JsonConvert.SerializeObject(encryptPdfCmd);
            var gZipData = BKAVInvoiceServiceHelper.EncryptGzip(jsonDataPdf, base64Key, base64IV);
            string dataEncrypt = BKAVInvoiceServiceHelper.EncryptData(gZipData, base64Key, base64IV);
            var dataPush = new PushDataRequest
            {
                partnerGUID = partnerGUI,
                CommandData = dataEncrypt
            };
            var dataJson = JsonConvert.SerializeObject(dataPush);
            var client = new HttpClient();
            var content = new StringContent(dataJson, Encoding.UTF8, "application/json");
            var send = await client.PostAsync(host, content);
            if (!send.IsSuccessStatusCode)
            {
                Console.WriteLine($"Error: {send.StatusCode}, {await send.Content.ReadAsStringAsync()}");
                throw new Exception($"Error: {send.StatusCode}, {await send.Content.ReadAsStringAsync()}");
            }
            var responseContent = await send.Content.ReadAsStringAsync();
            Console.WriteLine("Response: " + responseContent);
            var response = JsonConvert.DeserializeObject<PushDataResponse>(responseContent);
            Console.WriteLine("Response Data: " + response!.d);
            if (response.d == null)
            {
                throw new Exception("Response data is null");
            }
            var dataByte = BKAVInvoiceServiceHelper.DecryptData(response.d, base64Key, base64IV);
            string plainText = BKAVInvoiceServiceHelper.ExtractFirstFileContent(dataByte);
            var decryptData = JsonConvert.DeserializeObject<PartnerResponseData<string>>(plainText);
            Console.WriteLine("Decrypted data: " + decryptData);
            if (!decryptData!.isOk)
            {
                throw new Exception($"Error:  StatusCode: {decryptData.Status}");
            }
            var result = JsonConvert.DeserializeObject<List<GetInvoicePdfResponse>>(decryptData.Object!);
            if (result == null || result.Count == 0)
            {
                throw new Exception("Result is null or empty");
            }
            var pdfFile = result.FirstOrDefault();
            if (pdfFile == null)
            {
                throw new Exception("PDF file content is null or empty");
            }
            return "https://demo.ehoadon.vn/" + pdfFile.MessLog;
        }
    }
}