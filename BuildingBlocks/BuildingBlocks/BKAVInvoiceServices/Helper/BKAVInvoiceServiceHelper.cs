﻿using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;

namespace BuildingBlocks.BKAVInvoiceServices.Helper
{
    public static class BKAVInvoiceServiceHelper
    {
        public static byte[] EncryptGzip(string jsonOrXml, string base64Key, string base64IV)
        {
            // 1. Nén GZIP dữ liệu text
            byte[] inputBytes = Encoding.UTF8.GetBytes(jsonOrXml);
            byte[] gzipBytes;
            using (var ms = new MemoryStream())
            {
                using (var gzip = new GZipStream(ms, CompressionLevel.Optimal))
                {
                    gzip.Write(inputBytes, 0, inputBytes.Length);
                }
                gzipBytes = ms.ToArray();
            }
            return gzipBytes;
        }

        public static string EncryptData(byte[] data, string key, string iv)
        {
            byte[] encryptedBytes;
            byte[] keyByte = Convert.FromBase64String(key);
            byte[] ivByte = Convert.FromBase64String(iv);
            using (Aes aes = Aes.Create())
            {
                aes.Key = keyByte;
                aes.IV = ivByte;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                using (var encryptor = aes.CreateEncryptor())
                using (var msOut = new MemoryStream())
                using (var cs = new CryptoStream(msOut, encryptor, CryptoStreamMode.Write))
                {
                    cs.Write(data, 0, data.Length);
                    cs.FlushFinalBlock();
                    encryptedBytes = msOut.ToArray();
                }
            }
            return Convert.ToBase64String(encryptedBytes);
        }

        public static byte[] DecryptData(string dataEncrypt, string key, string iv)
        {
            byte[] dataByte;
            byte[] cipherTextByte = Convert.FromBase64String(dataEncrypt);

            byte[] keyByte = Convert.FromBase64String(key);
            byte[] ivByte = Convert.FromBase64String(iv);
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = keyByte;
                aesAlg.IV = ivByte;
                aesAlg.Mode = CipherMode.CBC;
                aesAlg.Padding = PaddingMode.PKCS7;

                using (var decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV))
                using (var msIn = new MemoryStream(cipherTextByte))
                using (var cs = new CryptoStream(msIn, decryptor, CryptoStreamMode.Read))
                using (var msOut = new MemoryStream())
                {
                    cs.CopyTo(msOut);
                    dataByte = msOut.ToArray();
                    // if save file
                    //File.WriteAllBytes("debug1.zip", dataByte);
                    //plainText = ExtractFirstFileContent(dataByte);
                }
            }
            return dataByte;
        }

        public static string ExtractFirstFileContent(byte[] zipBytes)
        {
            using (var ms = new MemoryStream(zipBytes))
            using (var gzip = new GZipStream(ms, CompressionMode.Decompress))
            {
                using (var outStream = new MemoryStream())
                {
                    gzip.CopyTo(outStream);
                    byte[] decompressed = outStream.ToArray();
                    // Nếu bạn biết nó là file text
                    string content = System.Text.Encoding.UTF8.GetString(decompressed);
                    return content;
                }
            }
        }
    }
}