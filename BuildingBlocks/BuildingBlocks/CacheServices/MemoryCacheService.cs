using System.Collections.Concurrent;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;

namespace BuildingBlocks.CacheServices;

/// <summary>
/// Provides in-memory caching services
/// </summary>
public class MemoryCachedService(IDistributedCache distributedCache) : ICachedService
{
    private static readonly ConcurrentDictionary<string, bool> CachedKeys = new();
    private readonly IDistributedCache distributedCache = distributedCache;

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        string? cachedValue = await distributedCache.GetStringAsync(
            key, cancellationToken);
        return cachedValue != null ? JsonConvert.DeserializeObject<T>(cachedValue)
            : null;
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        await distributedCache.RemoveAsync(key, cancellationToken);
        CachedKeys.TryRemove(key, out bool _);
    }

    public async Task RemoveByPrefixAsync(string prefixKey, CancellationToken cancellationToken = default)
    {
        IEnumerable<Task> tasks = CachedKeys.Keys.Where(k => k.StartsWith(prefixKey))
             .Select(k => RemoveAsync(k, cancellationToken));

        await Task.WhenAll(tasks);
    }

    public async Task SetAsync<T>(string key, T value, int expireSecond = 600, CancellationToken cancellationToken = default) where T : class
    {
        string cachedValue = JsonConvert.SerializeObject(value,
           new JsonSerializerSettings()
           {
               ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
           });

        await distributedCache.SetStringAsync(key, cachedValue,
            new DistributedCacheEntryOptions()
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(expireSecond),
            }, cancellationToken);

        CachedKeys.TryAdd(key, false);
    }
}