namespace BuildingBlocks.CacheServices;

public interface ICachedService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
        where T : class;
    Task SetAsync<T>(string key, T value, int expireSecond = 600, CancellationToken cancellationToken = default)
        where T : class;
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);

    Task RemoveByPrefixAsync(string prefixKey, CancellationToken cancellationToken = default);
}