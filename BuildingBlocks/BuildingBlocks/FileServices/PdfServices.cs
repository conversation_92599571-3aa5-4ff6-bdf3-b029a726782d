﻿using Microsoft.AspNetCore.Hosting;
using Newtonsoft.Json;
using Syncfusion.HtmlConverter;
using Syncfusion.Pdf;
using Syncfusion.Pdf.Graphics;

namespace BuildingBlocks.FileServices
{
    public static  class PdfServices
    {
        public static bool ConvertHtmlToPdf(IWebHostEnvironment env, string htmlContent, string outputFileName)
        {
            try
            {
                /// Add font nếu cần

                //    string fontFaceDefinitions = @"
                //    <style>
                //        @font-face {
                //            font-family: 'Times New Roman';
                //            src: url('" + Path.Combine(env.WebRootPath, "Font", "Times New Roman.ttf").Replace("\\", "/") + @"');
                //        }
                //    </style>
                //";

                var htmlConverter = new HtmlToPdfConverter();
                htmlConverter.ConverterSettings = new BlinkConverterSettings()
                {
                    PdfPageSize = PdfPageSize.A4,
                    Orientation = PdfPageOrientation.Portrait,
                    Margin = new PdfMargins { All = 0 },
                    Scale = 1.05f,
                    WaitForExternalFonts = true,
                    CommandLineArguments = { "--no-sandbox", "--disable-setuid-sandbox" },
                };
                //PdfDocument pdfDocument = htmlConverter.Convert(fontFaceDefinitions + htmlContent, "");
                PdfDocument pdfDocument = htmlConverter.Convert(htmlContent);

                // Lưu file PDF
                using (FileStream fileStream = new FileStream(outputFileName, FileMode.Create, FileAccess.ReadWrite))
                {
                    pdfDocument.Save(fileStream);
                }

                // Dispose document sau khi sử dụng
                pdfDocument.Close(true);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi tạo PDF: {JsonConvert.SerializeObject(ex.InnerException)} || {ex.StackTrace} || {ex.Message}");
                return false;
            }
        }
    }
}