﻿using CsvHelper;
using Microsoft.AspNetCore.Http;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Syncfusion.XlsIO;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Reflection;
using System.Text;

namespace BuildingBlocks.FileServices
{
    public class ExcelServices
    {
        public static async Task<List<T>> ImportFile<T>(IFormFile file, Dictionary<string, PropertyInfo> mappings, string sheetName = "", int headerRow = 0)
        {
            var fileExtension = Path.GetExtension(file.FileName).ToLower();
            return fileExtension switch
            {
                ".csv" => ProcessCsvFile<T>(file, mappings),
                ".xlsx" or ".xls" => await ProcessExcelFile<T>(file, mappings, sheetName, headerRow),
                _ => throw new Exception("Unsupported file format")
            };
        }

        public static void ExportData<T>(IEnumerable<T> exportModels, string teampalteUrl, string exportUrl, int startRow = 1, int startCol = 1, int headerRow = 1)
        {
            using ExcelEngine excelEngine = new();
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Excel2013;
            FileStream inputStream = new FileStream(teampalteUrl, FileMode.Open, FileAccess.Read);
            Syncfusion.XlsIO.IWorkbook workbook = application.Workbooks.Open(inputStream);
            IWorksheet worksheetCompanies = workbook.Worksheets[0];

            // Style cho header nếu cần
            IStyle headerStyle = workbook.Styles.Add("HeaderStyle");
            headerStyle.Font.Bold = true;

            worksheetCompanies.ImportData(exportModels, startRow, startCol, false);

            // Mapping display name (hoặc property name) đã Normalize
            var props = typeof(T).GetProperties();
            var displayMap = props.ToDictionary(
                prop =>
                {
                    var displayAttr = prop.GetCustomAttributes(typeof(DisplayAttribute), true).FirstOrDefault() as DisplayAttribute;
                    var name = displayAttr?.Name?.Trim() ?? prop.Name;
                    string data=  NormalizeHeader(name);
                    Console.WriteLine($"Property: {prop.Name}, Display Name: {name}, Normalized: {data}");
                    return data;
                },
                prop => prop
            );

            // Đọc header từ file Excel và Normalize
            var headerToColIndex = new Dictionary<string, int>();
            int maxCol = worksheetCompanies.Columns.Length;
            for (int col = 0; col < maxCol; col++)
            {
                string? cellValue = worksheetCompanies[headerRow, startCol + col].Value?.ToString();
                var normalizedHeader = NormalizeHeader(cellValue!);
                Console.WriteLine($"Header raw: {cellValue}, Normalized: {normalizedHeader}");
                if (!string.IsNullOrEmpty(normalizedHeader))
                {
                    headerToColIndex[normalizedHeader] = startCol + col;
                }
            }

            // Gán value theo mapping đã Normalize
            int currentRow = startRow;
            foreach (var item in exportModels)
            {
                foreach (var header in headerToColIndex.Keys)
                {
                    if (displayMap.TryGetValue(header, out var prop))
                    {
                        var value = prop.GetValue(item);
                        worksheetCompanies[currentRow, headerToColIndex[header]].Value2 = value;
                    }
                }
                currentRow++;
            }

            // Format border, save file
            int rowCount = exportModels.Count();
            int colCount = typeof(T).GetProperties().Count();
            int endRow = startRow + rowCount - 1;
            int endCol = startCol + colCount - 1;
            var dataRange = worksheetCompanies.Range[startRow, startCol, endRow, endCol];
            dataRange.CellStyle.Borders[ExcelBordersIndex.DiagonalUp].LineStyle = ExcelLineStyle.None;
            dataRange.CellStyle.Borders[ExcelBordersIndex.DiagonalDown].LineStyle = ExcelLineStyle.None;
            dataRange.CellStyle.Borders.Color = ExcelKnownColors.Black;

            FileStream file = new FileStream(exportUrl, FileMode.Create, FileAccess.ReadWrite);
            workbook.SaveAs(file);
            inputStream.Dispose();
            inputStream.Close();
            file.Dispose();
            file.Close();
        }

        private static string NormalizeHeader(string input)
        {
            if (string.IsNullOrWhiteSpace(input)) return string.Empty;
            // Loại bỏ khoảng trắng, chuyển về chữ thường
            string temp = input.Trim().ToLowerInvariant();
            // Loại bỏ dấu tiếng Việt (Unicode Normalization Form D)
            string normalized = temp.Normalize(NormalizationForm.FormD);
            var sb = new StringBuilder();
            foreach (var c in normalized)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark && char.IsLetterOrDigit(c))
                    sb.Append(c);
            }
            return sb.ToString().Normalize(NormalizationForm.FormC);
        }

        private static List<T> ProcessCsvFile<T>(IFormFile file, Dictionary<string, PropertyInfo> mappings)
        {
            var results = new List<T>();

            using (var reader = new StreamReader(file.OpenReadStream()))
            using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
            {
                csv.Read();
                csv.ReadHeader();
                ValidateHeaders(csv.HeaderRecord!, mappings.Keys.ToArray());

                while (csv.Read())
                {
                    try
                    {
                        var item = Activator.CreateInstance<T>();
                        foreach (var mapping in mappings)
                        {
                            var value = csv.GetField(mapping.Key);
                            if (!string.IsNullOrEmpty(value))
                            {
                                var convertedValue = ConvertValue(value, mapping.Value.PropertyType);
                                mapping.Value.SetValue(item, convertedValue);
                            }
                        }

                        results.Add(item);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing row {csv.Context.Parser!.Row}: {ex.Message}");
                    }
                }
            }

            return results;
        }

        private static async Task<List<T>> ProcessExcelFile<T>(IFormFile file, Dictionary<string, PropertyInfo> mappings, string sheetName, int headerRowNum = 0)
        {
            var results = new List<T>();

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0;

                NPOI.SS.UserModel.IWorkbook workbook = file.FileName.EndsWith(".xlsx")
                    ? new XSSFWorkbook(stream)
                    : new HSSFWorkbook(stream);

                var sheet = !string.IsNullOrEmpty(sheetName) ? workbook.GetSheet(sheetName) : workbook.GetSheetAt(0);
                var headerRow = sheet.GetRow(headerRowNum);
                var headerIndexes = GetHeaderIndexes(headerRow, mappings.Keys.ToArray());

                for (int rowNum = headerRowNum + 1; rowNum <= sheet.LastRowNum; rowNum++)
                {
                    var row = sheet.GetRow(rowNum);
                    if (row == null) continue;
                    if (row.Cells.All(d => d.CellType == NPOI.SS.UserModel.CellType.Blank)) continue;

                    try
                    {
                        var item = Activator.CreateInstance<T>();
                        foreach (var mapping in mappings)
                        {
                            if (headerIndexes.TryGetValue(mapping.Key, out int index))
                            {
                                var cell = row.GetCell(index);
                                var value = GetCellValue(cell);
                                if (!string.IsNullOrEmpty(value))
                                {
                                    var convertedValue = ConvertValue(value, mapping.Value.GetModifiedPropertyType());
                                    mapping.Value.SetValue(item, convertedValue);
                                }
                            }
                        }

                        results.Add(item);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Error processing row {rowNum + 1}: {ex.Message}");
                    }
                }
            }

            return results;
        }

        private static object? ConvertValue(string value, Type targetType)
        {
            if (string.IsNullOrEmpty(value)) return null;

            if (targetType == typeof(Guid))
                return Guid.Parse(value);
            var data = Type.GetTypeCode(targetType);

            return Type.GetTypeCode(targetType) switch
            {
                TypeCode.DateTime => DateTime.Parse(value),
                TypeCode.Decimal => decimal.Parse(value),
                TypeCode.Double => double.Parse(value),
                TypeCode.Int32 => int.Parse(value),
                TypeCode.Boolean => bool.Parse(value),
                _ => value
            };
        }

        private static Dictionary<string, int> GetHeaderIndexes(IRow headerRow, string[] requiredHeaders)
        {
            var headerIndexes = new Dictionary<string, int>();
            for (int i = 0; i < headerRow.LastCellNum; i++)
            {
                var cell = headerRow.GetCell(i);
                if (cell != null)
                {
                    headerIndexes[cell.StringCellValue.Trim()] = i;
                }
            }

            ValidateHeaders(headerIndexes.Keys.ToArray(), requiredHeaders);
            return headerIndexes;
        }

        private static void ValidateHeaders(string[] headers, string[] requiredHeaders)
        {
            var missingHeaders = requiredHeaders.Where(h => !headers.Contains(h));
            if (missingHeaders.Any())
                throw new Exception($"Missing required headers: {string.Join(", ", missingHeaders)}");
        }

        private static string GetCellValue(ICell cell)
        {
            if (cell == null) return string.Empty;

            return cell.CellType switch
            {
                NPOI.SS.UserModel.CellType.Numeric => DateUtil.IsCellDateFormatted(cell)
                    ? cell.DateCellValue!.Value.ToString("yyyy-MM-dd")
                    : cell.NumericCellValue.ToString(),
                NPOI.SS.UserModel.CellType.String => cell.StringCellValue,
                NPOI.SS.UserModel.CellType.Boolean => cell.BooleanCellValue.ToString(),
                NPOI.SS.UserModel.CellType.Formula => GetFormulaCellValue(cell),
                _ => string.Empty
            };
        }

        private static string GetFormulaCellValue(ICell cell)
        {
            try
            {
                return cell.NumericCellValue.ToString();
            }
            catch
            {
                return cell.StringCellValue;
            }
        }

        public static Dictionary<string, PropertyInfo> GetPropertyMappings<T>()
        {
            var type = typeof(T);
            var dict = new Dictionary<string, PropertyInfo>();

            foreach (var prop in type.GetProperties())
            {
                var displayAttr = prop.GetCustomAttribute<DisplayAttribute>();
                if (displayAttr != null && !string.IsNullOrEmpty(displayAttr.Name))
                {
                    dict.Add(displayAttr.Name, prop);
                }
            }

            return dict;
        }

        public static bool IsValidFileExtension(string extension)
        {
            return extension == ".xlsx" || extension == ".xls" || extension == ".csv";
        }
    }
}