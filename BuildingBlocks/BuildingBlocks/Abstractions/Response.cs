﻿namespace BuildingBlocks.Abstractions;

public record Response<TEntity> where TEntity : class
{
    public Response() { }

    public Response(TEntity data)
    {
        Data = data;
        Code = "000";
    }

    public Response(TEntity data, string message)
    {
        Data = data;
        Message = message;
        Code = "000";
    }

    public Response(TEntity data, string message, string code)
    {
        Data = data;
        Message = message;
        Code = code;
    }

    public string Code { get; set; } = "000";
    public string Message { get; set; } = string.Empty;
    public object? Errors { get; set; } = null;
    public string? TraceId { get; set; }
    public TEntity? Data { get; set; }
}
