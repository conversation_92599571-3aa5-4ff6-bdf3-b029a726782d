using System.Net.Mail;
using System.Net;

namespace BuildingBlocks.MailServices;

/// <summary>
/// Enhanced MailServices with SemaphoreSlim for concurrency control and proper resource management
/// Base implementation by <PERSON><PERSON><PERSON>, enhanced with thread safety and SendAsync method
/// </summary>
public static class MailServices
{
    private static readonly SemaphoreSlim semaphore = new(25); // Giới hạn số luồng đồng thời

    /// <summary>
    /// Send simple email without attachments
    /// </summary>
    public static async Task<string> SendAsync(
        int port,
        string host,
        string password,
        string fromMail,
        string toMail,
        string displayName,
        string subject,
        string body)
    {
        if (!await semaphore.WaitAsync(TimeSpan.FromMinutes(1)))
        {
            return "Error: Send mail timeout";
        }

        try
        {
            var result = await SendWithAttachmentsAsync(
                port, host, password, fromMail, toMail, displayName, 
                subject, body, new List<string>(), []);
            
            return result.Item1 == "Success" ? string.Empty : result.Item2;
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// Send email with attachments 
    /// </summary>
    public static async Task<(string, string)> SendWithAttachmentsAsync(
        int port,
        string host,
        string password,
        string fromMail,
        string toMail,
        string displayName,
        string subject,
        string body,
        List<string> ccMails,
        string[] attachments)
    {
        if (!await semaphore.WaitAsync(TimeSpan.FromMinutes(2)))
        {
            return ("Error", "Send mail timeout");
        }

        string messageError = "Success";
        string messageLog = "Thành công";
        Dictionary<string, Attachment> attachmentsList = new();
        
        try
        {
            MailMessage message = new();
            SmtpClient smtp = new();
            message.From = new MailAddress(fromMail, displayName);
            message.To.Add(new MailAddress(toMail));
            foreach (var ccMail in ccMails)
            {
                message.CC.Add(ccMail);
            }
            message.Subject = subject;
            message.IsBodyHtml = true;
            message.Body = body;
            foreach (var attachment in attachments)
            {
                if (File.Exists(attachment))
                {
                    var attach = new Attachment(attachment);
                    message.Attachments.Add(attach);
                    attachmentsList.Add(attachment, attach);
                }
            }
            smtp.Port = port;
            smtp.Host = host;
            smtp.EnableSsl = true;
            smtp.UseDefaultCredentials = false;
            smtp.Credentials = new NetworkCredential(fromMail, password);
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network; // Ensure network delivery
            smtp.Timeout = 1000 * 300;
            await smtp.SendMailAsync(message);
        }
        catch (Exception ex)
        {
            Console.WriteLine("SendWithAttachmentsAsync " + ex.Message);
            messageError = "Error";
            messageLog = ex.Message;
        }
        finally
        {
            // Cleanup attachments và release semaphore
            foreach (var item in attachmentsList)
            {
                item.Value.Dispose();
                File.Delete(item.Key);
            }
            semaphore.Release();
        }

        return (messageError, messageLog);
    }
}
