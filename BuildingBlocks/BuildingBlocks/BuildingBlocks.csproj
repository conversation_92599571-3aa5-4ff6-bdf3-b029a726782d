﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>    
    <PackageReference Include="CsvHelper" Version="33.1.0" />    
    <PackageReference Include="FluentValidation" Version="12.0.0" />    
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />    
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />    
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.FeatureManagement.AspNetCore" Version="4.1.0" />
    <PackageReference Include="Nanoid" Version="3.1.0" />
    <PackageReference Include="NPOI" Version="2.7.3" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageReference Include="Syncfusion.HtmlToPdfConverter.Net.Linux" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Pdf.Net.Core" Version="29.2.9" />
    <PackageReference Include="Syncfusion.XlsIO.Net.Core" Version="29.2.9" />
  </ItemGroup>

</Project>
