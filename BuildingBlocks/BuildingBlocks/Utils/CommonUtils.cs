using NanoidDotNet;

namespace BuildingBlocks.Utils;

public static class CommonUtils
{
    /// <summary>
    /// Generates a unique code using a nano id generator
    /// </summary>
    /// <returns>New code</returns>
    public static string GenerateNewCode(int size = 10)
    {
        return Nanoid.Generate("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", size);
    }

    /// <summary>
    /// Generates a unique number code using a nano id generator
    /// </summary>
    /// <returns>New number</returns>
    public static string GenerateNewNumber(int size = 10)
    {
        return Nanoid.Generate("0123456789", size);
    }
}