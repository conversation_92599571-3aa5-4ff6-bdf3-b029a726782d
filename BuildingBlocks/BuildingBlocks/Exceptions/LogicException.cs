﻿namespace BuildingBlocks.Exceptions
{
    public class LogicException : Exception
    {
        public virtual string ErrorCode { get; } = "999"; // Default error code
        
        public LogicException(string message) : base(message)
        {
        }

        public LogicException(string message, string details) : base(message)
        {
            Details = details;
        }

        public string? Details { get; }
    }
}