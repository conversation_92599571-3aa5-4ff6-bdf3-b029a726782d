﻿using BuildingBlocks.Abstractions;
using FluentValidation;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace BuildingBlocks.Exceptions.Handler;

public class CustomExceptionHandler
    (ILogger<CustomExceptionHandler> logger)
    : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(
        HttpContext context,
        Exception exception,
        CancellationToken cancellationToken)
    {
        logger.LogError(
            "Error Message: {exceptionMessage}, Time of occurrence {time}",
            exception.Message, DateTime.UtcNow);

        (string Detail, string Message, int StatusCode, string StatusCodeString) = exception switch
        {
            ValidationException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status400BadRequest,
                StatusCodes.Status400BadRequest.ToString()
            ),
            LogicException appLogicException =>
            (
                appLogicException.Message,
                appLogicException.Message,
                context.Response.StatusCode = StatusCodes.Status200OK,
                appLogicException.ErrorCode
            ),
            BadRequestException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status400BadRequest,
                StatusCodes.Status400BadRequest.ToString()
            ),
            UnAuthorizeException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status401Unauthorized,
                StatusCodes.Status401Unauthorized.ToString()
            ),
            ForbiddenException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status403Forbidden,
                StatusCodes.Status403Forbidden.ToString()
            ),
            NotFoundException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status404NotFound,
                StatusCodes.Status404NotFound.ToString()
            ),
            InternalServerException =>
            (
                exception.GetType().Name,
                exception.Message,
                StatusCode = StatusCodes.Status500InternalServerError,
                StatusCodes.Status500InternalServerError.ToString()
            ),
            MaintenanceException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status503ServiceUnavailable,
                StatusCodes.Status503ServiceUnavailable.ToString()
            ),
            SavingEntityException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status200OK,
                "991" // Custom code for saving entity exceptions
            ),
            ExistedException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status409Conflict,
                StatusCodes.Status409Conflict.ToString()
            ),
            _ =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status500InternalServerError,
                StatusCodes.Status500InternalServerError.ToString()
            )
        };

        var problemDetails = new Response<object>
        {
            Message = Message,
            Errors = Detail,
            Code = StatusCodeString,
            TraceId = context.TraceIdentifier,
        };

        if (exception is ValidationException validationException)
        {
            problemDetails.Errors = validationException.Errors
                .Select(e => new { e.PropertyName, e.ErrorMessage })
                .ToList();
        }

        await context.Response.WriteAsJsonAsync(problemDetails, cancellationToken: cancellationToken);
        return true;
    }
}
