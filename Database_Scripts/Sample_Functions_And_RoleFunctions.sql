
-- =====================================================
-- SIMPLE ROLE ASSIGNMENT (using existing User.RoleId)
-- =====================================================

-- Since User table already has RoleId field, just update users directly
-- No need for separate UserRole junction table

-- Example: Assign roles to users using UPDATE statements
-- Replace with actual User IDs from your database

-- Assign admin role to admin user
-- UPDATE "Users" SET "RoleId" = 'r0000000-0000-0000-0000-000000000001' WHERE "Id" = 'u0000000-0000-0000-0000-000000000001';

-- Assign POS Manager role to manager user  
-- UPDATE "Users" SET "RoleId" = 'r0000000-0000-0000-0000-000000000002' WHERE "Id" = 'u0000000-0000-0000-0000-000000000002';

-- Assign POS Cashier role to cashier user
-- UPDATE "Users" SET "RoleId" = 'r0000000-0000-0000-0000-000000000003' WHERE "Id" = 'u0000000-0000-0000-0000-000000000003';

-- Assign POS Supervisor role to supervisor user
-- UPDATE "Users" SET "RoleId" = 'r0000000-0000-0000-0000-000000000004' WHERE "Id" = 'u0000000-0000-0000-0000-000000000004';

-- =====================================================
-- JWT TOKEN CLAIMS (same as before)
-- =====================================================

/*
When a user logs in, the JWT token will contain:

For Admin User (RoleId = admin role):
- ClaimTypes.Role: "ADMIN" (role name)
- All permission claims: "RMR", "RMC", "RMU", "RMD", "UMR", "UMC", etc.

For POS Manager (RoleId = manager role):
- ClaimTypes.Role: "POS_MANAGER" (role name)  
- Permission claims: "RMR", "RMU", "UMC", "UMR", "UMU", "UMD", "ORC", "ORR", "ORU", "ORA", etc.

Authorization Examples:
======================
.RequireAuthorization(new AuthorizeAttribute { Roles = "RMR" })  // Role Management Read
.RequireAuthorization(new AuthorizeAttribute { Roles = "UMU" })  // User Management Update  
.RequireAuthorization(new AuthorizeAttribute { Roles = "ORC" })  // Order Management Create
.RequireAuthorization(new AuthorizeAttribute { Roles = "ADMIN,RMR" }) // Admin OR Role Management Read
*/

-- =====================================================
-- VIEW USER ROLE ASSIGNMENTS
-- =====================================================

-- Check which users have which roles
-- SELECT 
--     u."UserName",
--     u."FullName", 
--     r."Name" as "RoleName",
--     r."Type" as "RoleType"
-- FROM "Users" u
-- LEFT JOIN "Roles" r ON u."RoleId" = r."Id"
-- WHERE u."IsDeleted" = false 
-- ORDER BY u."UserName";

-- Check users without roles
-- SELECT "UserName", "FullName" 
-- FROM "Users" 
-- WHERE "RoleId" IS NULL AND "IsDeleted" = false;
