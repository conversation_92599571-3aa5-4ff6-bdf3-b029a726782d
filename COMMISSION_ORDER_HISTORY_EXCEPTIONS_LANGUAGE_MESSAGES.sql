-- ===============================================
-- COMMISSION ORDER HISTORY EXCEPTIONS LANGUAGE MESSAGES
-- ===============================================
-- Generated: 2025-06-19
-- Total Messages: 8 (4 EN + 4 VI)
-- Pattern: ExceptionName_parameterName (EN/VI)
-- ===============================================

-- 1. CommissionOrderHistoryNotFoundException
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('CommissionOrderHistoryNotFoundException_missingIds', 'Commission order history records not found: {0}'),
('CommissionOrderHistoryNotFoundException_missingIds_vi', 'Không tìm thấy bản ghi lịch sử hoa hồng: {0}');

-- 2. CommissionOrderHistoryAlreadyPaidException  
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('CommissionOrderHistoryAlreadyPaidException_paidRecords', 'Cannot process payment for commission order histories that are already paid: {0}'),
('CommissionOrderHistoryAlreadyPaidException_paidRecords_vi', 'Không thể xử lý thanh toán cho các bản ghi hoa hồng đã được thanh toán: {0}');

-- 3. NoPendingCommissionOrderHistoryException
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('NoPendingCommissionOrderHistoryException', 'No pending commission order history records found to process payment'),
('NoPendingCommissionOrderHistoryException_vi', 'Không tìm thấy bản ghi hoa hồng chờ thanh toán nào để xử lý');

-- 4. CommissionOrderHistoryPaymentSaveFailedException
INSERT INTO "LanguageMessages" ("Key", "Message") VALUES
('CommissionOrderHistoryPaymentSaveFailedException', 'Failed to save commission order history payment confirmations to database'),
('CommissionOrderHistoryPaymentSaveFailedException_vi', 'Lỗi lưu xác nhận thanh toán hoa hồng vào cơ sở dữ liệu');

-- ===============================================
-- EXECUTION SUMMARY
-- ===============================================
-- Total Rows Inserted: 8
-- English Messages: 4
-- Vietnamese Messages: 4
-- Pattern: Exception class name + parameter name(s)
-- Usage: ExceptionMessageHelper.GetMessage(key, parameters)
-- ===============================================
